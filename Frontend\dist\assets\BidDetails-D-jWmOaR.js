import{ac as E,a as F,b as C,r as d,cj as f,j as e,a3 as I,aM as L,aN as p}from"./index-BpICMq6M.js";import{S as r}from"./SellerLayout-DgpLyAbq.js";import{T as U}from"./Table-JO9PSQsL.js";import{L as w}from"./LoadingSkeleton-B7J37_0t.js";import{a as P}from"./dateValidation-YzamylQ0.js";/* empty css                        */import"./timezoneUtils-Dky5bF8c.js";const J=()=>{var c,o,m,_,h,x,u,N,j,D;const{id:a}=E(),t=F(),{bid:s,isLoading:b,isError:v,error:l}=C(i=>i.bid),[g,y]=d.useState([]);d.useEffect(()=>{a&&t(f(a))},[t,a]),d.useEffect(()=>{var i,n,B;if(s){const k=[{bidId:s._id,customer:`${((i=s.bidder)==null?void 0:i.firstName)||""} ${((n=s.bidder)==null?void 0:n.lastName)||""}`.trim()||"Unknown",date:new Date(s.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),price:(B=s.content)!=null&&B.price?`$${parseFloat(s.content.price).toFixed(2)}`:"N/A",bidAmount:`$${parseFloat(s.amount).toFixed(2)}`,status:s.status||"Pending"}];y(k)}},[s]);const A=[{key:"bidId",label:"Bid Id"},{key:"customer",label:"Customer"},{key:"date",label:"Date"},{key:"price",label:"Price"},{key:"bidAmount",label:"Bid Amount"},{key:"status",label:"Status",render:i=>e.jsx("span",{className:`status-badge status-${i.status.toLowerCase().replace(" ","-")}`,children:i.status})}];if(b)return e.jsx(r,{children:e.jsx("div",{className:"BidDetails",children:e.jsx(w,{type:"table",rows:1})})});if(v||!s)return e.jsx(r,{children:e.jsx("div",{className:"BidDetails",children:e.jsx(I,{title:"Error Loading Bid Details",message:(l==null?void 0:l.message)||"Failed to load bid details",onRetry:()=>t(f(a))})})});const S=i=>P(i),$=i=>new Date(i).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit",hour12:!0});return e.jsx(r,{children:e.jsx("div",{className:"BidDetails",children:e.jsxs("div",{className:"BidDetails__content",children:[e.jsxs("div",{className:"BidDetails__main-section",children:[e.jsx("div",{className:"BidDetails__header",children:e.jsxs("div",{className:"BidDetails__content-info",children:[e.jsx("img",{src:(c=s.content)!=null&&c.thumbnailUrl?L(s.content.thumbnailUrl):p(200,120,"No image"),alt:((o=s.content)==null?void 0:o.title)||"Content",className:"BidDetails__content-image",onError:i=>{i.target.src=p(200,120,"Image not found")}}),e.jsxs("div",{className:"BidDetails__content-details",children:[e.jsx("h3",{className:"BidDetails__content-title",children:((m=s.content)==null?void 0:m.title)||"Untitled Content"}),e.jsx("p",{className:"BidDetails__content-subtitle",children:((_=s.content)==null?void 0:_.sport)||"Sports Content"})]})]})}),e.jsx("div",{className:"BidDetails__info-grid",children:e.jsxs("div",{className:"BidDetails__info-section",children:[e.jsxs("div",{className:"BidDetails__info-item-grid",children:[e.jsx("h3",{className:"BidDetails__section-title",children:"Bid Information"}),e.jsxs("div",{children:[e.jsxs("div",{className:"BidDetails__info-item",children:[e.jsx("span",{className:"BidDetails__info-label",children:"Bid Id"}),e.jsxs("span",{className:"BidDetails__info-value",children:["#",(h=s._id)==null?void 0:h.substring(0,8)]})]}),e.jsxs("div",{className:"BidDetails__info-item",children:[e.jsx("span",{className:"BidDetails__info-label",children:"Date"}),e.jsxs("span",{className:"BidDetails__info-value",children:[S(s.createdAt)," | ",$(s.createdAt)]})]}),e.jsx("div",{className:"vertical-line"})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"BidDetails__info-item",children:[e.jsx("span",{className:"BidDetails__info-label",children:"Price"}),e.jsx("span",{className:"BidDetails__info-value",children:(x=s.content)!=null&&x.price?`$${parseFloat(s.content.price).toFixed(2)}`:"N/A"})]}),e.jsxs("div",{className:"BidDetails__info-item",children:[e.jsx("span",{className:"BidDetails__info-label",children:"Bid Amount"}),e.jsxs("span",{className:"BidDetails__info-value",children:["$",parseFloat(s.amount).toFixed(2)]})]})]})]}),e.jsx("div",{className:"vertical-line"}),e.jsxs("div",{className:"BidDetails__info-item-grid",children:[e.jsx("h3",{className:"BidDetails__section-title",children:"Customer Details"}),e.jsxs("div",{className:"BidDetails__info-item",children:[e.jsx("span",{className:"BidDetails__info-label",children:"Name"}),e.jsx("span",{className:"BidDetails__info-value",children:`${((u=s.bidder)==null?void 0:u.firstName)||""} ${((N=s.bidder)==null?void 0:N.lastName)||""}`.trim()||"Unknown Customer"})]}),e.jsxs("div",{className:"BidDetails__info-item",children:[e.jsx("span",{className:"BidDetails__info-label",children:"Email Address"}),e.jsx("span",{className:"BidDetails__info-value",children:((j=s.bidder)==null?void 0:j.email)||"N/A"})]}),e.jsxs("div",{className:"BidDetails__info-item",children:[e.jsx("span",{className:"BidDetails__info-label",children:"Phone Number"}),e.jsx("span",{className:"BidDetails__info-value",children:((D=s.bidder)==null?void 0:D.phone)||"N/A"})]})]})]})})]}),e.jsxs("div",{className:"BidDetails__history-section",children:[e.jsx("h3",{className:"BidDetails__section-title",children:"Bid History"}),e.jsx(U,{columns:A,data:g,className:"BidDetails__history-table"})]})]})})})};export{J as default};

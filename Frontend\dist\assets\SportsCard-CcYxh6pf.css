.sports-card-component{position:relative;border-radius:var(--border-radius-large);overflow:hidden;cursor:pointer;width:100%;height:300px;box-shadow:var(--box-shadow-light);transition:all .3s ease;scroll-snap-align:start}.sports-card-component .sports-card-image{width:100%;height:100%;overflow:hidden}.sports-card-component .sports-card-image img{width:100%;height:100%;object-fit:cover;transition:transform .3s ease}.sports-card-component:hover .sports-card-image img{transform:scale(1.05)}.sports-card-component .sports-card-name{position:absolute;bottom:0;left:0;width:100%;padding:20px;font-size:var(--heading6);font-weight:600;color:var(--white);z-index:2}.sports-card-component:before{content:"";position:absolute;bottom:0;left:0;width:100%;height:100%;background:linear-gradient(to top,black 10%,transparent 100%);opacity:0;transition:opacity .3s ease;z-index:1;opacity:.8}.sports-card-component:hover:before{opacity:1;background:linear-gradient(to top,var(--btn-color) 0%,transparent 100%)}.sports-card-component:hover{transform:translateY(-4px);cursor:pointer}@media (max-width: 768px){.sports-card-component{min-width:150px;height:250px}.sports-card-component .sports-card-name{font-size:var(--smallfont)}}@media (max-width: 480px){.sports-card-component{min-width:130px;height:175px}.sports-card-component .sports-card-overlay{padding:15px 10px 10px}}

import{r as l,j as e,N as p,c7 as L,c8 as N,a8 as P}from"./index-BpICMq6M.js";import{D as R}from"./DocumentViewer-Ce_lxYx5.js";const M=({isOpen:o,onClose:i,fileUrl:t,fileName:u,fileType:w,title:h,contentType:a,showDownload:m=!1,onDownload:x=null})=>{const[c,_]=l.useState(!1),[S,F]=l.useState(!1),[j,E]=l.useState(null),[b,d]=l.useState(!0),s=l.useRef(null),g=l.useRef(null),f=(()=>{var n;if(a){if(a.toLowerCase().includes("video"))return"video";if(a.toLowerCase().includes("pdf")||a.toLowerCase().includes("document"))return"pdf"}if(w){const r=w.toLowerCase();if(["video","mp4","avi","mov","wmv","flv","webm","mkv"].includes(r))return"video";if(["pdf","document"].includes(r))return"pdf"}if(t){const r=(n=t.split(".").pop())==null?void 0:n.toLowerCase();if(["mp4","avi","mov","wmv","flv","webm","mkv"].includes(r))return"video";if(r==="pdf")return"pdf"}return"unknown"})(),k=async()=>{if(s.current)try{c?document.exitFullscreen?await document.exitFullscreen():document.webkitExitFullscreen?await document.webkitExitFullscreen():document.mozCancelFullScreen?await document.mozCancelFullScreen():document.msExitFullscreen&&await document.msExitFullscreen():s.current.requestFullscreen?await s.current.requestFullscreen():s.current.webkitRequestFullscreen?await s.current.webkitRequestFullscreen():s.current.mozRequestFullScreen?await s.current.mozRequestFullScreen():s.current.msRequestFullscreen&&await s.current.msRequestFullscreen()}catch(n){console.error("Fullscreen toggle failed:",n),_(!c)}};l.useEffect(()=>{const n=()=>{const r=!!(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement);_(r)};return document.addEventListener("fullscreenchange",n),document.addEventListener("webkitfullscreenchange",n),document.addEventListener("mozfullscreenchange",n),document.addEventListener("MSFullscreenChange",n),()=>{document.removeEventListener("fullscreenchange",n),document.removeEventListener("webkitfullscreenchange",n),document.removeEventListener("mozfullscreenchange",n),document.removeEventListener("MSFullscreenChange",n)}},[]),l.useEffect(()=>{const n=r=>{r.key==="Escape"&&o&&i()};return o&&(document.addEventListener("keydown",n),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",n),document.body.style.overflow="unset"}},[o,i]);const v=()=>{if(x)x();else if(t){const n=document.createElement("a");n.href=t,n.download=u||"download",document.body.appendChild(n),n.click(),document.body.removeChild(n)}},y=n=>{n.target===n.currentTarget&&i()},C=()=>{switch(f){case"video":return e.jsx("div",{className:"fullscreen-preview__video-container",children:e.jsxs("video",{ref:g,className:"fullscreen-preview__video",controls:!0,autoPlay:!1,controlsList:"nodownload noremoteplayback",disablePictureInPicture:!0,onPlay:()=>F(!0),onPause:()=>F(!1),onLoadStart:()=>d(!0),onLoadedData:()=>d(!1),onError:n=>{console.error("Video error:",n),E("Failed to load video"),d(!1)},children:[e.jsx("source",{src:t,type:"video/mp4"}),"Your browser does not support the video tag."]})});case"pdf":return e.jsx("div",{className:"fullscreen-preview__pdf-container",children:e.jsx(R,{fileUrl:t,fileName:u,title:h,className:"fullscreen-preview__pdf-viewer",height:"100%",showDownload:!1,onError:()=>{E("Failed to load PDF"),d(!1)}})});default:return e.jsx("div",{className:"fullscreen-preview__unsupported",children:e.jsxs("div",{className:"fullscreen-preview__unsupported-content",children:[e.jsx("h3",{children:"Preview Not Available"}),e.jsx("p",{children:"This file type is not supported for preview."}),m&&e.jsxs("button",{className:"fullscreen-preview__download-btn",onClick:v,children:[e.jsx(p,{})," Download File"]})]})})}};return o?e.jsx("div",{className:`fullscreen-preview-overlay ${c?"fullscreen-preview-overlay--fullscreen":""}`,onClick:y,ref:s,children:e.jsxs("div",{className:`fullscreen-preview ${c?"fullscreen-preview--fullscreen":""}`,children:[e.jsxs("div",{className:"fullscreen-preview__header",children:[e.jsxs("div",{className:"fullscreen-preview__title",children:[e.jsx("h3",{children:h||u||"Preview"}),e.jsxs("span",{className:"fullscreen-preview__type",children:[f.toUpperCase()," PREVIEW"]})]}),e.jsxs("div",{className:"fullscreen-preview__controls",children:[m&&e.jsx("button",{className:"fullscreen-preview__control-btn",onClick:v,title:"Download",children:e.jsx(p,{})}),e.jsx("button",{className:"fullscreen-preview__control-btn",onClick:k,title:c?"Exit Fullscreen":"Enter Fullscreen",children:c?e.jsx(L,{}):e.jsx(N,{})}),e.jsx("button",{className:"fullscreen-preview__control-btn fullscreen-preview__close-btn",onClick:i,title:"Close Preview",children:e.jsx(P,{})})]})]}),e.jsxs("div",{className:"fullscreen-preview__content",children:[b&&e.jsxs("div",{className:"fullscreen-preview__loading",children:[e.jsx("div",{className:"fullscreen-preview__spinner"}),e.jsxs("p",{children:["Loading ",f,"..."]})]}),j?e.jsxs("div",{className:"fullscreen-preview__error",children:[e.jsx("h3",{children:"Error Loading Content"}),e.jsx("p",{children:j}),m&&e.jsxs("button",{className:"fullscreen-preview__download-btn",onClick:v,children:[e.jsx(p,{})," Download File"]})]}):C()]}),e.jsx("div",{className:"fullscreen-preview__footer",children:e.jsxs("div",{className:"fullscreen-preview__info",children:[e.jsxs("span",{children:["File: ",u||"Unknown"]}),e.jsxs("span",{children:["Type: ",f.toUpperCase()]})]})})]})}):null};export{M as F};

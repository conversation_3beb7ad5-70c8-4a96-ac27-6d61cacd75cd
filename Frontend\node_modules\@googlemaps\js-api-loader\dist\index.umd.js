!function(r,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t(((r="undefined"!=typeof globalThis?globalThis:r||self).google=r.google||{},r.google.maps=r.google.maps||{},r.google.maps.plugins=r.google.maps.plugins||{},r.google.maps.plugins.loader={}))}(this,function(r){"use strict";var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(r){return r&&r.__esModule&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r}var e,i,o={};function u(){if(i)return e;i=1;var r=function(r){return r&&r.Math===Math&&r};return e=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof t&&t)||r("object"==typeof e&&e)||function(){return this}()||Function("return this")()}var a,c,f,s,l,h,p,v,d={};function g(){return c?a:(c=1,a=function(r){try{return!!r()}catch(r){return!0}})}function y(){if(s)return f;s=1;var r=g();return f=!r(function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})}function b(){if(h)return l;h=1;var r=g();return l=!r(function(){var r=function(){}.bind();return"function"!=typeof r||r.hasOwnProperty("prototype")})}function m(){if(v)return p;v=1;var r=b(),t=Function.prototype.call;return p=r?t.bind(t):function(){return t.apply(t,arguments)},p}var w,S,O,I,E,j,P,T,R,x,L,k,A,_,C,F,M,D,N,U,$,G,z,K,H,W,B,Z,J,Y,q,V,X,Q,rr,tr,nr,er,ir,or,ur,ar={};function cr(){return O?S:(O=1,S=function(r,t){return{enumerable:!(1&r),configurable:!(2&r),writable:!(4&r),value:t}})}function fr(){if(E)return I;E=1;var r=b(),t=Function.prototype,n=t.call,e=r&&t.bind.bind(n,n);return I=r?e:function(r){return function(){return n.apply(r,arguments)}},I}function sr(){if(P)return j;P=1;var r=fr(),t=r({}.toString),n=r("".slice);return j=function(r){return n(t(r),8,-1)}}function lr(){return L?x:(L=1,x=function(r){return null==r})}function hr(){if(A)return k;A=1;var r=lr(),t=TypeError;return k=function(n){if(r(n))throw new t("Can't call method on "+n);return n}}function pr(){if(C)return _;C=1;var r=function(){if(R)return T;R=1;var r=fr(),t=g(),n=sr(),e=Object,i=r("".split);return T=t(function(){return!e("z").propertyIsEnumerable(0)})?function(r){return"String"===n(r)?i(r,""):e(r)}:e}(),t=hr();return _=function(n){return r(t(n))}}function vr(){if(M)return F;M=1;var r="object"==typeof document&&document.all;return F=void 0===r&&void 0!==r?function(t){return"function"==typeof t||t===r}:function(r){return"function"==typeof r}}function dr(){if(N)return D;N=1;var r=vr();return D=function(t){return"object"==typeof t?null!==t:r(t)}}function gr(){if($)return U;$=1;var r=u(),t=vr();return U=function(n,e){return arguments.length<2?(i=r[n],t(i)?i:void 0):r[n]&&r[n][e];var i},U}function yr(){if(z)return G;z=1;var r=fr();return G=r({}.isPrototypeOf)}function br(){if(B)return W;B=1;var r,t,n=u(),e=function(){if(H)return K;H=1;var r=u().navigator,t=r&&r.userAgent;return K=t?String(t):""}(),i=n.process,o=n.Deno,a=i&&i.versions||o&&o.version,c=a&&a.v8;return c&&(t=(r=c.split("."))[0]>0&&r[0]<4?1:+(r[0]+r[1])),!t&&e&&(!(r=e.match(/Edge\/(\d+)/))||r[1]>=74)&&(r=e.match(/Chrome\/(\d+)/))&&(t=+r[1]),W=t}function mr(){if(J)return Z;J=1;var r=br(),t=g(),n=u().String;return Z=!!Object.getOwnPropertySymbols&&!t(function(){var t=Symbol("symbol detection");return!n(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&r&&r<41})}function wr(){if(q)return Y;q=1;var r=mr();return Y=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator}function Sr(){if(X)return V;X=1;var r=gr(),t=vr(),n=yr(),e=wr(),i=Object;return V=e?function(r){return"symbol"==typeof r}:function(e){var o=r("Symbol");return t(o)&&n(o.prototype,i(e))}}function Or(){if(rr)return Q;rr=1;var r=String;return Q=function(t){try{return r(t)}catch(r){return"Object"}}}function Ir(){if(nr)return tr;nr=1;var r=vr(),t=Or(),n=TypeError;return tr=function(e){if(r(e))return e;throw new n(t(e)+" is not a function")}}function Er(){if(ir)return er;ir=1;var r=Ir(),t=lr();return er=function(n,e){var i=n[e];return t(i)?void 0:r(i)}}function jr(){if(ur)return or;ur=1;var r=m(),t=vr(),n=dr(),e=TypeError;return or=function(i,o){var u,a;if("string"===o&&t(u=i.toString)&&!n(a=r(u,i)))return a;if(t(u=i.valueOf)&&!n(a=r(u,i)))return a;if("string"!==o&&t(u=i.toString)&&!n(a=r(u,i)))return a;throw new e("Can't convert object to primitive value")}}var Pr,Tr,Rr,xr,Lr,kr,Ar,_r,Cr,Fr,Mr,Dr,Nr,Ur,$r,Gr,zr,Kr,Hr,Wr,Br,Zr,Jr,Yr,qr={exports:{}};function Vr(){return Tr?Pr:(Tr=1,Pr=!1)}function Xr(){if(xr)return Rr;xr=1;var r=u(),t=Object.defineProperty;return Rr=function(n,e){try{t(r,n,{value:e,configurable:!0,writable:!0})}catch(t){r[n]=e}return e}}function Qr(){if(Lr)return qr.exports;Lr=1;var r=Vr(),t=u(),n=Xr(),e="__core-js_shared__",i=qr.exports=t[e]||n(e,{});return(i.versions||(i.versions=[])).push({version:"3.43.0",mode:r?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.43.0/LICENSE",source:"https://github.com/zloirock/core-js"}),qr.exports}function rt(){if(Ar)return kr;Ar=1;var r=Qr();return kr=function(t,n){return r[t]||(r[t]=n||{})}}function tt(){if(Cr)return _r;Cr=1;var r=hr(),t=Object;return _r=function(n){return t(r(n))}}function nt(){if(Mr)return Fr;Mr=1;var r=fr(),t=tt(),n=r({}.hasOwnProperty);return Fr=Object.hasOwn||function(r,e){return n(t(r),e)}}function et(){if(Nr)return Dr;Nr=1;var r=fr(),t=0,n=Math.random(),e=r(1.1.toString);return Dr=function(r){return"Symbol("+(void 0===r?"":r)+")_"+e(++t+n,36)}}function it(){if($r)return Ur;$r=1;var r=u(),t=rt(),n=nt(),e=et(),i=mr(),o=wr(),a=r.Symbol,c=t("wks"),f=o?a.for||a:a&&a.withoutSetter||e;return Ur=function(r){return n(c,r)||(c[r]=i&&n(a,r)?a[r]:f("Symbol."+r)),c[r]}}function ot(){if(zr)return Gr;zr=1;var r=m(),t=dr(),n=Sr(),e=Er(),i=jr(),o=it(),u=TypeError,a=o("toPrimitive");return Gr=function(o,c){if(!t(o)||n(o))return o;var f,s=e(o,a);if(s){if(void 0===c&&(c="default"),f=r(s,o,c),!t(f)||n(f))return f;throw new u("Can't convert object to primitive value")}return void 0===c&&(c="number"),i(o,c)}}function ut(){if(Hr)return Kr;Hr=1;var r=ot(),t=Sr();return Kr=function(n){var e=r(n,"string");return t(e)?e:e+""}}function at(){if(Br)return Wr;Br=1;var r=u(),t=dr(),n=r.document,e=t(n)&&t(n.createElement);return Wr=function(r){return e?n.createElement(r):{}}}function ct(){if(Jr)return Zr;Jr=1;var r=y(),t=g(),n=at();return Zr=!r&&!t(function(){return 7!==Object.defineProperty(n("div"),"a",{get:function(){return 7}}).a})}function ft(){if(Yr)return d;Yr=1;var r=y(),t=m(),n=function(){if(w)return ar;w=1;var r={}.propertyIsEnumerable,t=Object.getOwnPropertyDescriptor,n=t&&!r.call({1:2},1);return ar.f=n?function(r){var n=t(this,r);return!!n&&n.enumerable}:r,ar}(),e=cr(),i=pr(),o=ut(),u=nt(),a=ct(),c=Object.getOwnPropertyDescriptor;return d.f=r?c:function(r,f){if(r=i(r),f=o(f),a)try{return c(r,f)}catch(r){}if(u(r,f))return e(!t(n.f,r,f),r[f])},d}var st,lt,ht,pt,vt,dt,gt,yt={};function bt(){if(lt)return st;lt=1;var r=y(),t=g();return st=r&&t(function(){return 42!==Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype})}function mt(){if(pt)return ht;pt=1;var r=dr(),t=String,n=TypeError;return ht=function(e){if(r(e))return e;throw new n(t(e)+" is not an object")}}function wt(){if(vt)return yt;vt=1;var r=y(),t=ct(),n=bt(),e=mt(),i=ut(),o=TypeError,u=Object.defineProperty,a=Object.getOwnPropertyDescriptor,c="enumerable",f="configurable",s="writable";return yt.f=r?n?function(r,t,n){if(e(r),t=i(t),e(n),"function"==typeof r&&"prototype"===t&&"value"in n&&s in n&&!n[s]){var o=a(r,t);o&&o[s]&&(r[t]=n.value,n={configurable:f in n?n[f]:o[f],enumerable:c in n?n[c]:o[c],writable:!1})}return u(r,t,n)}:u:function(r,n,a){if(e(r),n=i(n),e(a),t)try{return u(r,n,a)}catch(r){}if("get"in a||"set"in a)throw new o("Accessors not supported");return"value"in a&&(r[n]=a.value),r},yt}function St(){if(gt)return dt;gt=1;var r=y(),t=wt(),n=cr();return dt=r?function(r,e,i){return t.f(r,e,n(1,i))}:function(r,t,n){return r[t]=n,r}}var Ot,It,Et,jt,Pt,Tt,Rt,xt,Lt,kt,At,_t,Ct,Ft,Mt,Dt={exports:{}};function Nt(){if(jt)return Et;jt=1;var r=fr(),t=vr(),n=Qr(),e=r(Function.toString);return t(n.inspectSource)||(n.inspectSource=function(r){return e(r)}),Et=n.inspectSource}function Ut(){if(xt)return Rt;xt=1;var r=rt(),t=et(),n=r("keys");return Rt=function(r){return n[r]||(n[r]=t(r))}}function $t(){return kt?Lt:(kt=1,Lt={})}function Gt(){if(_t)return At;_t=1;var r,t,n,e=function(){if(Tt)return Pt;Tt=1;var r=u(),t=vr(),n=r.WeakMap;return Pt=t(n)&&/native code/.test(String(n))}(),i=u(),o=dr(),a=St(),c=nt(),f=Qr(),s=Ut(),l=$t(),h="Object already initialized",p=i.TypeError,v=i.WeakMap;if(e||f.state){var d=f.state||(f.state=new v);d.get=d.get,d.has=d.has,d.set=d.set,r=function(r,t){if(d.has(r))throw new p(h);return t.facade=r,d.set(r,t),t},t=function(r){return d.get(r)||{}},n=function(r){return d.has(r)}}else{var g=s("state");l[g]=!0,r=function(r,t){if(c(r,g))throw new p(h);return t.facade=r,a(r,g,t),t},t=function(r){return c(r,g)?r[g]:{}},n=function(r){return c(r,g)}}return At={set:r,get:t,has:n,enforce:function(e){return n(e)?t(e):r(e,{})},getterFor:function(r){return function(n){var e;if(!o(n)||(e=t(n)).type!==r)throw new p("Incompatible receiver, "+r+" required");return e}}}}function zt(){if(Ct)return Dt.exports;Ct=1;var r=fr(),t=g(),n=vr(),e=nt(),i=y(),o=function(){if(It)return Ot;It=1;var r=y(),t=nt(),n=Function.prototype,e=r&&Object.getOwnPropertyDescriptor,i=t(n,"name"),o=i&&"something"===function(){}.name,u=i&&(!r||r&&e(n,"name").configurable);return Ot={EXISTS:i,PROPER:o,CONFIGURABLE:u}}().CONFIGURABLE,u=Nt(),a=Gt(),c=a.enforce,f=a.get,s=String,l=Object.defineProperty,h=r("".slice),p=r("".replace),v=r([].join),d=i&&!t(function(){return 8!==l(function(){},"length",{value:8}).length}),b=String(String).split("String"),m=Dt.exports=function(r,t,n){"Symbol("===h(s(t),0,7)&&(t="["+p(s(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),n&&n.getter&&(t="get "+t),n&&n.setter&&(t="set "+t),(!e(r,"name")||o&&r.name!==t)&&(i?l(r,"name",{value:t,configurable:!0}):r.name=t),d&&n&&e(n,"arity")&&r.length!==n.arity&&l(r,"length",{value:n.arity});try{n&&e(n,"constructor")&&n.constructor?i&&l(r,"prototype",{writable:!1}):r.prototype&&(r.prototype=void 0)}catch(r){}var u=c(r);return e(u,"source")||(u.source=v(b,"string"==typeof t?t:"")),r};return Function.prototype.toString=m(function(){return n(this)&&f(this).source||u(this)},"toString"),Dt.exports}function Kt(){if(Mt)return Ft;Mt=1;var r=vr(),t=wt(),n=zt(),e=Xr();return Ft=function(i,o,u,a){a||(a={});var c=a.enumerable,f=void 0!==a.name?a.name:o;if(r(u)&&n(u,f,a),a.global)c?i[o]=u:e(o,u);else{try{a.unsafe?i[o]&&(c=!0):delete i[o]}catch(r){}c?i[o]=u:t.f(i,o,{value:u,enumerable:!1,configurable:!a.nonConfigurable,writable:!a.nonWritable})}return i}}var Ht,Wt,Bt,Zt,Jt,Yt,qt,Vt,Xt,Qt,rn,tn,nn,en,on,un,an,cn={};function fn(){if(Zt)return Bt;Zt=1;var r=function(){if(Wt)return Ht;Wt=1;var r=Math.ceil,t=Math.floor;return Ht=Math.trunc||function(n){var e=+n;return(e>0?t:r)(e)}}();return Bt=function(t){var n=+t;return n!=n||0===n?0:r(n)}}function sn(){if(Yt)return Jt;Yt=1;var r=fn(),t=Math.max,n=Math.min;return Jt=function(e,i){var o=r(e);return o<0?t(o+i,0):n(o,i)}}function ln(){if(Vt)return qt;Vt=1;var r=fn(),t=Math.min;return qt=function(n){var e=r(n);return e>0?t(e,9007199254740991):0}}function hn(){if(Qt)return Xt;Qt=1;var r=ln();return Xt=function(t){return r(t.length)}}function pn(){if(en)return nn;en=1;var r=fr(),t=nt(),n=pr(),e=function(){if(tn)return rn;tn=1;var r=pr(),t=sn(),n=hn(),e=function(e){return function(i,o,u){var a=r(i),c=n(a);if(0===c)return!e&&-1;var f,s=t(u,c);if(e&&o!=o){for(;c>s;)if((f=a[s++])!=f)return!0}else for(;c>s;s++)if((e||s in a)&&a[s]===o)return e||s||0;return!e&&-1}};return rn={includes:e(!0),indexOf:e(!1)}}().indexOf,i=$t(),o=r([].push);return nn=function(r,u){var a,c=n(r),f=0,s=[];for(a in c)!t(i,a)&&t(c,a)&&o(s,a);for(;u.length>f;)t(c,a=u[f++])&&(~e(s,a)||o(s,a));return s}}function vn(){return un?on:(un=1,on=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"])}var dn,gn,yn,bn,mn,wn,Sn,On,In,En,jn,Pn,Tn,Rn,xn,Ln,kn,An,_n,Cn={};function Fn(){if(yn)return gn;yn=1;var r=gr(),t=fr(),n=function(){if(an)return cn;an=1;var r=pn(),t=vn().concat("length","prototype");return cn.f=Object.getOwnPropertyNames||function(n){return r(n,t)},cn}(),e=(dn||(dn=1,Cn.f=Object.getOwnPropertySymbols),Cn),i=mt(),o=t([].concat);return gn=r("Reflect","ownKeys")||function(r){var t=n.f(i(r)),u=e.f;return u?o(t,u(r)):t}}function Mn(){if(mn)return bn;mn=1;var r=nt(),t=Fn(),n=ft(),e=wt();return bn=function(i,o,u){for(var a=t(o),c=e.f,f=n.f,s=0;s<a.length;s++){var l=a[s];r(i,l)||u&&r(u,l)||c(i,l,f(o,l))}}}function Dn(){if(In)return On;In=1;var r=u(),t=ft().f,n=St(),e=Kt(),i=Xr(),o=Mn(),a=function(){if(Sn)return wn;Sn=1;var r=g(),t=vr(),n=/#|\.prototype\./,e=function(n,e){var c=o[i(n)];return c===a||c!==u&&(t(e)?r(e):!!e)},i=e.normalize=function(r){return String(r).replace(n,".").toLowerCase()},o=e.data={},u=e.NATIVE="N",a=e.POLYFILL="P";return wn=e}();return On=function(u,c){var f,s,l,h,p,v=u.target,d=u.global,g=u.stat;if(f=d?r:g?r[v]||i(v,{}):r[v]&&r[v].prototype)for(s in c){if(h=c[s],l=u.dontCallGetSet?(p=t(f,s))&&p.value:f[s],!a(d?s:v+(g?".":"#")+s,u.forced)&&void 0!==l){if(typeof h==typeof l)continue;o(h,l)}(u.sham||l&&l.sham)&&n(h,"sham",!0),e(f,s,h,u)}}}function Nn(){if(jn)return En;jn=1;var r=yr(),t=TypeError;return En=function(n,e){if(r(e,n))return n;throw new t("Incorrect invocation")}}function Un(){if(xn)return Rn;xn=1;var r=nt(),t=vr(),n=tt(),e=Ut(),i=function(){if(Tn)return Pn;Tn=1;var r=g();return Pn=!r(function(){function r(){}return r.prototype.constructor=null,Object.getPrototypeOf(new r)!==r.prototype})}(),o=e("IE_PROTO"),u=Object,a=u.prototype;return Rn=i?u.getPrototypeOf:function(e){var i=n(e);if(r(i,o))return i[o];var c=i.constructor;return t(c)&&i instanceof c?c.prototype:i instanceof u?a:null}}function $n(){if(kn)return Ln;kn=1;var r=zt(),t=wt();return Ln=function(n,e,i){return i.get&&r(i.get,e,{getter:!0}),i.set&&r(i.set,e,{setter:!0}),t.f(n,e,i)}}function Gn(){if(_n)return An;_n=1;var r=y(),t=wt(),n=cr();return An=function(e,i,o){r?t.f(e,i,n(0,o)):e[i]=o}}var zn,Kn,Hn,Wn,Bn,Zn,Jn,Yn,qn,Vn,Xn,Qn={};function re(){if(Kn)return zn;Kn=1;var r=pn(),t=vn();return zn=Object.keys||function(n){return r(n,t)}}function te(){if(Bn)return Wn;Bn=1;var r=gr();return Wn=r("document","documentElement")}function ne(){if(Jn)return Zn;Jn=1;var r,t=mt(),n=function(){if(Hn)return Qn;Hn=1;var r=y(),t=bt(),n=wt(),e=mt(),i=pr(),o=re();return Qn.f=r&&!t?Object.defineProperties:function(r,t){e(r);for(var u,a=i(t),c=o(t),f=c.length,s=0;f>s;)n.f(r,u=c[s++],a[u]);return r},Qn}(),e=vn(),i=$t(),o=te(),u=at(),a=Ut(),c="prototype",f="script",s=a("IE_PROTO"),l=function(){},h=function(r){return"<"+f+">"+r+"</"+f+">"},p=function(r){r.write(h("")),r.close();var t=r.parentWindow.Object;return r=null,t},v=function(){try{r=new ActiveXObject("htmlfile")}catch(r){}var t,n,i;v="undefined"!=typeof document?document.domain&&r?p(r):(n=u("iframe"),i="java"+f+":",n.style.display="none",o.appendChild(n),n.src=String(i),(t=n.contentWindow.document).open(),t.write(h("document.F=Object")),t.close(),t.F):p(r);for(var a=e.length;a--;)delete v[c][e[a]];return v()};return i[s]=!0,Zn=Object.create||function(r,e){var i;return null!==r?(l[c]=t(r),i=new l,l[c]=null,i[s]=r):i=v(),void 0===e?i:n.f(i,e)}}function ee(){if(qn)return Yn;qn=1;var r,t,n,e=g(),i=vr(),o=dr(),u=ne(),a=Un(),c=Kt(),f=it(),s=Vr(),l=f("iterator"),h=!1;return[].keys&&("next"in(n=[].keys())?(t=a(a(n)))!==Object.prototype&&(r=t):h=!0),!o(r)||e(function(){var t={};return r[l].call(t)!==t})?r={}:s&&(r=u(r)),i(r[l])||c(r,l,function(){return this}),Yn={IteratorPrototype:r,BUGGY_SAFARI_ITERATORS:h}}Xn||(Xn=1,function(){if(Vn)return o;Vn=1;var r=Dn(),t=u(),n=Nn(),e=mt(),i=vr(),a=Un(),c=$n(),f=Gn(),s=g(),l=nt(),h=it(),p=ee().IteratorPrototype,v=y(),d=Vr(),b="constructor",m="Iterator",w=h("toStringTag"),S=TypeError,O=t[m],I=d||!i(O)||O.prototype!==p||!s(function(){O({})}),E=function(){if(n(this,p),a(this)===p)throw new S("Abstract class Iterator not directly constructable")},j=function(r,t){v?c(p,r,{configurable:!0,get:function(){return t},set:function(t){if(e(this),this===p)throw new S("You can't redefine this property");l(this,r)?this[r]=t:f(this,r,t)}}):p[r]=t};l(p,w)||j(w,m),!I&&l(p,b)&&p[b]!==Object||j(b,E),E.prototype=p,r({global:!0,constructor:!0,forced:I},{Iterator:E})}());var ie,oe,ue,ae,ce,fe,se,le,he,pe,ve,de,ge,ye,be,me,we,Se,Oe,Ie,Ee,je,Pe,Te,Re,xe,Le={};function ke(){if(ae)return ue;ae=1;var r=function(){if(oe)return ie;oe=1;var r=sr(),t=fr();return ie=function(n){if("Function"===r(n))return t(n)}}(),t=Ir(),n=b(),e=r(r.bind);return ue=function(r,i){return t(r),void 0===i?r:n?e(r,i):function(){return r.apply(i,arguments)}},ue}function Ae(){return fe?ce:(fe=1,ce={})}function _e(){if(le)return se;le=1;var r=it(),t=Ae(),n=r("iterator"),e=Array.prototype;return se=function(r){return void 0!==r&&(t.Array===r||e[n]===r)}}function Ce(){if(de)return ve;de=1;var r=function(){if(pe)return he;pe=1;var r={};return r[it()("toStringTag")]="z",he="[object z]"===String(r)}(),t=vr(),n=sr(),e=it()("toStringTag"),i=Object,o="Arguments"===n(function(){return arguments}());return ve=r?n:function(r){var u,a,c;return void 0===r?"Undefined":null===r?"Null":"string"==typeof(a=function(r,t){try{return r[t]}catch(r){}}(u=i(r),e))?a:o?n(u):"Object"===(c=n(u))&&t(u.callee)?"Arguments":c}}function Fe(){if(ye)return ge;ye=1;var r=Ce(),t=Er(),n=lr(),e=Ae(),i=it()("iterator");return ge=function(o){if(!n(o))return t(o,i)||t(o,"@@iterator")||e[r(o)]}}function Me(){if(me)return be;me=1;var r=m(),t=Ir(),n=mt(),e=Or(),i=Fe(),o=TypeError;return be=function(u,a){var c=arguments.length<2?i(u):a;if(t(c))return n(r(c,u));throw new o(e(u)+" is not iterable")},be}function De(){if(Se)return we;Se=1;var r=m(),t=mt(),n=Er();return we=function(e,i,o){var u,a;t(e);try{if(!(u=n(e,"return"))){if("throw"===i)throw o;return o}u=r(u,e)}catch(r){a=!0,u=r}if("throw"===i)throw o;if(a)throw u;return t(u),o}}function Ne(){if(Ie)return Oe;Ie=1;var r=ke(),t=m(),n=mt(),e=Or(),i=_e(),o=hn(),u=yr(),a=Me(),c=Fe(),f=De(),s=TypeError,l=function(r,t){this.stopped=r,this.result=t},h=l.prototype;return Oe=function(p,v,d){var g,y,b,m,w,S,O,I=d&&d.that,E=!(!d||!d.AS_ENTRIES),j=!(!d||!d.IS_RECORD),P=!(!d||!d.IS_ITERATOR),T=!(!d||!d.INTERRUPTED),R=r(v,I),x=function(r){return g&&f(g,"normal"),new l(!0,r)},L=function(r){return E?(n(r),T?R(r[0],r[1],x):R(r[0],r[1])):T?R(r,x):R(r)};if(j)g=p.iterator;else if(P)g=p;else{if(!(y=c(p)))throw new s(e(p)+" is not iterable");if(i(y)){for(b=0,m=o(p);m>b;b++)if((w=L(p[b]))&&u(h,w))return w;return new l(!1)}g=a(p,y)}for(S=j?p.next:g.next;!(O=t(S,g)).done;){try{w=L(O.value)}catch(r){f(g,"throw",r)}if("object"==typeof w&&w&&u(h,w))return w}return new l(!1)}}function Ue(){return je?Ee:(je=1,Ee=function(r){return{iterator:r,next:r.next,done:!1}})}function $e(){if(Te)return Pe;Te=1;var r=u();return Pe=function(t,n){var e=r.Iterator,i=e&&e.prototype,o=i&&i[t],u=!1;if(o)try{o.call({next:function(){return{done:!0}},return:function(){u=!0}},-1)}catch(r){r instanceof n||(u=!1)}if(!u)return o}}xe||(xe=1,function(){if(Re)return Le;Re=1;var r=Dn(),t=m(),n=Ne(),e=Ir(),i=mt(),o=Ue(),u=De(),a=$e()("forEach",TypeError);r({target:"Iterator",proto:!0,real:!0,forced:a},{forEach:function(r){i(this);try{e(r)}catch(r){u(this,"throw",r)}if(a)return t(a,this,r);var c=o(this),f=0;n(c,function(t){r(t,f++)},{IS_RECORD:!0})}})}());var Ge,ze,Ke,He,We,Be,Ze,Je,Ye,qe,Ve,Xe,Qe,ri,ti,ni,ei={};function ii(){if(ze)return Ge;ze=1;var r=Kt();return Ge=function(t,n,e){for(var i in n)r(t,i,n[i],e);return t}}function oi(){return He?Ke:(He=1,Ke=function(r,t){return{value:r,done:t}})}function ui(){if(Be)return We;Be=1;var r=De();return We=function(t,n,e){for(var i=t.length-1;i>=0;i--)if(void 0!==t[i])try{e=r(t[i].iterator,n,e)}catch(r){n="throw",e=r}if("throw"===n)throw e;return e}}function ai(){if(qe)return Ye;qe=1;var r=mt(),t=De();return Ye=function(n,e,i,o){try{return o?e(r(i)[0],i[1]):e(i)}catch(r){t(n,"throw",r)}}}function ci(){return Xe?Ve:(Xe=1,Ve=function(r,t){var n="function"==typeof Iterator&&Iterator.prototype[r];if(n)try{n.call({next:null},t).next()}catch(r){return!0}})}function fi(){if(Qe)return ei;Qe=1;var r=Dn(),t=m(),n=Ir(),e=mt(),i=Ue(),o=function(){if(Je)return Ze;Je=1;var r=m(),t=ne(),n=St(),e=ii(),i=it(),o=Gt(),u=Er(),a=ee().IteratorPrototype,c=oi(),f=De(),s=ui(),l=i("toStringTag"),h="IteratorHelper",p="WrapForValidIterator",v="normal",d="throw",g=o.set,y=function(n){var i=o.getterFor(n?p:h);return e(t(a),{next:function(){var r=i(this);if(n)return r.nextHandler();if(r.done)return c(void 0,!0);try{var t=r.nextHandler();return r.returnHandlerResult?t:c(t,r.done)}catch(t){throw r.done=!0,t}},return:function(){var t=i(this),e=t.iterator;if(t.done=!0,n){var o=u(e,"return");return o?r(o,e):c(void 0,!0)}if(t.inner)try{f(t.inner.iterator,v)}catch(r){return f(e,d,r)}if(t.openIters)try{s(t.openIters,v)}catch(r){return f(e,d,r)}return e&&f(e,v),c(void 0,!0)}})},b=y(!0),w=y(!1);return n(w,l,"Iterator Helper"),Ze=function(r,t,n){var e=function(e,i){i?(i.iterator=e.iterator,i.next=e.next):i=e,i.type=t?p:h,i.returnHandlerResult=!!n,i.nextHandler=r,i.counter=0,i.done=!1,g(this,i)};return e.prototype=t?b:w,e}}(),u=ai(),a=De(),c=ci(),f=$e(),s=Vr(),l=!s&&!c("map",function(){}),h=!s&&!l&&f("map",TypeError),p=s||l||h,v=o(function(){var r=this.iterator,n=e(t(this.next,r));if(!(this.done=!!n.done))return u(r,this.mapper,[n.value,this.counter++],!0)});return r({target:"Iterator",proto:!0,real:!0,forced:p},{map:function(r){e(this);try{n(r)}catch(r){a(this,"throw",r)}return h?t(h,this,r):new v(i(this),{mapper:r})}}),ei}function si(r,t,n,e){return new(n||(n=Promise))(function(i,o){function u(r){try{c(e.next(r))}catch(r){o(r)}}function a(r){try{c(e.throw(r))}catch(r){o(r)}}function c(r){var t;r.done?i(r.value):(t=r.value,t instanceof n?t:new n(function(r){r(t)})).then(u,a)}c((e=e.apply(r,t||[])).next())})}ri||(ri=1,fi()),"function"==typeof SuppressedError&&SuppressedError;var li=n(ni?ti:(ni=1,ti=function r(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;var e,i,o;if(Array.isArray(t)){if((e=t.length)!=n.length)return!1;for(i=e;0!==i--;)if(!r(t[i],n[i]))return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((e=(o=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(i=e;0!==i--;)if(!Object.prototype.hasOwnProperty.call(n,o[i]))return!1;for(i=e;0!==i--;){var u=o[i];if(!r(t[u],n[u]))return!1}return!0}return t!=t&&n!=n}));const hi="__googleMapsScriptId";var pi;r.LoaderStatus=void 0,(pi=r.LoaderStatus||(r.LoaderStatus={}))[pi.INITIALIZED=0]="INITIALIZED",pi[pi.LOADING=1]="LOADING",pi[pi.SUCCESS=2]="SUCCESS",pi[pi.FAILURE=3]="FAILURE";class vi{constructor(r){let{apiKey:t,authReferrerPolicy:n,channel:e,client:i,id:o=hi,language:u,libraries:a=[],mapIds:c,nonce:f,region:s,retries:l=3,url:h="https://maps.googleapis.com/maps/api/js",version:p}=r;if(this.callbacks=[],this.done=!1,this.loading=!1,this.errors=[],this.apiKey=t,this.authReferrerPolicy=n,this.channel=e,this.client=i,this.id=o||hi,this.language=u,this.libraries=a,this.mapIds=c,this.nonce=f,this.region=s,this.retries=l,this.url=h,this.version=p,vi.instance){if(!li(this.options,vi.instance.options))throw new Error(`Loader must not be called again with different options. ${JSON.stringify(this.options)} !== ${JSON.stringify(vi.instance.options)}`);return vi.instance}vi.instance=this}get options(){return{version:this.version,apiKey:this.apiKey,channel:this.channel,client:this.client,id:this.id,libraries:this.libraries,language:this.language,region:this.region,mapIds:this.mapIds,nonce:this.nonce,url:this.url,authReferrerPolicy:this.authReferrerPolicy}}get status(){return this.errors.length?r.LoaderStatus.FAILURE:this.done?r.LoaderStatus.SUCCESS:this.loading?r.LoaderStatus.LOADING:r.LoaderStatus.INITIALIZED}get failed(){return this.done&&!this.loading&&this.errors.length>=this.retries+1}createUrl(){let r=this.url;return r+="?callback=__googleMapsCallback&loading=async",this.apiKey&&(r+=`&key=${this.apiKey}`),this.channel&&(r+=`&channel=${this.channel}`),this.client&&(r+=`&client=${this.client}`),this.libraries.length>0&&(r+=`&libraries=${this.libraries.join(",")}`),this.language&&(r+=`&language=${this.language}`),this.region&&(r+=`&region=${this.region}`),this.version&&(r+=`&v=${this.version}`),this.mapIds&&(r+=`&map_ids=${this.mapIds.join(",")}`),this.authReferrerPolicy&&(r+=`&auth_referrer_policy=${this.authReferrerPolicy}`),r}deleteScript(){const r=document.getElementById(this.id);r&&r.remove()}load(){return this.loadPromise()}loadPromise(){return new Promise((r,t)=>{this.loadCallback(n=>{n?t(n.error):r(window.google)})})}importLibrary(r){return this.execute(),google.maps.importLibrary(r)}loadCallback(r){this.callbacks.push(r),this.execute()}setScript(){var r,t;if(document.getElementById(this.id))return void this.callback();const n={key:this.apiKey,channel:this.channel,client:this.client,libraries:this.libraries.length&&this.libraries,v:this.version,mapIds:this.mapIds,language:this.language,region:this.region,authReferrerPolicy:this.authReferrerPolicy};Object.keys(n).forEach(r=>!n[r]&&delete n[r]),(null===(t=null===(r=null===window||void 0===window?void 0:window.google)||void 0===r?void 0:r.maps)||void 0===t?void 0:t.importLibrary)||(r=>{let t,n,e,i="The Google Maps JavaScript API",o="google",u="importLibrary",a="__ib__",c=document,f=window;f=f[o]||(f[o]={});const s=f.maps||(f.maps={}),l=new Set,h=new URLSearchParams,p=()=>t||(t=new Promise((u,f)=>si(this,void 0,void 0,function*(){var p;for(e in yield n=c.createElement("script"),n.id=this.id,h.set("libraries",[...l]+""),r)h.set(e.replace(/[A-Z]/g,r=>"_"+r[0].toLowerCase()),r[e]);h.set("callback",o+".maps."+a),n.src=this.url+"?"+h,s[a]=u,n.onerror=()=>t=f(Error(i+" could not load.")),n.nonce=this.nonce||(null===(p=c.querySelector("script[nonce]"))||void 0===p?void 0:p.nonce)||"",c.head.append(n)})));s[u]?console.warn(i+" only loads once. Ignoring:",r):s[u]=function(r){for(var t=arguments.length,n=new Array(t>1?t-1:0),e=1;e<t;e++)n[e-1]=arguments[e];return l.add(r)&&p().then(()=>s[u](r,...n))}})(n);const e=this.libraries.map(r=>this.importLibrary(r));e.length||e.push(this.importLibrary("core")),Promise.all(e).then(()=>this.callback(),r=>{const t=new ErrorEvent("error",{error:r});this.loadErrorCallback(t)})}reset(){this.deleteScript(),this.done=!1,this.loading=!1,this.errors=[],this.onerrorEvent=null}resetIfRetryingFailed(){this.failed&&this.reset()}loadErrorCallback(r){if(this.errors.push(r),this.errors.length<=this.retries){const r=this.errors.length*Math.pow(2,this.errors.length);console.error(`Failed to load Google Maps script, retrying in ${r} ms.`),setTimeout(()=>{this.deleteScript(),this.setScript()},r)}else this.onerrorEvent=r,this.callback()}callback(){this.done=!0,this.loading=!1,this.callbacks.forEach(r=>{r(this.onerrorEvent)}),this.callbacks=[]}execute(){if(this.resetIfRetryingFailed(),!this.loading)if(this.done)this.callback();else{if(window.google&&window.google.maps&&window.google.maps.version)return console.warn("Google Maps already loaded outside @googlemaps/js-api-loader. This may result in undesirable behavior as options and script parameters may not match."),void this.callback();this.loading=!0,this.setScript()}}}r.DEFAULT_ID=hi,r.Loader=vi});
//# sourceMappingURL=index.umd.js.map

import React, { useState, useEffect, useRef } from "react";
import { FaTimes, FaExpand, FaCompress, FaDownload, FaChevronLeft, FaChevronRight } from "react-icons/fa";
import { MdFullscreen, MdFullscreenExit } from "react-icons/md";
import DocumentViewer from "./DocumentViewer";
import "../../styles/FullscreenPreview.css";

const FullscreenPreview = ({
  isOpen,
  onClose,
  fileUrl,
  fileName,
  fileType,
  title,
  contentType,
  showDownload = false,
  onDownload = null
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const containerRef = useRef(null);
  const videoRef = useRef(null);

  // Determine media type
  const getMediaType = () => {
    if (contentType) {
      if (contentType.toLowerCase().includes('video')) return 'video';
      if (contentType.toLowerCase().includes('pdf') || contentType.toLowerCase().includes('document')) return 'pdf';
    }
    
    if (fileType) {
      const type = fileType.toLowerCase();
      if (['video', 'mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'].includes(type)) return 'video';
      if (['pdf', 'document'].includes(type)) return 'pdf';
    }

    if (fileUrl) {
      const extension = fileUrl.split('.').pop()?.toLowerCase();
      if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'].includes(extension)) return 'video';
      if (extension === 'pdf') return 'pdf';
    }

    return 'unknown';
  };

  const mediaType = getMediaType();

  // Handle fullscreen toggle
  const toggleFullscreen = async () => {
    if (!containerRef.current) return;

    try {
      if (!isFullscreen) {
        // Enter fullscreen
        if (containerRef.current.requestFullscreen) {
          await containerRef.current.requestFullscreen();
        } else if (containerRef.current.webkitRequestFullscreen) {
          await containerRef.current.webkitRequestFullscreen();
        } else if (containerRef.current.mozRequestFullScreen) {
          await containerRef.current.mozRequestFullScreen();
        } else if (containerRef.current.msRequestFullscreen) {
          await containerRef.current.msRequestFullscreen();
        }
      } else {
        // Exit fullscreen
        if (document.exitFullscreen) {
          await document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
          await document.webkitExitFullscreen();
        } else if (document.mozCancelFullScreen) {
          await document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
          await document.msExitFullscreen();
        }
      }
    } catch (error) {
      console.error('Fullscreen toggle failed:', error);
      // Fallback to CSS fullscreen
      setIsFullscreen(!isFullscreen);
    }
  };

  // Listen for fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      );
      setIsFullscreen(isCurrentlyFullscreen);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  // Handle download
  const handleDownload = () => {
    if (onDownload) {
      onDownload();
    } else if (fileUrl) {
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = fileName || 'download';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // Handle overlay click
  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Render content based on media type
  const renderContent = () => {
    switch (mediaType) {
      case 'video':
        return (
          <div className="fullscreen-preview__video-container">
            <video
              ref={videoRef}
              className="fullscreen-preview__video"
              controls
              autoPlay={false}
              controlsList="nodownload noremoteplayback"
              disablePictureInPicture
              onPlay={() => setIsPlaying(true)}
              onPause={() => setIsPlaying(false)}
              onLoadStart={() => setIsLoading(true)}
              onLoadedData={() => setIsLoading(false)}
              onError={(e) => {
                console.error('Video error:', e);
                setError('Failed to load video');
                setIsLoading(false);
              }}
            >
              <source src={fileUrl} type="video/mp4" />
              Your browser does not support the video tag.
            </video>
          </div>
        );

      case 'pdf':
        return (
          <div className="fullscreen-preview__pdf-container">
            <DocumentViewer
              fileUrl={fileUrl}
              fileName={fileName}
              title={title}
              className="fullscreen-preview__pdf-viewer"
              height="100%"
              showDownload={false}
              onError={() => {
                setError('Failed to load PDF');
                setIsLoading(false);
              }}
            />
          </div>
        );

      default:
        return (
          <div className="fullscreen-preview__unsupported">
            <div className="fullscreen-preview__unsupported-content">
              <h3>Preview Not Available</h3>
              <p>This file type is not supported for preview.</p>
              {showDownload && (
                <button
                  className="fullscreen-preview__download-btn"
                  onClick={handleDownload}
                >
                  <FaDownload /> Download File
                </button>
              )}
            </div>
          </div>
        );
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className={`fullscreen-preview-overlay ${isFullscreen ? 'fullscreen-preview-overlay--fullscreen' : ''}`}
      onClick={handleOverlayClick}
      ref={containerRef}
    >
      <div className={`fullscreen-preview ${isFullscreen ? 'fullscreen-preview--fullscreen' : ''}`}>
        {/* Header */}
        <div className="fullscreen-preview__header">
          <div className="fullscreen-preview__title">
            <h3>{title || fileName || 'Preview'}</h3>
            <span className="fullscreen-preview__type">
              {mediaType.toUpperCase()} PREVIEW
            </span>
          </div>
          <div className="fullscreen-preview__controls">
            {showDownload && (
              <button
                className="fullscreen-preview__control-btn"
                onClick={handleDownload}
                title="Download"
              >
                <FaDownload />
              </button>
            )}
            <button
              className="fullscreen-preview__control-btn"
              onClick={toggleFullscreen}
              title={isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"}
            >
              {isFullscreen ? <MdFullscreenExit /> : <MdFullscreen />}
            </button>
            <button
              className="fullscreen-preview__control-btn fullscreen-preview__close-btn"
              onClick={onClose}
              title="Close Preview"
            >
              <FaTimes />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="fullscreen-preview__content">
          {isLoading && (
            <div className="fullscreen-preview__loading">
              <div className="fullscreen-preview__spinner"></div>
              <p>Loading {mediaType}...</p>
            </div>
          )}
          
          {error ? (
            <div className="fullscreen-preview__error">
              <h3>Error Loading Content</h3>
              <p>{error}</p>
              {showDownload && (
                <button
                  className="fullscreen-preview__download-btn"
                  onClick={handleDownload}
                >
                  <FaDownload /> Download File
                </button>
              )}
            </div>
          ) : (
            renderContent()
          )}
        </div>

        {/* Footer */}
        <div className="fullscreen-preview__footer">
          <div className="fullscreen-preview__info">
            <span>File: {fileName || 'Unknown'}</span>
            <span>Type: {mediaType.toUpperCase()}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FullscreenPreview;

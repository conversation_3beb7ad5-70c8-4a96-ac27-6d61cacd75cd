const C="data:image/svg+xml,%3csvg%20width='56'%20height='56'%20viewBox='0%200%2056%2056'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M54.0284%2027.9988C54.8932%2026.9917%2055.4993%2025.7888%2055.794%2024.4944C56.0887%2023.2%2056.0631%2021.8533%2055.7195%2020.571C55.3759%2019.2888%2054.7246%2018.1097%2053.8222%2017.1361C52.9197%2016.1626%2051.7934%2015.4239%2050.5408%2014.9842C50.7863%2013.6795%2050.7096%2012.3346%2050.3176%2011.0663C49.9256%209.79793%2049.23%208.64439%2048.2912%207.70572C47.3525%206.76706%2046.1988%206.07164%2044.9304%205.67978C43.662%205.28793%2042.3172%205.21148%2041.0125%205.45707C40.5732%204.20422%2039.8346%203.07756%2038.861%202.1749C37.8874%201.27223%2036.7082%200.620857%2035.4258%200.277318C34.1434%20-0.0662206%2032.7964%20-0.0915361%2031.502%200.203569C30.2075%200.498675%2029.0047%201.10528%2027.9979%201.97072C26.9908%201.10588%2025.7878%200.499817%2024.4935%200.205119C23.1991%20-0.0895785%2021.8523%20-0.0640135%2020.5701%200.279596C19.2878%200.623205%2018.1088%201.27448%2017.1352%202.17692C16.1616%203.07937%2015.4229%204.20573%2014.9832%205.45828C13.6786%205.21309%2012.3339%205.28988%2011.0657%205.682C9.79756%206.07412%208.64419%206.76972%207.70568%207.70847C6.76717%208.64722%206.07186%209.80077%205.68007%2011.0691C5.28827%2012.3373%205.21182%2013.6821%205.45735%2014.9866C4.20479%2015.4263%203.07843%2016.165%202.17599%2017.1386C1.27354%2018.1121%200.622264%2019.2912%200.278655%2020.5734C-0.0649546%2021.8557%20-0.0905234%2023.2024%200.204174%2024.4968C0.498872%2025.7912%201.10494%2026.9941%201.96979%2028.0012C1.10468%2029.0083%200.498423%2030.2113%200.203641%2031.5058C-0.0911404%2032.8004%20-0.0655494%2034.1472%200.27821%2035.4296C0.62197%2036.712%201.2735%2037.8911%202.17625%2038.8646C3.07901%2039.8381%204.20572%2040.5765%205.45857%2041.0159C5.21283%2042.3204%205.28919%2043.6653%205.68103%2044.9336C6.07287%2046.202%206.76836%2047.3555%207.7071%2048.2941C8.64585%2049.2328%209.79949%2049.9281%2011.0679%2050.3198C12.3363%2050.7115%2013.6811%2050.7877%2014.9857%2050.5418C15.4254%2051.7943%2016.1641%2052.9207%2017.1376%2053.8231C18.1112%2054.7256%2019.2903%2055.3768%2020.5725%2055.7204C21.8548%2056.0641%2023.2015%2056.0896%2024.4959%2055.7949C25.7902%2055.5002%2026.9932%2054.8942%2028.0003%2054.0293C29.0074%2054.8944%2030.2104%2055.5007%2031.5049%2055.7955C32.7994%2056.0902%2034.1463%2056.0646%2035.4287%2055.7209C36.7111%2055.3771%2037.8902%2054.7256%2038.8637%2053.8228C39.8371%2052.9201%2040.5756%2051.7934%2041.015%2050.5405C42.3196%2050.7862%2043.6644%2050.7098%2044.9328%2050.3179C46.2011%2049.9261%2047.3547%2049.2306%2048.2934%2048.2919C49.2321%2047.3532%2049.9276%2046.1996%2050.3195%2044.9313C50.7113%2043.6629%2050.7877%2042.3181%2050.542%2041.0135C51.7947%2040.5738%2052.9211%2039.8352%2053.8236%2038.8616C54.7261%2037.888%2055.3774%2036.7088%2055.7209%2035.4265C56.0644%2034.1441%2056.0898%2032.7973%2055.7948%2031.503C55.4999%2030.2086%2054.8935%2029.0057%2054.0284%2027.9988V27.9988Z'%20fill='url(%23paint0_linear_564_4346)'/%3e%3cpath%20d='M22.9725%2038.687L15.1916%2030.911C14.8963%2030.6152%2014.7305%2030.2143%2014.7305%2029.7964C14.7305%2029.3784%2014.8963%2028.9776%2015.1916%2028.6818L16.1369%2027.7353C16.4327%2027.44%2016.8336%2027.2741%2017.2515%2027.2741C17.6695%2027.2741%2018.0703%2027.44%2018.3661%2027.7353L24.0222%2033.3877L37.3426%2019.2324C37.6293%2018.9283%2038.0248%2018.7503%2038.4425%2018.7376C38.8603%2018.7248%2039.266%2018.8783%2039.5706%2019.1644L40.5414%2020.0806C40.8459%2020.3673%2041.0241%2020.7631%2041.0368%2021.1811C41.0496%2021.5991%2040.8958%2022.0051%2040.6093%2022.3098L25.2393%2038.6518C25.0946%2038.806%2024.9203%2038.9296%2024.7269%2039.0151C24.5335%2039.1007%2024.3248%2039.1465%2024.1134%2039.1497C23.9019%2039.153%2023.6919%2039.1138%2023.4959%2039.0343C23.2999%2038.9548%2023.1219%2038.8367%2022.9725%2038.687Z'%20fill='white'/%3e%3cdefs%3e%3clinearGradient%20id='paint0_linear_564_4346'%20x1='27.9994'%20y1='-0.000488281'%20x2='27.9994'%20y2='55.9991'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23EC1D3B'/%3e%3cstop%20offset='1'%20stop-color='%23EC1D3B'/%3e%3c/linearGradient%3e%3c/defs%3e%3c/svg%3e";export{C as T};

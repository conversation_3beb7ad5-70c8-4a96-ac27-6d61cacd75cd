import{a as z,b as J,r as p,aQ as U,t as C,c as $,j as e,cs as K,aR as E,w as Z,aS as ee,aM as le,ct as D,cu as O,bc as re,aI as ie,cv as R,A as M,y as A,aT as se,aU as ae}from"./index-BpICMq6M.js";import{S as oe}from"./SellerLayout-DgpLyAbq.js";const de=()=>{const x=z(),{user:m,isLoading:j,isSuccess:L,isError:I,error:b}=J(i=>i.auth),[l,h]=p.useState({firstName:"",lastName:"",email:"",mobile:"",profileImage:"",description:"",experiences:[],minTrainingCost:"",socialLinks:{facebook:"",instagram:"",twitter:""},isOnboardingComplete:!1}),[Y,q]=p.useState(null),[w,B]=p.useState(null),[d,F]=p.useState(!1),[N,k]=p.useState({experienceYears:{}});p.useEffect(()=>{x(U())},[x]),p.useEffect(()=>{var i,r,s,t,c,o;if(m&&m.data){const a=m.data,n=a.sellerInfo||{};h({firstName:a.firstName||"",lastName:a.lastName||"",email:a.email||"",mobile:a.mobile||"",profileImage:a.profileImage||"",description:n.description||"",experiences:n.experiences&&n.experiences.length>0?n.experiences:[{schoolName:"",position:"",fromYear:"",toYear:""}],minTrainingCost:n.minTrainingCost||"",socialLinks:{facebook:((i=n.socialLinks)==null?void 0:i.facebook)||"",instagram:((r=n.socialLinks)==null?void 0:r.instagram)||"",twitter:((s=n.socialLinks)==null?void 0:s.twitter)||""},isOnboardingComplete:n.isOnboardingComplete||!1})}else if(m){const a=m.sellerInfo||{};h({firstName:m.firstName||"",lastName:m.lastName||"",email:m.email||"",mobile:m.mobile||"",profileImage:m.profileImage||"",description:a.description||"",experiences:a.experiences&&a.experiences.length>0?a.experiences:[{schoolName:"",position:"",fromYear:"",toYear:""}],minTrainingCost:a.minTrainingCost||"",socialLinks:{facebook:((t=a.socialLinks)==null?void 0:t.facebook)||"",instagram:((c=a.socialLinks)==null?void 0:c.instagram)||"",twitter:((o=a.socialLinks)==null?void 0:o.twitter)||""},isOnboardingComplete:a.isOnboardingComplete||!1})}},[m]);const[g,P]=p.useState(!1),[V,T]=p.useState(!1);p.useEffect(()=>{g&&L&&!j&&(C.success("Profile updated successfully!"),x($()),P(!1),F(!1),x(U())),g&&I&&b&&(C.error(b.message||"Failed to update profile"),x($()),P(!1))},[L,I,b,j,x,g]);const y=()=>{const i=new Date().getFullYear(),r=1950,s=i,t={};let c=!1;return console.log("=== Seller Profile Year Validation Debug ==="),console.log("Current year:",i),console.log("Max allowed year:",s),console.log("Experiences to validate:",l.experiences),l.experiences.forEach((o,a)=>{const n=parseInt(o.fromYear),_=parseInt(o.toYear),f={};console.log(`Experience ${a}:`,{fromYearString:o.fromYear,toYearString:o.toYear,fromYearParsed:n,toYearParsed:_}),String(o.fromYear||"").trim()?(isNaN(n)||n<r||n>s)&&(f.fromYear=`From year must be between ${r} and ${s}`,c=!0,console.log(`From year error: out of range (${n})`)):(f.fromYear="From year is required",c=!0,console.log("From year error: required")),String(o.toYear||"").trim()?isNaN(_)||_<r||_>s?(f.toYear=`To year must be between ${r} and ${s}`,c=!0,console.log(`To year error: out of range (${_})`)):!isNaN(n)&&_<=n&&(f.toYear="To year must be greater than from year",c=!0,console.log("To year error: less than from year")):(f.toYear="To year is required",c=!0,console.log("To year error: required")),Object.keys(f).length>0&&(t[a]=f,console.log(`Errors for experience ${a}:`,f))}),console.log("Total year errors:",t),console.log("Has errors:",c),c?(k(o=>({...o,experienceYears:t})),!1):(k(o=>({...o,experienceYears:{}})),!0)},S=i=>{const{name:r,value:s}=i.target;if(r.startsWith("socialLinks.")){const t=r.split(".")[1];h(c=>({...c,socialLinks:{...c.socialLinks,[t]:s}}))}else h(t=>({...t,[r]:s}))},v=(i,r,s)=>{var t,c;h(o=>({...o,experiences:o.experiences.map((a,n)=>n===i?{...a,[r]:s}:a)})),(r==="fromYear"||r==="toYear")&&(c=(t=N.experienceYears)==null?void 0:t[i])!=null&&c[r]&&k(o=>({...o,experienceYears:{...o.experienceYears,[i]:{...o.experienceYears[i],[r]:""}}}))},G=()=>{h(i=>({...i,experiences:[...i.experiences,{schoolName:"",position:"",fromYear:"",toYear:""}]}))},H=i=>{l.experiences.length>1&&h(r=>({...r,experiences:r.experiences.filter((s,t)=>t!==i)}))},Q=async i=>{if(i.preventDefault(),console.log("Validating experience years before submission..."),!y()){console.log("Year validation failed, preventing submission");return}P(!0);try{let s=l.profileImage;Y&&(s=(await x(se(Y)).unwrap()).data.fileUrl);const t={firstName:l.firstName,lastName:l.lastName,profileImage:s,sellerInfo:{description:l.description,experiences:l.experiences,minTrainingCost:l.minTrainingCost,socialLinks:l.socialLinks,isOnboardingComplete:l.isOnboardingComplete}};console.log("Updating seller profile with data:",t),console.log("Profile update details:",{profileImage:s,isOnboardingComplete:l.isOnboardingComplete,newImageUploaded:!!Y}),x(ae(t))}catch(s){console.error("Profile update error:",s),C.error("Failed to upload image or update profile"),P(!1)}},W=i=>{const r=i.target.files[0];if(r){q(r),T(!1);const s=new FileReader;s.onloadend=()=>{B(s.result)},s.readAsDataURL(r)}},X=()=>{T(!0)};return e.jsx(oe,{children:e.jsxs("div",{className:"SellerProfile",children:[e.jsx("div",{className:"SellerProfile__header",children:e.jsxs("button",{className:"SellerProfile__edit-btn btn-primary",onClick:()=>F(!d),children:[e.jsx(K,{})," ",d?"Cancel Edit":"Edit Profile"]})}),e.jsxs("div",{className:"SellerProfile__container",children:[e.jsx("div",{className:"SellerProfile__left-section",children:e.jsxs("div",{className:"SellerProfile__section",children:[e.jsx("h3",{className:"SellerProfile__section-title",children:"Basic Information"}),e.jsxs("div",{className:"SellerProfile__form-row",children:[e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(E,{})}),e.jsx("input",{type:"text",name:"firstName",value:l.firstName,onChange:S,placeholder:"First Name",disabled:!d,className:`SellerProfile__input ${d?"":"SellerProfile__input--disabled"}`})]})}),e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(E,{})}),e.jsx("input",{type:"text",name:"lastName",value:l.lastName,onChange:S,placeholder:"Last Name",disabled:!d,className:`SellerProfile__input ${d?"":"SellerProfile__input--disabled"}`})]})})]}),e.jsxs("div",{className:"SellerProfile__form-row ",children:[e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(Z,{})}),e.jsx("input",{type:"email",name:"email",value:l.email,placeholder:"Email Address",disabled:!0,className:"SellerProfile__input SellerProfile__input--disabled"})]})}),e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(ee,{})}),e.jsx("input",{type:"tel",name:"mobile",value:l.mobile,placeholder:"Mobile Number",disabled:!0,className:"SellerProfile__input SellerProfile__input--disabled"})]})})]})]})}),e.jsx("div",{className:"SellerProfile__right-section",children:e.jsxs("div",{className:"SellerProfile__image-container",children:[e.jsx("h3",{className:"SellerProfile__image-title",children:"Profile Image"}),e.jsx("div",{className:"SellerProfile__image",children:w||l.profileImage&&!V?e.jsx("img",{src:w||le(l.profileImage),alt:"Profile",onError:X}):e.jsx("div",{className:"SellerProfile__placeholder",children:l.firstName&&l.lastName?`${l.firstName.charAt(0)}${l.lastName.charAt(0)}`:e.jsx(E,{className:"SellerProfile__user-icon"})})}),d&&e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"SellerProfile__upload-btn",onClick:()=>document.getElementById("profile-image-upload").click(),children:"Upload Photo"}),e.jsx("input",{type:"file",id:"profile-image-upload",accept:"image/*",onChange:W,style:{display:"none"}})]})]})})]}),e.jsxs("div",{className:"SellerProfile__description-experience-container",children:[e.jsxs("div",{className:"SellerProfile__section",children:[e.jsx("h3",{className:"SellerProfile__section-title",children:"Description"}),e.jsx("div",{className:"SellerProfile__description-container",children:d?e.jsx("textarea",{name:"description",value:l.description,onChange:S,placeholder:"Enter your professional description...",className:"SellerProfile__textarea",rows:4}):e.jsx("div",{className:"SellerProfile__description-display",children:l.description||"No description provided"})})]}),e.jsxs("div",{className:"SellerProfile__section",children:[e.jsx("h3",{className:"SellerProfile__section-title",children:"Minimum Training Cost"}),e.jsx("div",{className:"SellerProfile__cost-container",children:d?e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(D,{})}),e.jsx("input",{type:"number",name:"minTrainingCost",value:l.minTrainingCost,onChange:S,placeholder:"Enter amount",className:"SellerProfile__input"})]})}):e.jsxs("div",{className:"SellerProfile__cost-display",children:[e.jsx(D,{className:"SellerProfile__cost-icon"}),e.jsx("span",{className:"SellerProfile__cost-amount",children:l.minTrainingCost?`${l.minTrainingCost}`:"Not specified"})]})})]})]}),e.jsxs("div",{className:"SellerProfile__section",children:[e.jsx("h3",{className:"SellerProfile__section-title",children:"Experience"}),e.jsx("div",{className:"SellerProfile__experiences",children:d?e.jsxs(e.Fragment,{children:[l.experiences&&l.experiences.length>0?e.jsx("div",{className:"SellerProfile__experiences-grid",children:l.experiences.map((i,r)=>{var s,t,c,o,a,n,_,f;return e.jsxs("div",{className:"SellerProfile__experience-edit-item",children:[e.jsxs("div",{className:"SellerProfile__experience-edit-header",children:[e.jsx(O,{className:"SellerProfile__experience-icon"}),e.jsxs("span",{className:"SellerProfile__experience-number",children:["Experience ",r+1]}),l.experiences.length>1&&e.jsx("button",{type:"button",className:"SellerProfile__remove-btn",onClick:()=>H(r),children:e.jsx(re,{className:""})})]}),e.jsxs("div",{className:"SellerProfile__experience-form",children:[e.jsxs("div",{className:"SellerProfile__form-row",children:[e.jsx("div",{className:"SellerProfile__input-field",children:e.jsx("input",{type:"text",placeholder:"Enter Experience",value:i.schoolName,onChange:u=>v(r,"schoolName",u.target.value),className:"SellerProfile__input"})}),e.jsx("div",{className:"SellerProfile__input-field",children:e.jsx("input",{type:"text",placeholder:"Enter Position",value:i.position,onChange:u=>v(r,"position",u.target.value),className:"SellerProfile__input"})})]}),e.jsxs("div",{className:"SellerProfile__form-row SellerProfile__form-row-email-phone",children:[e.jsxs("div",{className:"SellerProfile__input-field",children:[e.jsx("input",{type:"text",placeholder:"From Year",value:i.fromYear,onChange:u=>v(r,"fromYear",u.target.value),onBlur:()=>y(),className:`SellerProfile__input ${(t=(s=N.experienceYears)==null?void 0:s[r])!=null&&t.fromYear?"SellerProfile__input--error":""}`}),((o=(c=N.experienceYears)==null?void 0:c[r])==null?void 0:o.fromYear)&&e.jsx("div",{className:"SellerProfile__field-error",children:N.experienceYears[r].fromYear})]}),e.jsxs("div",{className:"SellerProfile__input-field",children:[e.jsx("input",{type:"text",placeholder:"To Year",value:i.toYear,onChange:u=>v(r,"toYear",u.target.value),onBlur:()=>y(),className:`SellerProfile__input ${(n=(a=N.experienceYears)==null?void 0:a[r])!=null&&n.toYear?"SellerProfile__input--error":""}`}),((f=(_=N.experienceYears)==null?void 0:_[r])==null?void 0:f.toYear)&&e.jsx("div",{className:"SellerProfile__field-error",children:N.experienceYears[r].toYear})]})]})]})]},r)})}):e.jsx("div",{className:"SellerProfile__no-data",children:"No experience information provided"}),e.jsx("div",{className:"SellerProfile__add-experience-container",children:e.jsxs("button",{type:"button",className:"SellerProfile__add-btn btn-primary",onClick:G,children:[e.jsx(ie,{})," Add More Experience"]})})]}):e.jsx(e.Fragment,{children:l.experiences&&l.experiences.length>0?e.jsx("div",{className:"SellerProfile__experiences-grid",children:l.experiences.map((i,r)=>e.jsxs("div",{className:"SellerProfile__experience-item",children:[e.jsxs("div",{className:"SellerProfile__experience-header",children:[e.jsx(O,{className:"SellerProfile__experience-icon"}),e.jsxs("span",{className:"SellerProfile__experience-number",children:["Experience ",r+1]})]}),e.jsxs("div",{className:"SellerProfile__experience-content",children:[e.jsx("div",{className:"SellerProfile__experience-field",children:i.schoolName||"School Name"}),e.jsx("div",{className:"SellerProfile__experience-field",children:i.position||"Position"}),e.jsxs("div",{className:"SellerProfile__experience-years",children:[e.jsx("span",{children:i.fromYear||"Start Year"}),e.jsx("span",{children:i.toYear||"End Year"})]})]})]},r))}):e.jsx("div",{className:"SellerProfile__no-data",children:"No experience information provided"})})})]}),e.jsxs("div",{className:"SellerProfile__section",children:[e.jsx("h3",{className:"SellerProfile__section-title",children:"Social Media Links"}),e.jsx("div",{className:"SellerProfile__social-links",children:d?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(R,{className:"facebook"})}),e.jsx("input",{type:"url",name:"socialLinks.facebook",value:l.socialLinks.facebook,onChange:S,placeholder:"Facebook URL",className:"SellerProfile__input"})]})}),e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(M,{className:"instagram"})}),e.jsx("input",{type:"url",name:"socialLinks.instagram",value:l.socialLinks.instagram,onChange:S,placeholder:"Instagram URL",className:"SellerProfile__input"})]})}),e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(A,{className:"twitter"})}),e.jsx("input",{type:"url",name:"socialLinks.twitter",value:l.socialLinks.twitter,onChange:S,placeholder:"Twitter URL",className:"SellerProfile__input"})]})})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"SellerProfile__social-item",children:[e.jsx(R,{className:"SellerProfile__social-icon facebook"}),e.jsx("span",{className:"SellerProfile__social-label",children:"Facebook:"}),e.jsx("span",{className:"SellerProfile__social-value",children:l.socialLinks.facebook?e.jsx("a",{href:l.socialLinks.facebook,target:"_blank",rel:"noopener noreferrer",children:l.socialLinks.facebook}):"Not provided"})]}),e.jsxs("div",{className:"SellerProfile__social-item",children:[e.jsx(M,{className:"SellerProfile__social-icon instagram"}),e.jsx("span",{className:"SellerProfile__social-label",children:"Instagram:"}),e.jsx("span",{className:"SellerProfile__social-value",children:l.socialLinks.instagram?e.jsx("a",{href:l.socialLinks.instagram,target:"_blank",rel:"noopener noreferrer",children:l.socialLinks.instagram}):"Not provided"})]}),e.jsxs("div",{className:"SellerProfile__social-item",children:[e.jsx(A,{className:"SellerProfile__social-icon twitter"}),e.jsx("span",{className:"SellerProfile__social-label",children:"Twitter:"}),e.jsx("span",{className:"SellerProfile__social-value",children:l.socialLinks.twitter?e.jsx("a",{href:l.socialLinks.twitter,target:"_blank",rel:"noopener noreferrer",children:l.socialLinks.twitter}):"Not provided"})]})]})})]}),d&&e.jsx("div",{className:"SellerProfile__buttons",children:e.jsx("button",{type:"button",className:"SellerProfile__save-btn btn-primary",onClick:Q,disabled:g||j,children:g||j?"Updating...":"Update & Save"})})]})})};export{de as default};

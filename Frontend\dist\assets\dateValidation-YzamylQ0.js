import{f as e}from"./timezoneUtils-Dky5bF8c.js";const o=r=>{if(!r)return"N/A";try{return e(r)}catch(t){return console.error("Error formatting date:",t),"Invalid Date"}},n=r=>{if(!r)return"N/A";try{return e(r,{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",timeZoneName:"short"}).replace(", "," at ")}catch(t){return console.error("Error formatting date time:",t),"Invalid Date"}},i=r=>{if(!r)return"N/A";try{return e(r,{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})}catch(t){return console.error("Error formatting table date:",t),"Invalid Date"}};export{o as a,n as b,i as f};

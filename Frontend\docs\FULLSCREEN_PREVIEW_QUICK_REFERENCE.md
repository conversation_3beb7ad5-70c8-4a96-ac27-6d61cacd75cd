# Fullscreen Preview - Quick Reference Guide

## 🚀 Quick Start

### 1. Import the Component
```jsx
import FullscreenPreview from '../components/common/FullscreenPreview';
import { FaExpand } from 'react-icons/fa';
```

### 2. Add State Management
```jsx
const [showFullscreenPreview, setShowFullscreenPreview] = useState(false);
```

### 3. Add Helper Functions
```jsx
const isVideoContent = () => {
  return content?.contentType?.toLowerCase().includes('video') ||
         content?.fileUrl?.toLowerCase().match(/\.(mp4|avi|mov|wmv|flv|webm|mkv)$/);
};

const isPDFContent = () => {
  return content?.contentType?.toLowerCase().includes('pdf') ||
         content?.contentType?.toLowerCase().includes('document') ||
         content?.fileUrl?.toLowerCase().endsWith('.pdf');
};

const handleFullscreenPreview = () => {
  if (content?.fileUrl) {
    setShowFullscreenPreview(true);
  } else {
    toast.error("No file available for preview");
  }
};
```

### 4. Add Preview Button
```jsx
{/* Add this next to your existing content display */}
<div className="preview-controls">
  <button
    className="preview-btn"
    onClick={handleFullscreenPreview}
    title={isVideoContent() ? "Open video in fullscreen preview" : "Open PDF in fullscreen preview"}
  >
    <FaExpand /> {isVideoContent() ? "Preview Video" : "Preview PDF"}
  </button>
</div>
```

### 5. Add FullscreenPreview Component
```jsx
{/* Add this at the end of your component, before closing div */}
<FullscreenPreview
  isOpen={showFullscreenPreview}
  onClose={() => setShowFullscreenPreview(false)}
  fileUrl={content?.fileUrl}
  fileName={content?.fileUrl?.split("/").pop() || "file"}
  fileType={isVideoContent() ? "video" : isPDFContent() ? "pdf" : "unknown"}
  contentType={content?.contentType}
  title={content?.title}
  showDownload={false}
/>
```

### 6. Add CSS Styles
```css
/* Preview Button Styles */
.preview-controls {
  display: flex;
  justify-content: center;
  padding: var(--basefont);
  border-top: 1px solid var(--light-gray);
  background: var(--white);
}

.preview-btn {
  background: var(--btn-color);
  color: var(--white);
  border: none;
  padding: var(--smallfont) var(--basefont);
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: var(--smallfont);
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
}

.preview-btn:hover {
  background: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Mobile Responsive */
@media (max-width: 480px) {
  .preview-btn {
    padding: var(--extrasmallfont) var(--smallfont);
    font-size: var(--extrasmallfont);
    min-height: 44px;
    gap: var(--extrasmallfont);
  }

  .preview-controls {
    padding: var(--smallfont);
  }
}
```

## 📋 Component Locations

### Where Preview Buttons Are Added:

1. **StrategyDetails.jsx** (Seller Dashboard)
   - Video section: After `<video>` element
   - PDF section: After `<DocumentViewer>` component

2. **BuyerContentDetail.jsx** (Buyer Content Viewing)
   - Video section: After `<video>` element in video wrapper
   - PDF section: After `<DocumentViewer>` component

3. **DownloadDetails.jsx** (Downloaded Content)
   - Video section: After `<video>` element in video player
   - PDF section: After `<DocumentViewer>` component

## 🎯 Key Features

### ✅ Supported File Types
- **Videos**: MP4, AVI, MOV, WMV, FLV, WebM, MKV
- **Documents**: PDF files
- **Fallback**: Graceful handling of unsupported types

### ✅ Device Support
- **Desktop**: Full functionality with keyboard navigation
- **Tablet**: Touch-optimized controls
- **Mobile**: 44px touch targets, responsive layouts

### ✅ Browser Support
- Chrome/Chromium ✅
- Firefox ✅
- Safari ✅
- Edge ✅
- Mobile browsers ✅

## 🔧 Customization Options

### Button Text Customization
```jsx
// Custom button labels
<FaExpand /> {isVideoContent() ? "Watch Fullscreen" : "View Document"}
```

### Custom Styling
```jsx
// Add custom CSS classes
<button className="preview-btn custom-preview-btn">
```

### Download Functionality
```jsx
// Enable download button
<FullscreenPreview
  showDownload={true}
  onDownload={() => {
    // Custom download logic
    window.open(fileUrl, '_blank');
  }}
  // ... other props
/>
```

## 🐛 Common Issues & Solutions

### Issue: Preview button not showing
**Solution**: Check if content has `fileUrl` and proper `contentType`

### Issue: Fullscreen not working
**Solution**: Ensure browser supports Fullscreen API (works in all modern browsers)

### Issue: Video not playing
**Solution**: Check video URL accessibility and format support

### Issue: PDF not loading
**Solution**: Verify PDF URL is accessible and not corrupted

### Issue: Mobile touch targets too small
**Solution**: Ensure CSS includes mobile responsive styles with 44px minimum

## 📱 Testing Checklist

### Quick Test Steps:
1. ✅ Click preview button opens fullscreen
2. ✅ ESC key closes preview
3. ✅ Close button works
4. ✅ Fullscreen toggle works
5. ✅ Responsive on mobile
6. ✅ Works with both PDF and video
7. ✅ Error handling for invalid files

### Browser Testing:
```bash
# Test in different browsers
- Chrome: ✅
- Firefox: ✅
- Safari: ✅
- Edge: ✅
- Mobile Chrome: ✅
- Mobile Safari: ✅
```

## 🚀 Performance Tips

1. **Lazy Loading**: Only load preview when button is clicked
2. **File Size**: Optimize video/PDF file sizes for better loading
3. **Caching**: Browser will cache files automatically
4. **Error Handling**: Always provide fallback options

## 📞 Need Help?

1. Check browser console for errors
2. Verify file URLs are accessible
3. Test with different file types
4. Check responsive design on target devices
5. Review implementation documentation

---

**Last Updated**: December 2024  
**Version**: 1.0.0

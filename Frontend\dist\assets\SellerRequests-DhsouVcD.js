import{b as i,by as d,u as m,j as s,aV as u}from"./index-BpICMq6M.js";import{S as b}from"./SellerLayout-DgpLyAbq.js";import{a as x}from"./index-BNN5CycL.js";import{T as y}from"./Table-JO9PSQsL.js";const N=()=>{const t=i(d),l=m(),o=e=>{l(`/seller/request-details/${e.replace("#","")}`)},r=[{key:"no",label:"No.",className:"no"},{key:"id",label:"Request Id"},{key:"content",label:"Videos/Documents",render:e=>s.jsxs("div",{className:"video-doc",children:[s.jsx("img",{src:e.image,alt:e.title}),s.jsx("span",{children:e.title})]})},{key:"date",label:"Date"},{key:"price",label:"Price"},{key:"requestedAmount",label:"Requested Amount"},{key:"requestedCustomer",label:"Requested Customer"},{key:"action",label:"Action",render:e=>s.jsxs("div",{className:"action-icons",children:[s.jsx(u,{className:"eye-icon",onClick:()=>o(e.id)}),s.jsx(x,{className:"comment-icon"})]})}],n=e=>e.map((a,c)=>({...a,no:c+1,date:`${a.date} | 4:50PM`}));return s.jsx(b,{children:s.jsx("div",{className:"seller-requests-container",children:s.jsx(y,{columns:r,data:n(t),className:"requests-table"})})})};export{N as default};

var Kn=Object.defineProperty;var gn=T=>{throw TypeError(T)};var Jn=(T,e,B)=>e in T?Kn(T,e,{enumerable:!0,configurable:!0,writable:!0,value:B}):T[e]=B;var Kt=(T,e,B)=>Jn(T,typeof e!="symbol"?e+"":e,B),Me=(T,e,B)=>e.has(T)||gn("Cannot "+B);var t=(T,e,B)=>(Me(T,e,"read from private field"),B?B.call(T):e.get(T)),rt=(T,e,B)=>e.has(T)?gn("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(T):e.set(T,B),ot=(T,e,B,n)=>(Me(T,e,"write to private field"),n?n.call(T,B):e.set(T,B),B),K=(T,e,B)=>(Me(T,e,"access private method"),B);var he=(T,e,B,n)=>({set _(d){ot(T,e,d,B)},get _(){return t(T,e,n)}});import{ca as getAugmentedNamespace,cb as getDefaultExportFromCjs,r as reactExports,c3 as React,cc as clsx,j as jsxRuntimeExports,cd as FaChevronLeft,ce as FaChevronRight,c9 as FaExclamationTriangle,a1 as FaSync,N as FaDownload,cf as FaFileWord,cg as API_BASE_URL,ch as FaTable,ci as FaFileExcel,ax as FaFile,bJ as FaFileAlt,aA as FaFilePdf}from"./index-BpICMq6M.js";import{P as PropTypes}from"./index-BEo3mm4X.js";function _mergeNamespaces(T,e){for(var B=0;B<e.length;B++){const n=e[B];if(typeof n!="string"&&!Array.isArray(n)){for(const d in n)if(d!=="default"&&!(d in T)){const H=Object.getOwnPropertyDescriptor(n,d);H&&Object.defineProperty(T,d,H.get?H:{enumerable:!0,get:()=>n[d]})}}}return Object.freeze(Object.defineProperty(T,Symbol.toStringTag,{value:"Module"}))}function commonjsRequire(T){throw new Error('Could not dynamically require "'+T+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var pdf$1={exports:{}};const __viteBrowserExternal={},__viteBrowserExternal$1=Object.freeze(Object.defineProperty({__proto__:null,default:__viteBrowserExternal},Symbol.toStringTag,{value:"Module"})),require$$5=getAugmentedNamespace(__viteBrowserExternal$1);var hasRequiredPdf;function requirePdf(){return hasRequiredPdf||(hasRequiredPdf=1,function(module,exports){(function(e,B){module.exports=e.pdfjsLib=B()})(globalThis,()=>(()=>{var __webpack_modules__=[,(T,e)=>{var qt;Object.defineProperty(e,"__esModule",{value:!0}),e.VerbosityLevel=e.Util=e.UnknownErrorException=e.UnexpectedResponseException=e.TextRenderingMode=e.RenderingIntentFlag=e.PromiseCapability=e.PermissionFlag=e.PasswordResponses=e.PasswordException=e.PageActionEventType=e.OPS=e.MissingPDFException=e.MAX_IMAGE_SIZE_TO_CACHE=e.LINE_FACTOR=e.LINE_DESCENT_FACTOR=e.InvalidPDFException=e.ImageKind=e.IDENTITY_MATRIX=e.FormatError=e.FeatureTest=e.FONT_IDENTITY_MATRIX=e.DocumentActionEventType=e.CMapCompressionType=e.BaseException=e.BASELINE_FACTOR=e.AnnotationType=e.AnnotationReplyType=e.AnnotationPrefix=e.AnnotationMode=e.AnnotationFlag=e.AnnotationFieldFlag=e.AnnotationEditorType=e.AnnotationEditorPrefix=e.AnnotationEditorParamsType=e.AnnotationBorderStyleType=e.AnnotationActionEventType=e.AbortException=void 0,e.assert=R,e.bytesToString=ct,e.createValidAbsoluteUrl=et,e.getModificationDate=xt,e.getUuid=Ot,e.getVerbosityLevel=$,e.info=q,e.isArrayBuffer=tt,e.isArrayEqual=wt,e.isNodeJS=void 0,e.normalizeUnicode=kt,e.objectFromMap=_t,e.objectSize=mt,e.setVerbosityLevel=Z,e.shadow=S,e.string32=bt,e.stringToBytes=ft,e.stringToPDFString=pt,e.stringToUTF8String=Et,e.unreachable=it,e.utf8StringToString=At,e.warn=G;const B=typeof process=="object"&&process+""=="[object process]"&&!process.versions.nw&&!(process.versions.electron&&process.type&&process.type!=="browser");e.isNodeJS=B;const n=[1,0,0,1,0,0];e.IDENTITY_MATRIX=n;const d=[.001,0,0,.001,0,0];e.FONT_IDENTITY_MATRIX=d;const H=1e7;e.MAX_IMAGE_SIZE_TO_CACHE=H;const W=1.35;e.LINE_FACTOR=W;const ht=.35;e.LINE_DESCENT_FACTOR=ht;const P=ht/W;e.BASELINE_FACTOR=P;const x={ANY:1,DISPLAY:2,PRINT:4,SAVE:8,ANNOTATIONS_FORMS:16,ANNOTATIONS_STORAGE:32,ANNOTATIONS_DISABLE:64,OPLIST:256};e.RenderingIntentFlag=x;const f={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3};e.AnnotationMode=f;const D="pdfjs_internal_editor_";e.AnnotationEditorPrefix=D;const I={DISABLE:-1,NONE:0,FREETEXT:3,STAMP:13,INK:15};e.AnnotationEditorType=I;const y={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23};e.AnnotationEditorParamsType=y;const m={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048};e.PermissionFlag=m;const E={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4};e.TextRenderingMode=E;const p={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3};e.ImageKind=p;const u={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26};e.AnnotationType=u;const b={GROUP:"Group",REPLY:"R"};e.AnnotationReplyType=b;const C={INVISIBLE:1,HIDDEN:2,PRINT:4,NOZOOM:8,NOROTATE:16,NOVIEW:32,READONLY:64,LOCKED:128,TOGGLENOVIEW:256,LOCKEDCONTENTS:512};e.AnnotationFlag=C;const _={READONLY:1,REQUIRED:2,NOEXPORT:4,MULTILINE:4096,PASSWORD:8192,NOTOGGLETOOFF:16384,RADIO:32768,PUSHBUTTON:65536,COMBO:131072,EDIT:262144,SORT:524288,FILESELECT:1048576,MULTISELECT:2097152,DONOTSPELLCHECK:4194304,DONOTSCROLL:8388608,COMB:16777216,RICHTEXT:33554432,RADIOSINUNISON:33554432,COMMITONSELCHANGE:67108864};e.AnnotationFieldFlag=_;const i={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5};e.AnnotationBorderStyleType=i;const c={E:"Mouse Enter",X:"Mouse Exit",D:"Mouse Down",U:"Mouse Up",Fo:"Focus",Bl:"Blur",PO:"PageOpen",PC:"PageClose",PV:"PageVisible",PI:"PageInvisible",K:"Keystroke",F:"Format",V:"Validate",C:"Calculate"};e.AnnotationActionEventType=c;const o={WC:"WillClose",WS:"WillSave",DS:"DidSave",WP:"WillPrint",DP:"DidPrint"};e.DocumentActionEventType=o;const l={O:"PageOpen",C:"PageClose"};e.PageActionEventType=l;const h={ERRORS:0,WARNINGS:1,INFOS:5};e.VerbosityLevel=h;const F={NONE:0,BINARY:1};e.CMapCompressionType=F;const r={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91};e.OPS=r;const v={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};e.PasswordResponses=v;let k=h.WARNINGS;function Z(vt){Number.isInteger(vt)&&(k=vt)}function $(){return k}function q(vt){k>=h.INFOS&&console.log(`Info: ${vt}`)}function G(vt){k>=h.WARNINGS&&console.log(`Warning: ${vt}`)}function it(vt){throw new Error(vt)}function R(vt,at){vt||it(at)}function V(vt){switch(vt==null?void 0:vt.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}function et(vt,at=null,lt=null){if(!vt)return null;try{if(lt&&typeof vt=="string"){if(lt.addDefaultProtocol&&vt.startsWith("www.")){const zt=vt.match(/\./g);(zt==null?void 0:zt.length)>=2&&(vt=`http://${vt}`)}if(lt.tryConvertEncoding)try{vt=Et(vt)}catch{}}const Rt=at?new URL(vt,at):new URL(vt);if(V(Rt))return Rt}catch{}return null}function S(vt,at,lt,Rt=!1){return Object.defineProperty(vt,at,{value:lt,enumerable:!Rt,configurable:!0,writable:!1}),lt}const s=function(){function at(lt,Rt){this.constructor===at&&it("Cannot initialize BaseException."),this.message=lt,this.name=Rt}return at.prototype=new Error,at.constructor=at,at}();e.BaseException=s;class a extends s{constructor(at,lt){super(at,"PasswordException"),this.code=lt}}e.PasswordException=a;class g extends s{constructor(at,lt){super(at,"UnknownErrorException"),this.details=lt}}e.UnknownErrorException=g;class L extends s{constructor(at){super(at,"InvalidPDFException")}}e.InvalidPDFException=L;class O extends s{constructor(at){super(at,"MissingPDFException")}}e.MissingPDFException=O;class N extends s{constructor(at,lt){super(at,"UnexpectedResponseException"),this.status=lt}}e.UnexpectedResponseException=N;class X extends s{constructor(at){super(at,"FormatError")}}e.FormatError=X;class nt extends s{constructor(at){super(at,"AbortException")}}e.AbortException=nt;function ct(vt){(typeof vt!="object"||(vt==null?void 0:vt.length)===void 0)&&it("Invalid argument for bytesToString");const at=vt.length,lt=8192;if(at<lt)return String.fromCharCode.apply(null,vt);const Rt=[];for(let zt=0;zt<at;zt+=lt){const $t=Math.min(zt+lt,at),U=vt.subarray(zt,$t);Rt.push(String.fromCharCode.apply(null,U))}return Rt.join("")}function ft(vt){typeof vt!="string"&&it("Invalid argument for stringToBytes");const at=vt.length,lt=new Uint8Array(at);for(let Rt=0;Rt<at;++Rt)lt[Rt]=vt.charCodeAt(Rt)&255;return lt}function bt(vt){return String.fromCharCode(vt>>24&255,vt>>16&255,vt>>8&255,vt&255)}function mt(vt){return Object.keys(vt).length}function _t(vt){const at=Object.create(null);for(const[lt,Rt]of vt)at[lt]=Rt;return at}function J(){const vt=new Uint8Array(4);return vt[0]=1,new Uint32Array(vt.buffer,0,1)[0]===1}function Q(){try{return new Function(""),!0}catch{return!1}}class A{static get isLittleEndian(){return S(this,"isLittleEndian",J())}static get isEvalSupported(){return S(this,"isEvalSupported",Q())}static get isOffscreenCanvasSupported(){return S(this,"isOffscreenCanvasSupported",typeof OffscreenCanvas<"u")}static get platform(){return typeof navigator>"u"?S(this,"platform",{isWin:!1,isMac:!1}):S(this,"platform",{isWin:navigator.platform.includes("Win"),isMac:navigator.platform.includes("Mac")})}static get isCSSRoundSupported(){var at,lt;return S(this,"isCSSRoundSupported",(lt=(at=globalThis.CSS)==null?void 0:at.supports)==null?void 0:lt.call(at,"width: round(1.5px, 1px)"))}}e.FeatureTest=A;const j=[...Array(256).keys()].map(vt=>vt.toString(16).padStart(2,"0"));class Y{static makeHexColor(at,lt,Rt){return`#${j[at]}${j[lt]}${j[Rt]}`}static scaleMinMax(at,lt){let Rt;at[0]?(at[0]<0&&(Rt=lt[0],lt[0]=lt[1],lt[1]=Rt),lt[0]*=at[0],lt[1]*=at[0],at[3]<0&&(Rt=lt[2],lt[2]=lt[3],lt[3]=Rt),lt[2]*=at[3],lt[3]*=at[3]):(Rt=lt[0],lt[0]=lt[2],lt[2]=Rt,Rt=lt[1],lt[1]=lt[3],lt[3]=Rt,at[1]<0&&(Rt=lt[2],lt[2]=lt[3],lt[3]=Rt),lt[2]*=at[1],lt[3]*=at[1],at[2]<0&&(Rt=lt[0],lt[0]=lt[1],lt[1]=Rt),lt[0]*=at[2],lt[1]*=at[2]),lt[0]+=at[4],lt[1]+=at[4],lt[2]+=at[5],lt[3]+=at[5]}static transform(at,lt){return[at[0]*lt[0]+at[2]*lt[1],at[1]*lt[0]+at[3]*lt[1],at[0]*lt[2]+at[2]*lt[3],at[1]*lt[2]+at[3]*lt[3],at[0]*lt[4]+at[2]*lt[5]+at[4],at[1]*lt[4]+at[3]*lt[5]+at[5]]}static applyTransform(at,lt){const Rt=at[0]*lt[0]+at[1]*lt[2]+lt[4],zt=at[0]*lt[1]+at[1]*lt[3]+lt[5];return[Rt,zt]}static applyInverseTransform(at,lt){const Rt=lt[0]*lt[3]-lt[1]*lt[2],zt=(at[0]*lt[3]-at[1]*lt[2]+lt[2]*lt[5]-lt[4]*lt[3])/Rt,$t=(-at[0]*lt[1]+at[1]*lt[0]+lt[4]*lt[1]-lt[5]*lt[0])/Rt;return[zt,$t]}static getAxialAlignedBoundingBox(at,lt){const Rt=this.applyTransform(at,lt),zt=this.applyTransform(at.slice(2,4),lt),$t=this.applyTransform([at[0],at[3]],lt),U=this.applyTransform([at[2],at[1]],lt);return[Math.min(Rt[0],zt[0],$t[0],U[0]),Math.min(Rt[1],zt[1],$t[1],U[1]),Math.max(Rt[0],zt[0],$t[0],U[0]),Math.max(Rt[1],zt[1],$t[1],U[1])]}static inverseTransform(at){const lt=at[0]*at[3]-at[1]*at[2];return[at[3]/lt,-at[1]/lt,-at[2]/lt,at[0]/lt,(at[2]*at[5]-at[4]*at[3])/lt,(at[4]*at[1]-at[5]*at[0])/lt]}static singularValueDecompose2dScale(at){const lt=[at[0],at[2],at[1],at[3]],Rt=at[0]*lt[0]+at[1]*lt[2],zt=at[0]*lt[1]+at[1]*lt[3],$t=at[2]*lt[0]+at[3]*lt[2],U=at[2]*lt[1]+at[3]*lt[3],gt=(Rt+U)/2,Tt=Math.sqrt((Rt+U)**2-4*(Rt*U-$t*zt))/2,Dt=gt+Tt||1,Nt=gt-Tt||1;return[Math.sqrt(Dt),Math.sqrt(Nt)]}static normalizeRect(at){const lt=at.slice(0);return at[0]>at[2]&&(lt[0]=at[2],lt[2]=at[0]),at[1]>at[3]&&(lt[1]=at[3],lt[3]=at[1]),lt}static intersect(at,lt){const Rt=Math.max(Math.min(at[0],at[2]),Math.min(lt[0],lt[2])),zt=Math.min(Math.max(at[0],at[2]),Math.max(lt[0],lt[2]));if(Rt>zt)return null;const $t=Math.max(Math.min(at[1],at[3]),Math.min(lt[1],lt[3])),U=Math.min(Math.max(at[1],at[3]),Math.max(lt[1],lt[3]));return $t>U?null:[Rt,$t,zt,U]}static bezierBoundingBox(at,lt,Rt,zt,$t,U,gt,Tt){const Dt=[],Nt=[[],[]];let Ct,M,w,z,dt,ut,yt,St;for(let Wt=0;Wt<2;++Wt){if(Wt===0?(M=6*at-12*Rt+6*$t,Ct=-3*at+9*Rt-9*$t+3*gt,w=3*Rt-3*at):(M=6*lt-12*zt+6*U,Ct=-3*lt+9*zt-9*U+3*Tt,w=3*zt-3*lt),Math.abs(Ct)<1e-12){if(Math.abs(M)<1e-12)continue;z=-w/M,0<z&&z<1&&Dt.push(z);continue}yt=M*M-4*w*Ct,St=Math.sqrt(yt),!(yt<0)&&(dt=(-M+St)/(2*Ct),0<dt&&dt<1&&Dt.push(dt),ut=(-M-St)/(2*Ct),0<ut&&ut<1&&Dt.push(ut))}let Lt=Dt.length,Ft;const Pt=Lt;for(;Lt--;)z=Dt[Lt],Ft=1-z,Nt[0][Lt]=Ft*Ft*Ft*at+3*Ft*Ft*z*Rt+3*Ft*z*z*$t+z*z*z*gt,Nt[1][Lt]=Ft*Ft*Ft*lt+3*Ft*Ft*z*zt+3*Ft*z*z*U+z*z*z*Tt;return Nt[0][Pt]=at,Nt[1][Pt]=lt,Nt[0][Pt+1]=gt,Nt[1][Pt+1]=Tt,Nt[0].length=Nt[1].length=Pt+2,[Math.min(...Nt[0]),Math.min(...Nt[1]),Math.max(...Nt[0]),Math.max(...Nt[1])]}}e.Util=Y;const st=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,728,711,710,729,733,731,730,732,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8226,8224,8225,8230,8212,8211,402,8260,8249,8250,8722,8240,8222,8220,8221,8216,8217,8218,8482,64257,64258,321,338,352,376,381,305,322,339,353,382,0,8364];function pt(vt){if(vt[0]>="ï"){let lt;if(vt[0]==="þ"&&vt[1]==="ÿ"?lt="utf-16be":vt[0]==="ÿ"&&vt[1]==="þ"?lt="utf-16le":vt[0]==="ï"&&vt[1]==="»"&&vt[2]==="¿"&&(lt="utf-8"),lt)try{const Rt=new TextDecoder(lt,{fatal:!0}),zt=ft(vt);return Rt.decode(zt)}catch(Rt){G(`stringToPDFString: "${Rt}".`)}}const at=[];for(let lt=0,Rt=vt.length;lt<Rt;lt++){const zt=st[vt.charCodeAt(lt)];at.push(zt?String.fromCharCode(zt):vt.charAt(lt))}return at.join("")}function Et(vt){return decodeURIComponent(escape(vt))}function At(vt){return unescape(encodeURIComponent(vt))}function tt(vt){return typeof vt=="object"&&(vt==null?void 0:vt.byteLength)!==void 0}function wt(vt,at){if(vt.length!==at.length)return!1;for(let lt=0,Rt=vt.length;lt<Rt;lt++)if(vt[lt]!==at[lt])return!1;return!0}function xt(vt=new Date){return[vt.getUTCFullYear().toString(),(vt.getUTCMonth()+1).toString().padStart(2,"0"),vt.getUTCDate().toString().padStart(2,"0"),vt.getUTCHours().toString().padStart(2,"0"),vt.getUTCMinutes().toString().padStart(2,"0"),vt.getUTCSeconds().toString().padStart(2,"0")].join("")}class Ut{constructor(){rt(this,qt,!1);this.promise=new Promise((at,lt)=>{this.resolve=Rt=>{ot(this,qt,!0),at(Rt)},this.reject=Rt=>{ot(this,qt,!0),lt(Rt)}})}get settled(){return t(this,qt)}}qt=new WeakMap,e.PromiseCapability=Ut;let jt=null,Vt=null;function kt(vt){return jt||(jt=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu,Vt=new Map([["ﬅ","ſt"]])),vt.replaceAll(jt,(at,lt,Rt)=>lt?lt.normalize("NFKC"):Vt.get(Rt))}function Ot(){if(typeof crypto<"u"&&typeof(crypto==null?void 0:crypto.randomUUID)=="function")return crypto.randomUUID();const vt=new Uint8Array(32);if(typeof crypto<"u"&&typeof(crypto==null?void 0:crypto.getRandomValues)=="function")crypto.getRandomValues(vt);else for(let at=0;at<32;at++)vt[at]=Math.floor(Math.random()*255);return ct(vt)}const Ht="pdfjs_internal_id_";e.AnnotationPrefix=Ht},(__unused_webpack_module,exports,__w_pdfjs_require__)=>{var T,B,n,d,re,_e,ht,P,x,f,D,I,y,m,E,ye,u,b,De,_,i;Object.defineProperty(exports,"__esModule",{value:!0}),exports.RenderTask=exports.PDFWorkerUtil=exports.PDFWorker=exports.PDFPageProxy=exports.PDFDocumentProxy=exports.PDFDocumentLoadingTask=exports.PDFDataRangeTransport=exports.LoopbackPort=exports.DefaultStandardFontDataFactory=exports.DefaultFilterFactory=exports.DefaultCanvasFactory=exports.DefaultCMapReaderFactory=void 0,Object.defineProperty(exports,"SVGGraphics",{enumerable:!0,get:function(){return _displaySvg.SVGGraphics}}),exports.build=void 0,exports.getDocument=getDocument,exports.version=void 0;var _util=__w_pdfjs_require__(1),_annotation_storage=__w_pdfjs_require__(3),_display_utils=__w_pdfjs_require__(6),_font_loader=__w_pdfjs_require__(9),_displayNode_utils=__w_pdfjs_require__(10),_canvas=__w_pdfjs_require__(11),_worker_options=__w_pdfjs_require__(14),_message_handler=__w_pdfjs_require__(15),_metadata=__w_pdfjs_require__(16),_optional_content_config=__w_pdfjs_require__(17),_transport_stream=__w_pdfjs_require__(18),_displayFetch_stream=__w_pdfjs_require__(19),_displayNetwork=__w_pdfjs_require__(22),_displayNode_stream=__w_pdfjs_require__(23),_displaySvg=__w_pdfjs_require__(24),_xfa_text=__w_pdfjs_require__(25);const DEFAULT_RANGE_CHUNK_SIZE=65536,RENDERING_CANCELLED_TIMEOUT=100,DELAYED_CLEANUP_TIMEOUT=5e3,DefaultCanvasFactory=_util.isNodeJS?_displayNode_utils.NodeCanvasFactory:_display_utils.DOMCanvasFactory;exports.DefaultCanvasFactory=DefaultCanvasFactory;const DefaultCMapReaderFactory=_util.isNodeJS?_displayNode_utils.NodeCMapReaderFactory:_display_utils.DOMCMapReaderFactory;exports.DefaultCMapReaderFactory=DefaultCMapReaderFactory;const DefaultFilterFactory=_util.isNodeJS?_displayNode_utils.NodeFilterFactory:_display_utils.DOMFilterFactory;exports.DefaultFilterFactory=DefaultFilterFactory;const DefaultStandardFontDataFactory=_util.isNodeJS?_displayNode_utils.NodeStandardFontDataFactory:_display_utils.DOMStandardFontDataFactory;exports.DefaultStandardFontDataFactory=DefaultStandardFontDataFactory;function getDocument(o){if(typeof o=="string"||o instanceof URL?o={url:o}:(0,_util.isArrayBuffer)(o)&&(o={data:o}),typeof o!="object")throw new Error("Invalid parameter in getDocument, need parameter object.");if(!o.url&&!o.data&&!o.range)throw new Error("Invalid parameter object: need either .data, .range or .url");const l=new PDFDocumentLoadingTask,{docId:h}=l,F=o.url?getUrlProp(o.url):null,r=o.data?getDataProp(o.data):null,v=o.httpHeaders||null,k=o.withCredentials===!0,Z=o.password??null,$=o.range instanceof PDFDataRangeTransport?o.range:null,q=Number.isInteger(o.rangeChunkSize)&&o.rangeChunkSize>0?o.rangeChunkSize:DEFAULT_RANGE_CHUNK_SIZE;let G=o.worker instanceof PDFWorker?o.worker:null;const it=o.verbosity,R=typeof o.docBaseUrl=="string"&&!(0,_display_utils.isDataScheme)(o.docBaseUrl)?o.docBaseUrl:null,V=typeof o.cMapUrl=="string"?o.cMapUrl:null,et=o.cMapPacked!==!1,S=o.CMapReaderFactory||DefaultCMapReaderFactory,s=typeof o.standardFontDataUrl=="string"?o.standardFontDataUrl:null,a=o.StandardFontDataFactory||DefaultStandardFontDataFactory,g=o.stopAtErrors!==!0,L=Number.isInteger(o.maxImageSize)&&o.maxImageSize>-1?o.maxImageSize:-1,O=o.isEvalSupported!==!1,N=typeof o.isOffscreenCanvasSupported=="boolean"?o.isOffscreenCanvasSupported:!_util.isNodeJS,X=Number.isInteger(o.canvasMaxAreaInBytes)?o.canvasMaxAreaInBytes:-1,nt=typeof o.disableFontFace=="boolean"?o.disableFontFace:_util.isNodeJS,ct=o.fontExtraProperties===!0,ft=o.enableXfa===!0,bt=o.ownerDocument||globalThis.document,mt=o.disableRange===!0,_t=o.disableStream===!0,J=o.disableAutoFetch===!0,Q=o.pdfBug===!0,A=$?$.length:o.length??NaN,j=typeof o.useSystemFonts=="boolean"?o.useSystemFonts:!_util.isNodeJS&&!nt,Y=typeof o.useWorkerFetch=="boolean"?o.useWorkerFetch:S===_display_utils.DOMCMapReaderFactory&&a===_display_utils.DOMStandardFontDataFactory&&V&&s&&(0,_display_utils.isValidFetchUrl)(V,document.baseURI)&&(0,_display_utils.isValidFetchUrl)(s,document.baseURI),st=o.canvasFactory||new DefaultCanvasFactory({ownerDocument:bt}),pt=o.filterFactory||new DefaultFilterFactory({docId:h,ownerDocument:bt}),Et=null;(0,_util.setVerbosityLevel)(it);const At={canvasFactory:st,filterFactory:pt};if(Y||(At.cMapReaderFactory=new S({baseUrl:V,isCompressed:et}),At.standardFontDataFactory=new a({baseUrl:s})),!G){const xt={verbosity:it,port:_worker_options.GlobalWorkerOptions.workerPort};G=xt.port?PDFWorker.fromPort(xt):new PDFWorker(xt),l._worker=G}const tt={docId:h,apiVersion:"3.11.174",data:r,password:Z,disableAutoFetch:J,rangeChunkSize:q,length:A,docBaseUrl:R,enableXfa:ft,evaluatorOptions:{maxImageSize:L,disableFontFace:nt,ignoreErrors:g,isEvalSupported:O,isOffscreenCanvasSupported:N,canvasMaxAreaInBytes:X,fontExtraProperties:ct,useSystemFonts:j,cMapUrl:Y?V:null,standardFontDataUrl:Y?s:null}},wt={ignoreErrors:g,isEvalSupported:O,disableFontFace:nt,fontExtraProperties:ct,enableXfa:ft,ownerDocument:bt,disableAutoFetch:J,pdfBug:Q,styleElement:Et};return G.promise.then(function(){if(l.destroyed)throw new Error("Loading aborted");const xt=_fetchDocument(G,tt),Ut=new Promise(function(jt){let Vt;$?Vt=new _transport_stream.PDFDataTransportStream({length:A,initialData:$.initialData,progressiveDone:$.progressiveDone,contentDispositionFilename:$.contentDispositionFilename,disableRange:mt,disableStream:_t},$):r||(Vt=(Ot=>_util.isNodeJS?new _displayNode_stream.PDFNodeStream(Ot):(0,_display_utils.isValidFetchUrl)(Ot.url)?new _displayFetch_stream.PDFFetchStream(Ot):new _displayNetwork.PDFNetworkStream(Ot))({url:F,length:A,httpHeaders:v,withCredentials:k,rangeChunkSize:q,disableRange:mt,disableStream:_t})),jt(Vt)});return Promise.all([xt,Ut]).then(function([jt,Vt]){if(l.destroyed)throw new Error("Loading aborted");const kt=new _message_handler.MessageHandler(h,jt,G.port),Ot=new WorkerTransport(kt,l,Vt,wt,At);l._transport=Ot,kt.send("Ready",null)})}).catch(l._capability.reject),l}async function _fetchDocument(o,l){if(o.destroyed)throw new Error("Worker was destroyed");const h=await o.messageHandler.sendWithPromise("GetDocRequest",l,l.data?[l.data.buffer]:null);if(o.destroyed)throw new Error("Worker was destroyed");return h}function getUrlProp(o){if(o instanceof URL)return o.href;try{return new URL(o,window.location).href}catch{if(_util.isNodeJS&&typeof o=="string")return o}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}function getDataProp(o){if(_util.isNodeJS&&typeof Buffer<"u"&&o instanceof Buffer)throw new Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(o instanceof Uint8Array&&o.byteLength===o.buffer.byteLength)return o;if(typeof o=="string")return(0,_util.stringToBytes)(o);if(typeof o=="object"&&!isNaN(o==null?void 0:o.length)||(0,_util.isArrayBuffer)(o))return new Uint8Array(o);throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}const e=class e{constructor(){this._capability=new _util.PromiseCapability,this._transport=null,this._worker=null,this.docId=`d${he(e,T)._++}`,this.destroyed=!1,this.onPassword=null,this.onProgress=null}get promise(){return this._capability.promise}async destroy(){var l,h,F;this.destroyed=!0;try{(l=this._worker)!=null&&l.port&&(this._worker._pendingDestroy=!0),await((h=this._transport)==null?void 0:h.destroy())}catch(r){throw(F=this._worker)!=null&&F.port&&delete this._worker._pendingDestroy,r}this._transport=null,this._worker&&(this._worker.destroy(),this._worker=null)}};T=new WeakMap,rt(e,T,0);let PDFDocumentLoadingTask=e;exports.PDFDocumentLoadingTask=PDFDocumentLoadingTask;class PDFDataRangeTransport{constructor(l,h,F=!1,r=null){this.length=l,this.initialData=h,this.progressiveDone=F,this.contentDispositionFilename=r,this._rangeListeners=[],this._progressListeners=[],this._progressiveReadListeners=[],this._progressiveDoneListeners=[],this._readyCapability=new _util.PromiseCapability}addRangeListener(l){this._rangeListeners.push(l)}addProgressListener(l){this._progressListeners.push(l)}addProgressiveReadListener(l){this._progressiveReadListeners.push(l)}addProgressiveDoneListener(l){this._progressiveDoneListeners.push(l)}onDataRange(l,h){for(const F of this._rangeListeners)F(l,h)}onDataProgress(l,h){this._readyCapability.promise.then(()=>{for(const F of this._progressListeners)F(l,h)})}onDataProgressiveRead(l){this._readyCapability.promise.then(()=>{for(const h of this._progressiveReadListeners)h(l)})}onDataProgressiveDone(){this._readyCapability.promise.then(()=>{for(const l of this._progressiveDoneListeners)l()})}transportReady(){this._readyCapability.resolve()}requestDataRange(l,h){(0,_util.unreachable)("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}exports.PDFDataRangeTransport=PDFDataRangeTransport;class PDFDocumentProxy{constructor(l,h){this._pdfInfo=l,this._transport=h,Object.defineProperty(this,"getJavaScript",{value:()=>((0,_display_utils.deprecated)("`PDFDocumentProxy.getJavaScript`, please use `PDFDocumentProxy.getJSActions` instead."),this.getJSActions().then(F=>{if(!F)return F;const r=[];for(const v in F)r.push(...F[v]);return r}))})}get annotationStorage(){return this._transport.annotationStorage}get filterFactory(){return this._transport.filterFactory}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return(0,_util.shadow)(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(l){return this._transport.getPage(l)}getPageIndex(l){return this._transport.getPageIndex(l)}getDestinations(){return this._transport.getDestinations()}getDestination(l){return this._transport.getDestination(l)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig(){return this._transport.getOptionalContentConfig()}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(l=!1){return this._transport.startCleanup(l||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}exports.PDFDocumentProxy=PDFDocumentProxy;class PDFPageProxy{constructor(l,h,F,r=!1){rt(this,d);rt(this,B,null);rt(this,n,!1);this._pageIndex=l,this._pageInfo=h,this._transport=F,this._stats=r?new _display_utils.StatTimer:null,this._pdfBug=r,this.commonObjs=F.commonObjs,this.objs=new PDFObjects,this._maybeCleanupAfterRender=!1,this._intentStates=new Map,this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport({scale:l,rotation:h=this.rotate,offsetX:F=0,offsetY:r=0,dontFlip:v=!1}={}){return new _display_utils.PageViewport({viewBox:this.view,scale:l,rotation:h,offsetX:F,offsetY:r,dontFlip:v})}getAnnotations({intent:l="display"}={}){const h=this._transport.getRenderingIntent(l);return this._transport.getAnnotations(this._pageIndex,h.renderingIntent)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get filterFactory(){return this._transport.filterFactory}get isPureXfa(){return(0,_util.shadow)(this,"isPureXfa",!!this._transport._htmlForXfa)}async getXfa(){var l;return((l=this._transport._htmlForXfa)==null?void 0:l.children[this._pageIndex])||null}render({canvasContext:l,viewport:h,intent:F="display",annotationMode:r=_util.AnnotationMode.ENABLE,transform:v=null,background:k=null,optionalContentConfigPromise:Z=null,annotationCanvasMap:$=null,pageColors:q=null,printAnnotationStorage:G=null}){var a,g;(a=this._stats)==null||a.time("Overall");const it=this._transport.getRenderingIntent(F,r,G);ot(this,n,!1),K(this,d,_e).call(this),Z||(Z=this._transport.getOptionalContentConfig());let R=this._intentStates.get(it.cacheKey);R||(R=Object.create(null),this._intentStates.set(it.cacheKey,R)),R.streamReaderCancelTimeout&&(clearTimeout(R.streamReaderCancelTimeout),R.streamReaderCancelTimeout=null);const V=!!(it.renderingIntent&_util.RenderingIntentFlag.PRINT);R.displayReadyCapability||(R.displayReadyCapability=new _util.PromiseCapability,R.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},(g=this._stats)==null||g.time("Page Request"),this._pumpOperatorList(it));const et=L=>{var O,N;R.renderTasks.delete(S),(this._maybeCleanupAfterRender||V)&&ot(this,n,!0),K(this,d,re).call(this,!V),L?(S.capability.reject(L),this._abortOperatorList({intentState:R,reason:L instanceof Error?L:new Error(L)})):S.capability.resolve(),(O=this._stats)==null||O.timeEnd("Rendering"),(N=this._stats)==null||N.timeEnd("Overall")},S=new InternalRenderTask({callback:et,params:{canvasContext:l,viewport:h,transform:v,background:k},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:$,operatorList:R.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!V,pdfBug:this._pdfBug,pageColors:q});(R.renderTasks||(R.renderTasks=new Set)).add(S);const s=S.task;return Promise.all([R.displayReadyCapability.promise,Z]).then(([L,O])=>{var N;if(this.destroyed){et();return}(N=this._stats)==null||N.time("Rendering"),S.initializeGraphics({transparency:L,optionalContentConfig:O}),S.operatorListChanged()}).catch(et),s}getOperatorList({intent:l="display",annotationMode:h=_util.AnnotationMode.ENABLE,printAnnotationStorage:F=null}={}){var $;function r(){k.operatorList.lastChunk&&(k.opListReadCapability.resolve(k.operatorList),k.renderTasks.delete(Z))}const v=this._transport.getRenderingIntent(l,h,F,!0);let k=this._intentStates.get(v.cacheKey);k||(k=Object.create(null),this._intentStates.set(v.cacheKey,k));let Z;return k.opListReadCapability||(Z=Object.create(null),Z.operatorListChanged=r,k.opListReadCapability=new _util.PromiseCapability,(k.renderTasks||(k.renderTasks=new Set)).add(Z),k.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},($=this._stats)==null||$.time("Page Request"),this._pumpOperatorList(v)),k.opListReadCapability.promise}streamTextContent({includeMarkedContent:l=!1,disableNormalization:h=!1}={}){return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:l===!0,disableNormalization:h===!0},{highWaterMark:100,size(r){return r.items.length}})}getTextContent(l={}){if(this._transport._htmlForXfa)return this.getXfa().then(F=>_xfa_text.XfaText.textContent(F));const h=this.streamTextContent(l);return new Promise(function(F,r){function v(){k.read().then(function({value:$,done:q}){if(q){F(Z);return}Object.assign(Z.styles,$.styles),Z.items.push(...$.items),v()},r)}const k=h.getReader(),Z={items:[],styles:Object.create(null)};v()})}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;const l=[];for(const h of this._intentStates.values())if(this._abortOperatorList({intentState:h,reason:new Error("Page was destroyed."),force:!0}),!h.opListReadCapability)for(const F of h.renderTasks)l.push(F.completed),F.cancel();return this.objs.clear(),ot(this,n,!1),K(this,d,_e).call(this),Promise.all(l)}cleanup(l=!1){ot(this,n,!0);const h=K(this,d,re).call(this,!1);return l&&h&&this._stats&&(this._stats=new _display_utils.StatTimer),h}_startRenderPage(l,h){var r,v;const F=this._intentStates.get(h);F&&((r=this._stats)==null||r.timeEnd("Page Request"),(v=F.displayReadyCapability)==null||v.resolve(l))}_renderPageChunk(l,h){for(let F=0,r=l.length;F<r;F++)h.operatorList.fnArray.push(l.fnArray[F]),h.operatorList.argsArray.push(l.argsArray[F]);h.operatorList.lastChunk=l.lastChunk,h.operatorList.separateAnnots=l.separateAnnots;for(const F of h.renderTasks)F.operatorListChanged();l.lastChunk&&K(this,d,re).call(this,!0)}_pumpOperatorList({renderingIntent:l,cacheKey:h,annotationStorageSerializable:F}){const{map:r,transfers:v}=F,Z=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:l,cacheKey:h,annotationStorage:r},v).getReader(),$=this._intentStates.get(h);$.streamReader=Z;const q=()=>{Z.read().then(({value:G,done:it})=>{if(it){$.streamReader=null;return}this._transport.destroyed||(this._renderPageChunk(G,$),q())},G=>{if($.streamReader=null,!this._transport.destroyed){if($.operatorList){$.operatorList.lastChunk=!0;for(const it of $.renderTasks)it.operatorListChanged();K(this,d,re).call(this,!0)}if($.displayReadyCapability)$.displayReadyCapability.reject(G);else if($.opListReadCapability)$.opListReadCapability.reject(G);else throw G}})};q()}_abortOperatorList({intentState:l,reason:h,force:F=!1}){if(l.streamReader){if(l.streamReaderCancelTimeout&&(clearTimeout(l.streamReaderCancelTimeout),l.streamReaderCancelTimeout=null),!F){if(l.renderTasks.size>0)return;if(h instanceof _display_utils.RenderingCancelledException){let r=RENDERING_CANCELLED_TIMEOUT;h.extraDelay>0&&h.extraDelay<1e3&&(r+=h.extraDelay),l.streamReaderCancelTimeout=setTimeout(()=>{l.streamReaderCancelTimeout=null,this._abortOperatorList({intentState:l,reason:h,force:!0})},r);return}}if(l.streamReader.cancel(new _util.AbortException(h.message)).catch(()=>{}),l.streamReader=null,!this._transport.destroyed){for(const[r,v]of this._intentStates)if(v===l){this._intentStates.delete(r);break}this.cleanup()}}}get stats(){return this._stats}}B=new WeakMap,n=new WeakMap,d=new WeakSet,re=function(l=!1){if(K(this,d,_e).call(this),!t(this,n)||this.destroyed)return!1;if(l)return ot(this,B,setTimeout(()=>{ot(this,B,null),K(this,d,re).call(this,!1)},DELAYED_CLEANUP_TIMEOUT)),!1;for(const{renderTasks:h,operatorList:F}of this._intentStates.values())if(h.size>0||!F.lastChunk)return!1;return this._intentStates.clear(),this.objs.clear(),ot(this,n,!1),!0},_e=function(){t(this,B)&&(clearTimeout(t(this,B)),ot(this,B,null))},exports.PDFPageProxy=PDFPageProxy;class LoopbackPort{constructor(){rt(this,ht,new Set);rt(this,P,Promise.resolve())}postMessage(l,h){const F={data:structuredClone(l,h?{transfer:h}:null)};t(this,P).then(()=>{for(const r of t(this,ht))r.call(this,F)})}addEventListener(l,h){t(this,ht).add(h)}removeEventListener(l,h){t(this,ht).delete(h)}terminate(){t(this,ht).clear()}}ht=new WeakMap,P=new WeakMap,exports.LoopbackPort=LoopbackPort;const PDFWorkerUtil={isWorkerDisabled:!1,fallbackWorkerSrc:null,fakeWorkerId:0};exports.PDFWorkerUtil=PDFWorkerUtil;{if(_util.isNodeJS&&typeof commonjsRequire=="function")PDFWorkerUtil.isWorkerDisabled=!0,PDFWorkerUtil.fallbackWorkerSrc="./pdf.worker.js";else if(typeof document=="object"){const o=(x=document==null?void 0:document.currentScript)==null?void 0:x.src;o&&(PDFWorkerUtil.fallbackWorkerSrc=o.replace(/(\.(?:min\.)?js)(\?.*)?$/i,".worker$1$2"))}PDFWorkerUtil.isSameOrigin=function(o,l){let h;try{if(h=new URL(o),!h.origin||h.origin==="null")return!1}catch{return!1}const F=new URL(l,h);return h.origin===F.origin},PDFWorkerUtil.createCDNWrapper=function(o){const l=`importScripts("${o}");`;return URL.createObjectURL(new Blob([l]))}}const _PDFWorker=class _PDFWorker{constructor({name:o=null,port:l=null,verbosity:h=(0,_util.getVerbosityLevel)()}={}){var F;if(this.name=o,this.destroyed=!1,this.verbosity=h,this._readyCapability=new _util.PromiseCapability,this._port=null,this._webWorker=null,this._messageHandler=null,l){if((F=t(_PDFWorker,f))!=null&&F.has(l))throw new Error("Cannot use more than one PDFWorker per port.");(t(_PDFWorker,f)||ot(_PDFWorker,f,new WeakMap)).set(l,this),this._initializeFromPort(l);return}this._initialize()}get promise(){return this._readyCapability.promise}get port(){return this._port}get messageHandler(){return this._messageHandler}_initializeFromPort(o){this._port=o,this._messageHandler=new _message_handler.MessageHandler("main","worker",o),this._messageHandler.on("ready",function(){}),this._readyCapability.resolve(),this._messageHandler.send("configure",{verbosity:this.verbosity})}_initialize(){if(!PDFWorkerUtil.isWorkerDisabled&&!_PDFWorker._mainThreadWorkerMessageHandler){let{workerSrc:o}=_PDFWorker;try{PDFWorkerUtil.isSameOrigin(window.location.href,o)||(o=PDFWorkerUtil.createCDNWrapper(new URL(o,window.location).href));const l=new Worker(o),h=new _message_handler.MessageHandler("main","worker",l),F=()=>{l.removeEventListener("error",r),h.destroy(),l.terminate(),this.destroyed?this._readyCapability.reject(new Error("Worker was destroyed")):this._setupFakeWorker()},r=()=>{this._webWorker||F()};l.addEventListener("error",r),h.on("test",k=>{if(l.removeEventListener("error",r),this.destroyed){F();return}k?(this._messageHandler=h,this._port=l,this._webWorker=l,this._readyCapability.resolve(),h.send("configure",{verbosity:this.verbosity})):(this._setupFakeWorker(),h.destroy(),l.terminate())}),h.on("ready",k=>{if(l.removeEventListener("error",r),this.destroyed){F();return}try{v()}catch{this._setupFakeWorker()}});const v=()=>{const k=new Uint8Array;h.send("test",k,[k.buffer])};v();return}catch{(0,_util.info)("The worker has been disabled.")}}this._setupFakeWorker()}_setupFakeWorker(){PDFWorkerUtil.isWorkerDisabled||((0,_util.warn)("Setting up fake worker."),PDFWorkerUtil.isWorkerDisabled=!0),_PDFWorker._setupFakeWorkerGlobal.then(o=>{if(this.destroyed){this._readyCapability.reject(new Error("Worker was destroyed"));return}const l=new LoopbackPort;this._port=l;const h=`fake${PDFWorkerUtil.fakeWorkerId++}`,F=new _message_handler.MessageHandler(h+"_worker",h,l);o.setup(F,l);const r=new _message_handler.MessageHandler(h,h+"_worker",l);this._messageHandler=r,this._readyCapability.resolve(),r.send("configure",{verbosity:this.verbosity})}).catch(o=>{this._readyCapability.reject(new Error(`Setting up fake worker failed: "${o.message}".`))})}destroy(){var o;this.destroyed=!0,this._webWorker&&(this._webWorker.terminate(),this._webWorker=null),(o=t(_PDFWorker,f))==null||o.delete(this._port),this._port=null,this._messageHandler&&(this._messageHandler.destroy(),this._messageHandler=null)}static fromPort(o){var h;if(!(o!=null&&o.port))throw new Error("PDFWorker.fromPort - invalid method signature.");const l=(h=t(this,f))==null?void 0:h.get(o.port);if(l){if(l._pendingDestroy)throw new Error("PDFWorker.fromPort - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return l}return new _PDFWorker(o)}static get workerSrc(){if(_worker_options.GlobalWorkerOptions.workerSrc)return _worker_options.GlobalWorkerOptions.workerSrc;if(PDFWorkerUtil.fallbackWorkerSrc!==null)return _util.isNodeJS||(0,_display_utils.deprecated)('No "GlobalWorkerOptions.workerSrc" specified.'),PDFWorkerUtil.fallbackWorkerSrc;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get _mainThreadWorkerMessageHandler(){var o;try{return((o=globalThis.pdfjsWorker)==null?void 0:o.WorkerMessageHandler)||null}catch{return null}}static get _setupFakeWorkerGlobal(){const loader=async()=>{const mainWorkerMessageHandler=this._mainThreadWorkerMessageHandler;if(mainWorkerMessageHandler)return mainWorkerMessageHandler;if(_util.isNodeJS&&typeof commonjsRequire=="function"){const worker=eval("require")(this.workerSrc);return worker.WorkerMessageHandler}return await(0,_display_utils.loadScript)(this.workerSrc),window.pdfjsWorker.WorkerMessageHandler};return(0,_util.shadow)(this,"_setupFakeWorkerGlobal",loader())}};f=new WeakMap,rt(_PDFWorker,f);let PDFWorker=_PDFWorker;exports.PDFWorker=PDFWorker;class WorkerTransport{constructor(l,h,F,r,v){rt(this,E);rt(this,D,new Map);rt(this,I,new Map);rt(this,y,new Map);rt(this,m,null);this.messageHandler=l,this.loadingTask=h,this.commonObjs=new PDFObjects,this.fontLoader=new _font_loader.FontLoader({ownerDocument:r.ownerDocument,styleElement:r.styleElement}),this._params=r,this.canvasFactory=v.canvasFactory,this.filterFactory=v.filterFactory,this.cMapReaderFactory=v.cMapReaderFactory,this.standardFontDataFactory=v.standardFontDataFactory,this.destroyed=!1,this.destroyCapability=null,this._networkStream=F,this._fullReader=null,this._lastProgress=null,this.downloadInfoCapability=new _util.PromiseCapability,this.setupMessageHandler()}get annotationStorage(){return(0,_util.shadow)(this,"annotationStorage",new _annotation_storage.AnnotationStorage)}getRenderingIntent(l,h=_util.AnnotationMode.ENABLE,F=null,r=!1){let v=_util.RenderingIntentFlag.DISPLAY,k=_annotation_storage.SerializableEmpty;switch(l){case"any":v=_util.RenderingIntentFlag.ANY;break;case"display":break;case"print":v=_util.RenderingIntentFlag.PRINT;break;default:(0,_util.warn)(`getRenderingIntent - invalid intent: ${l}`)}switch(h){case _util.AnnotationMode.DISABLE:v+=_util.RenderingIntentFlag.ANNOTATIONS_DISABLE;break;case _util.AnnotationMode.ENABLE:break;case _util.AnnotationMode.ENABLE_FORMS:v+=_util.RenderingIntentFlag.ANNOTATIONS_FORMS;break;case _util.AnnotationMode.ENABLE_STORAGE:v+=_util.RenderingIntentFlag.ANNOTATIONS_STORAGE,k=(v&_util.RenderingIntentFlag.PRINT&&F instanceof _annotation_storage.PrintAnnotationStorage?F:this.annotationStorage).serializable;break;default:(0,_util.warn)(`getRenderingIntent - invalid annotationMode: ${h}`)}return r&&(v+=_util.RenderingIntentFlag.OPLIST),{renderingIntent:v,cacheKey:`${v}_${k.hash}`,annotationStorageSerializable:k}}destroy(){var F;if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=new _util.PromiseCapability,(F=t(this,m))==null||F.reject(new Error("Worker was destroyed during onPassword callback"));const l=[];for(const r of t(this,I).values())l.push(r._destroy());t(this,I).clear(),t(this,y).clear(),this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const h=this.messageHandler.sendWithPromise("Terminate",null);return l.push(h),Promise.all(l).then(()=>{var r;this.commonObjs.clear(),this.fontLoader.clear(),t(this,D).clear(),this.filterFactory.destroy(),(r=this._networkStream)==null||r.cancelAllRequests(new _util.AbortException("Worker was terminated.")),this.messageHandler&&(this.messageHandler.destroy(),this.messageHandler=null),this.destroyCapability.resolve()},this.destroyCapability.reject),this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:l,loadingTask:h}=this;l.on("GetReader",(F,r)=>{(0,_util.assert)(this._networkStream,"GetReader - no `IPDFStream` instance available."),this._fullReader=this._networkStream.getFullReader(),this._fullReader.onProgress=v=>{this._lastProgress={loaded:v.loaded,total:v.total}},r.onPull=()=>{this._fullReader.read().then(function({value:v,done:k}){if(k){r.close();return}(0,_util.assert)(v instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer."),r.enqueue(new Uint8Array(v),1,[v])}).catch(v=>{r.error(v)})},r.onCancel=v=>{this._fullReader.cancel(v),r.ready.catch(k=>{if(!this.destroyed)throw k})}}),l.on("ReaderHeadersReady",F=>{const r=new _util.PromiseCapability,v=this._fullReader;return v.headersReady.then(()=>{var k;(!v.isStreamingSupported||!v.isRangeSupported)&&(this._lastProgress&&((k=h.onProgress)==null||k.call(h,this._lastProgress)),v.onProgress=Z=>{var $;($=h.onProgress)==null||$.call(h,{loaded:Z.loaded,total:Z.total})}),r.resolve({isStreamingSupported:v.isStreamingSupported,isRangeSupported:v.isRangeSupported,contentLength:v.contentLength})},r.reject),r.promise}),l.on("GetRangeReader",(F,r)=>{(0,_util.assert)(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const v=this._networkStream.getRangeReader(F.begin,F.end);if(!v){r.close();return}r.onPull=()=>{v.read().then(function({value:k,done:Z}){if(Z){r.close();return}(0,_util.assert)(k instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer."),r.enqueue(new Uint8Array(k),1,[k])}).catch(k=>{r.error(k)})},r.onCancel=k=>{v.cancel(k),r.ready.catch(Z=>{if(!this.destroyed)throw Z})}}),l.on("GetDoc",({pdfInfo:F})=>{this._numPages=F.numPages,this._htmlForXfa=F.htmlForXfa,delete F.htmlForXfa,h._capability.resolve(new PDFDocumentProxy(F,this))}),l.on("DocException",function(F){let r;switch(F.name){case"PasswordException":r=new _util.PasswordException(F.message,F.code);break;case"InvalidPDFException":r=new _util.InvalidPDFException(F.message);break;case"MissingPDFException":r=new _util.MissingPDFException(F.message);break;case"UnexpectedResponseException":r=new _util.UnexpectedResponseException(F.message,F.status);break;case"UnknownErrorException":r=new _util.UnknownErrorException(F.message,F.details);break;default:(0,_util.unreachable)("DocException - expected a valid Error.")}h._capability.reject(r)}),l.on("PasswordRequest",F=>{if(ot(this,m,new _util.PromiseCapability),h.onPassword){const r=v=>{v instanceof Error?t(this,m).reject(v):t(this,m).resolve({password:v})};try{h.onPassword(r,F.code)}catch(v){t(this,m).reject(v)}}else t(this,m).reject(new _util.PasswordException(F.message,F.code));return t(this,m).promise}),l.on("DataLoaded",F=>{var r;(r=h.onProgress)==null||r.call(h,{loaded:F.length,total:F.length}),this.downloadInfoCapability.resolve(F)}),l.on("StartRenderPage",F=>{if(this.destroyed)return;t(this,I).get(F.pageIndex)._startRenderPage(F.transparency,F.cacheKey)}),l.on("commonobj",([F,r,v])=>{var k;if(!this.destroyed&&!this.commonObjs.has(F))switch(r){case"Font":const Z=this._params;if("error"in v){const G=v.error;(0,_util.warn)(`Error during font loading: ${G}`),this.commonObjs.resolve(F,G);break}const $=Z.pdfBug&&((k=globalThis.FontInspector)!=null&&k.enabled)?(G,it)=>globalThis.FontInspector.fontAdded(G,it):null,q=new _font_loader.FontFaceObject(v,{isEvalSupported:Z.isEvalSupported,disableFontFace:Z.disableFontFace,ignoreErrors:Z.ignoreErrors,inspectFont:$});this.fontLoader.bind(q).catch(G=>l.sendWithPromise("FontFallback",{id:F})).finally(()=>{!Z.fontExtraProperties&&q.data&&(q.data=null),this.commonObjs.resolve(F,q)});break;case"FontPath":case"Image":case"Pattern":this.commonObjs.resolve(F,v);break;default:throw new Error(`Got unknown common object type ${r}`)}}),l.on("obj",([F,r,v,k])=>{var $;if(this.destroyed)return;const Z=t(this,I).get(r);if(!Z.objs.has(F))switch(v){case"Image":if(Z.objs.resolve(F,k),k){let q;if(k.bitmap){const{width:G,height:it}=k;q=G*it*4}else q=(($=k.data)==null?void 0:$.length)||0;q>_util.MAX_IMAGE_SIZE_TO_CACHE&&(Z._maybeCleanupAfterRender=!0)}break;case"Pattern":Z.objs.resolve(F,k);break;default:throw new Error(`Got unknown object type ${v}`)}}),l.on("DocProgress",F=>{var r;this.destroyed||(r=h.onProgress)==null||r.call(h,{loaded:F.loaded,total:F.total})}),l.on("FetchBuiltInCMap",F=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.cMapReaderFactory?this.cMapReaderFactory.fetch(F):Promise.reject(new Error("CMapReaderFactory not initialized, see the `useWorkerFetch` parameter."))),l.on("FetchStandardFontData",F=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.standardFontDataFactory?this.standardFontDataFactory.fetch(F):Promise.reject(new Error("StandardFontDataFactory not initialized, see the `useWorkerFetch` parameter.")))}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){var F;this.annotationStorage.size<=0&&(0,_util.warn)("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");const{map:l,transfers:h}=this.annotationStorage.serializable;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:l,filename:((F=this._fullReader)==null?void 0:F.filename)??null},h).finally(()=>{this.annotationStorage.resetModified()})}getPage(l){if(!Number.isInteger(l)||l<=0||l>this._numPages)return Promise.reject(new Error("Invalid page request."));const h=l-1,F=t(this,y).get(h);if(F)return F;const r=this.messageHandler.sendWithPromise("GetPage",{pageIndex:h}).then(v=>{if(this.destroyed)throw new Error("Transport destroyed");const k=new PDFPageProxy(h,v,this,this._params.pdfBug);return t(this,I).set(h,k),k});return t(this,y).set(h,r),r}getPageIndex(l){return typeof l!="object"||l===null||!Number.isInteger(l.num)||l.num<0||!Number.isInteger(l.gen)||l.gen<0?Promise.reject(new Error("Invalid pageIndex request.")):this.messageHandler.sendWithPromise("GetPageIndex",{num:l.num,gen:l.gen})}getAnnotations(l,h){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:l,intent:h})}getFieldObjects(){return K(this,E,ye).call(this,"GetFieldObjects")}hasJSActions(){return K(this,E,ye).call(this,"HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(l){return typeof l!="string"?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:l})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getDocJSActions(){return K(this,E,ye).call(this,"GetDocJSActions")}getPageJSActions(l){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:l})}getStructTree(l){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:l})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(){return this.messageHandler.sendWithPromise("GetOptionalContentConfig",null).then(l=>new _optional_content_config.OptionalContentConfig(l))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){const l="GetMetadata",h=t(this,D).get(l);if(h)return h;const F=this.messageHandler.sendWithPromise(l,null).then(r=>{var v,k;return{info:r[0],metadata:r[1]?new _metadata.Metadata(r[1]):null,contentDispositionFilename:((v=this._fullReader)==null?void 0:v.filename)??null,contentLength:((k=this._fullReader)==null?void 0:k.contentLength)??null}});return t(this,D).set(l,F),F}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(l=!1){if(!this.destroyed){await this.messageHandler.sendWithPromise("Cleanup",null);for(const h of t(this,I).values())if(!h.cleanup())throw new Error(`startCleanup: Page ${h.pageNumber} is currently rendering.`);this.commonObjs.clear(),l||this.fontLoader.clear(),t(this,D).clear(),this.filterFactory.destroy(!0)}}get loadingParams(){const{disableAutoFetch:l,enableXfa:h}=this._params;return(0,_util.shadow)(this,"loadingParams",{disableAutoFetch:l,enableXfa:h})}}D=new WeakMap,I=new WeakMap,y=new WeakMap,m=new WeakMap,E=new WeakSet,ye=function(l,h=null){const F=t(this,D).get(l);if(F)return F;const r=this.messageHandler.sendWithPromise(l,h);return t(this,D).set(l,r),r};class PDFObjects{constructor(){rt(this,b);rt(this,u,Object.create(null))}get(l,h=null){if(h){const r=K(this,b,De).call(this,l);return r.capability.promise.then(()=>h(r.data)),null}const F=t(this,u)[l];if(!(F!=null&&F.capability.settled))throw new Error(`Requesting object that isn't resolved yet ${l}.`);return F.data}has(l){const h=t(this,u)[l];return(h==null?void 0:h.capability.settled)||!1}resolve(l,h=null){const F=K(this,b,De).call(this,l);F.data=h,F.capability.resolve()}clear(){var l;for(const h in t(this,u)){const{data:F}=t(this,u)[h];(l=F==null?void 0:F.bitmap)==null||l.close()}ot(this,u,Object.create(null))}}u=new WeakMap,b=new WeakSet,De=function(l){var h;return(h=t(this,u))[l]||(h[l]={capability:new _util.PromiseCapability,data:null})};class RenderTask{constructor(l){rt(this,_,null);ot(this,_,l),this.onContinue=null}get promise(){return t(this,_).capability.promise}cancel(l=0){t(this,_).cancel(null,l)}get separateAnnots(){const{separateAnnots:l}=t(this,_).operatorList;if(!l)return!1;const{annotationCanvasMap:h}=t(this,_);return l.form||l.canvas&&(h==null?void 0:h.size)>0}}_=new WeakMap,exports.RenderTask=RenderTask;const c=class c{constructor({callback:l,params:h,objs:F,commonObjs:r,annotationCanvasMap:v,operatorList:k,pageIndex:Z,canvasFactory:$,filterFactory:q,useRequestAnimationFrame:G=!1,pdfBug:it=!1,pageColors:R=null}){this.callback=l,this.params=h,this.objs=F,this.commonObjs=r,this.annotationCanvasMap=v,this.operatorListIdx=null,this.operatorList=k,this._pageIndex=Z,this.canvasFactory=$,this.filterFactory=q,this._pdfBug=it,this.pageColors=R,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this._useRequestAnimationFrame=G===!0&&typeof window<"u",this.cancelled=!1,this.capability=new _util.PromiseCapability,this.task=new RenderTask(this),this._cancelBound=this.cancel.bind(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=h.canvasContext.canvas}get completed(){return this.capability.promise.catch(function(){})}initializeGraphics({transparency:l=!1,optionalContentConfig:h}){var Z,$;if(this.cancelled)return;if(this._canvas){if(t(c,i).has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");t(c,i).add(this._canvas)}this._pdfBug&&((Z=globalThis.StepperManager)!=null&&Z.enabled)&&(this.stepper=globalThis.StepperManager.create(this._pageIndex),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());const{canvasContext:F,viewport:r,transform:v,background:k}=this.params;this.gfx=new _canvas.CanvasGraphics(F,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:h},this.annotationCanvasMap,this.pageColors),this.gfx.beginDrawing({transform:v,viewport:r,transparency:l,background:k}),this.operatorListIdx=0,this.graphicsReady=!0,($=this.graphicsReadyCallback)==null||$.call(this)}cancel(l=null,h=0){var F;this.running=!1,this.cancelled=!0,(F=this.gfx)==null||F.endDrawing(),t(c,i).delete(this._canvas),this.callback(l||new _display_utils.RenderingCancelledException(`Rendering cancelled, page ${this._pageIndex+1}`,h))}operatorListChanged(){var l;if(!this.graphicsReady){this.graphicsReadyCallback||(this.graphicsReadyCallback=this._continueBound);return}(l=this.stepper)==null||l.updateOperatorList(this.operatorList),!this.running&&this._continue()}_continue(){this.running=!0,!this.cancelled&&(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?window.requestAnimationFrame(()=>{this._nextBound().catch(this._cancelBound)}):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){this.cancelled||(this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),t(c,i).delete(this._canvas),this.callback())))}};i=new WeakMap,rt(c,i,new WeakSet);let InternalRenderTask=c;const version="3.11.174";exports.version=version;const build="ce8716743";exports.build=build},(T,e,B)=>{var x,f,D,mn,y;Object.defineProperty(e,"__esModule",{value:!0}),e.SerializableEmpty=e.PrintAnnotationStorage=e.AnnotationStorage=void 0;var n=B(1),d=B(4),H=B(8);const W=Object.freeze({map:null,hash:"",transfers:void 0});e.SerializableEmpty=W;class ht{constructor(){rt(this,D);rt(this,x,!1);rt(this,f,new Map);this.onSetModified=null,this.onResetModified=null,this.onAnnotationEditor=null}getValue(E,p){const u=t(this,f).get(E);return u===void 0?p:Object.assign(p,u)}getRawValue(E){return t(this,f).get(E)}remove(E){if(t(this,f).delete(E),t(this,f).size===0&&this.resetModified(),typeof this.onAnnotationEditor=="function"){for(const p of t(this,f).values())if(p instanceof d.AnnotationEditor)return;this.onAnnotationEditor(null)}}setValue(E,p){const u=t(this,f).get(E);let b=!1;if(u!==void 0)for(const[C,_]of Object.entries(p))u[C]!==_&&(b=!0,u[C]=_);else b=!0,t(this,f).set(E,p);b&&K(this,D,mn).call(this),p instanceof d.AnnotationEditor&&typeof this.onAnnotationEditor=="function"&&this.onAnnotationEditor(p.constructor._type)}has(E){return t(this,f).has(E)}getAll(){return t(this,f).size>0?(0,n.objectFromMap)(t(this,f)):null}setAll(E){for(const[p,u]of Object.entries(E))this.setValue(p,u)}get size(){return t(this,f).size}resetModified(){t(this,x)&&(ot(this,x,!1),typeof this.onResetModified=="function"&&this.onResetModified())}get print(){return new P(this)}get serializable(){if(t(this,f).size===0)return W;const E=new Map,p=new H.MurmurHash3_64,u=[],b=Object.create(null);let C=!1;for(const[_,i]of t(this,f)){const c=i instanceof d.AnnotationEditor?i.serialize(!1,b):i;c&&(E.set(_,c),p.update(`${_}:${JSON.stringify(c)}`),C||(C=!!c.bitmap))}if(C)for(const _ of E.values())_.bitmap&&u.push(_.bitmap);return E.size>0?{map:E,hash:p.hexdigest(),transfers:u}:W}}x=new WeakMap,f=new WeakMap,D=new WeakSet,mn=function(){t(this,x)||(ot(this,x,!0),typeof this.onSetModified=="function"&&this.onSetModified())},e.AnnotationStorage=ht;class P extends ht{constructor(p){super();rt(this,y);const{map:u,hash:b,transfers:C}=p.serializable,_=structuredClone(u,C?{transfer:C}:null);ot(this,y,{map:_,hash:b,transfers:C})}get print(){(0,n.unreachable)("Should not call PrintAnnotationStorage.print")}get serializable(){return t(this,y)}}y=new WeakMap,e.PrintAnnotationStorage=P},(T,e,B)=>{var P,x,f,D,I,y,m,E,p,u,b,C,_,i,c,Le,Oe,h,Ie,Ne,bn,_n,yn,je,An;Object.defineProperty(e,"__esModule",{value:!0}),e.AnnotationEditor=void 0;var n=B(5),d=B(1),H=B(6);const G=class G{constructor(R){rt(this,c);rt(this,P,"");rt(this,x,!1);rt(this,f,null);rt(this,D,null);rt(this,I,null);rt(this,y,!1);rt(this,m,null);rt(this,E,this.focusin.bind(this));rt(this,p,this.focusout.bind(this));rt(this,u,!1);rt(this,b,!1);rt(this,C,!1);Kt(this,"_initialOptions",Object.create(null));Kt(this,"_uiManager",null);Kt(this,"_focusEventsAllowed",!0);Kt(this,"_l10nPromise",null);rt(this,_,!1);rt(this,i,G._zIndex++);this.constructor===G&&(0,d.unreachable)("Cannot initialize AnnotationEditor."),this.parent=R.parent,this.id=R.id,this.width=this.height=null,this.pageIndex=R.parent.pageIndex,this.name=R.name,this.div=null,this._uiManager=R.uiManager,this.annotationElementId=null,this._willKeepAspectRatio=!1,this._initialOptions.isCentered=R.isCentered,this._structTreeParentId=null;const{rotation:V,rawDims:{pageWidth:et,pageHeight:S,pageX:s,pageY:a}}=this.parent.viewport;this.rotation=V,this.pageRotation=(360+V-this._uiManager.viewParameters.rotation)%360,this.pageDimensions=[et,S],this.pageTranslation=[s,a];const[g,L]=this.parentDimensions;this.x=R.x/g,this.y=R.y/L,this.isAttachedToDOM=!1,this.deleted=!1}get editorType(){return Object.getPrototypeOf(this).constructor._type}static get _defaultLineColor(){return(0,d.shadow)(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}static deleteAnnotationElement(R){const V=new ht({id:R.parent.getNextId(),parent:R.parent,uiManager:R._uiManager});V.annotationElementId=R.annotationElementId,V.deleted=!0,V._uiManager.addToAnnotationStorage(V)}static initialize(R,V=null){if(G._l10nPromise||(G._l10nPromise=new Map(["editor_alt_text_button_label","editor_alt_text_edit_button_label","editor_alt_text_decorative_tooltip"].map(S=>[S,R.get(S)]))),V!=null&&V.strings)for(const S of V.strings)G._l10nPromise.set(S,R.get(S));if(G._borderLineWidth!==-1)return;const et=getComputedStyle(document.documentElement);G._borderLineWidth=parseFloat(et.getPropertyValue("--outline-width"))||0}static updateDefaultParams(R,V){}static get defaultPropertiesToUpdate(){return[]}static isHandlingMimeForPasting(R){return!1}static paste(R,V){(0,d.unreachable)("Not implemented")}get propertiesToUpdate(){return[]}get _isDraggable(){return t(this,_)}set _isDraggable(R){var V;ot(this,_,R),(V=this.div)==null||V.classList.toggle("draggable",R)}center(){const[R,V]=this.pageDimensions;switch(this.parentRotation){case 90:this.x-=this.height*V/(R*2),this.y+=this.width*R/(V*2);break;case 180:this.x+=this.width/2,this.y+=this.height/2;break;case 270:this.x+=this.height*V/(R*2),this.y-=this.width*R/(V*2);break;default:this.x-=this.width/2,this.y-=this.height/2;break}this.fixAndSetPosition()}addCommands(R){this._uiManager.addCommands(R)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=t(this,i)}setParent(R){R!==null&&(this.pageIndex=R.pageIndex,this.pageDimensions=R.pageDimensions),this.parent=R}focusin(R){this._focusEventsAllowed&&(t(this,u)?ot(this,u,!1):this.parent.setSelected(this))}focusout(R){var et;if(!this._focusEventsAllowed||!this.isAttachedToDOM)return;const V=R.relatedTarget;V!=null&&V.closest(`#${this.id}`)||(R.preventDefault(),(et=this.parent)!=null&&et.isMultipleSelection||this.commitOrRemove())}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}setAt(R,V,et,S){const[s,a]=this.parentDimensions;[et,S]=this.screenToPageTranslation(et,S),this.x=(R+et)/s,this.y=(V+S)/a,this.fixAndSetPosition()}translate(R,V){K(this,c,Le).call(this,this.parentDimensions,R,V)}translateInPage(R,V){K(this,c,Le).call(this,this.pageDimensions,R,V),this.div.scrollIntoView({block:"nearest"})}drag(R,V){const[et,S]=this.parentDimensions;if(this.x+=R/et,this.y+=V/S,this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){const{x:O,y:N}=this.div.getBoundingClientRect();this.parent.findNewParent(this,O,N)&&(this.x-=Math.floor(this.x),this.y-=Math.floor(this.y))}let{x:s,y:a}=this;const[g,L]=K(this,c,Oe).call(this);s+=g,a+=L,this.div.style.left=`${(100*s).toFixed(2)}%`,this.div.style.top=`${(100*a).toFixed(2)}%`,this.div.scrollIntoView({block:"nearest"})}fixAndSetPosition(){const[R,V]=this.pageDimensions;let{x:et,y:S,width:s,height:a}=this;switch(s*=R,a*=V,et*=R,S*=V,this.rotation){case 0:et=Math.max(0,Math.min(R-s,et)),S=Math.max(0,Math.min(V-a,S));break;case 90:et=Math.max(0,Math.min(R-a,et)),S=Math.min(V,Math.max(s,S));break;case 180:et=Math.min(R,Math.max(s,et)),S=Math.min(V,Math.max(a,S));break;case 270:et=Math.min(R,Math.max(a,et)),S=Math.max(0,Math.min(V-s,S));break}this.x=et/=R,this.y=S/=V;const[g,L]=K(this,c,Oe).call(this);et+=g,S+=L;const{style:O}=this.div;O.left=`${(100*et).toFixed(2)}%`,O.top=`${(100*S).toFixed(2)}%`,this.moveInDOM()}screenToPageTranslation(R,V){var et;return K(et=G,h,Ie).call(et,R,V,this.parentRotation)}pageTranslationToScreen(R,V){var et;return K(et=G,h,Ie).call(et,R,V,360-this.parentRotation)}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}get parentDimensions(){const{parentScale:R,pageDimensions:[V,et]}=this,S=V*R,s=et*R;return d.FeatureTest.isCSSRoundSupported?[Math.round(S),Math.round(s)]:[S,s]}setDims(R,V){var s;const[et,S]=this.parentDimensions;this.div.style.width=`${(100*R/et).toFixed(2)}%`,t(this,y)||(this.div.style.height=`${(100*V/S).toFixed(2)}%`),(s=t(this,f))==null||s.classList.toggle("small",R<G.SMALL_EDITOR_SIZE||V<G.SMALL_EDITOR_SIZE)}fixDims(){const{style:R}=this.div,{height:V,width:et}=R,S=et.endsWith("%"),s=!t(this,y)&&V.endsWith("%");if(S&&s)return;const[a,g]=this.parentDimensions;S||(R.width=`${(100*parseFloat(et)/a).toFixed(2)}%`),!t(this,y)&&!s&&(R.height=`${(100*parseFloat(V)/g).toFixed(2)}%`)}getInitialTranslation(){return[0,0]}async addAltTextButton(){if(t(this,f))return;const R=ot(this,f,document.createElement("button"));R.className="altText";const V=await G._l10nPromise.get("editor_alt_text_button_label");R.textContent=V,R.setAttribute("aria-label",V),R.tabIndex="0",R.addEventListener("contextmenu",H.noContextMenu),R.addEventListener("pointerdown",et=>et.stopPropagation()),R.addEventListener("click",et=>{et.preventDefault(),this._uiManager.editAltText(this)},{capture:!0}),R.addEventListener("keydown",et=>{et.target===R&&et.key==="Enter"&&(et.preventDefault(),this._uiManager.editAltText(this))}),K(this,c,je).call(this),this.div.append(R),G.SMALL_EDITOR_SIZE||(G.SMALL_EDITOR_SIZE=Math.min(128,Math.round(R.getBoundingClientRect().width*1.4)))}getClientDimensions(){return this.div.getBoundingClientRect()}get altTextData(){return{altText:t(this,P),decorative:t(this,x)}}set altTextData({altText:R,decorative:V}){t(this,P)===R&&t(this,x)===V||(ot(this,P,R),ot(this,x,V),K(this,c,je).call(this))}render(){this.div=document.createElement("div"),this.div.setAttribute("data-editor-rotation",(360-this.rotation)%360),this.div.className=this.name,this.div.setAttribute("id",this.id),this.div.setAttribute("tabIndex",0),this.setInForeground(),this.div.addEventListener("focusin",t(this,E)),this.div.addEventListener("focusout",t(this,p));const[R,V]=this.parentDimensions;this.parentRotation%180!==0&&(this.div.style.maxWidth=`${(100*V/R).toFixed(2)}%`,this.div.style.maxHeight=`${(100*R/V).toFixed(2)}%`);const[et,S]=this.getInitialTranslation();return this.translate(et,S),(0,n.bindEvents)(this,this.div,["pointerdown"]),this.div}pointerdown(R){const{isMac:V}=d.FeatureTest.platform;if(R.button!==0||R.ctrlKey&&V){R.preventDefault();return}ot(this,u,!0),K(this,c,An).call(this,R)}moveInDOM(){var R;(R=this.parent)==null||R.moveEditorInDOM(this)}_setParentAndPosition(R,V,et){R.changeParent(this),this.x=V,this.y=et,this.fixAndSetPosition()}getRect(R,V){const et=this.parentScale,[S,s]=this.pageDimensions,[a,g]=this.pageTranslation,L=R/et,O=V/et,N=this.x*S,X=this.y*s,nt=this.width*S,ct=this.height*s;switch(this.rotation){case 0:return[N+L+a,s-X-O-ct+g,N+L+nt+a,s-X-O+g];case 90:return[N+O+a,s-X+L+g,N+O+ct+a,s-X+L+nt+g];case 180:return[N-L-nt+a,s-X+O+g,N-L+a,s-X+O+ct+g];case 270:return[N-O-ct+a,s-X-L-nt+g,N-O+a,s-X-L+g];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(R,V){const[et,S,s,a]=R,g=s-et,L=a-S;switch(this.rotation){case 0:return[et,V-a,g,L];case 90:return[et,V-S,L,g];case 180:return[s,V-S,g,L];case 270:return[s,V-a,L,g];default:throw new Error("Invalid rotation")}}onceAdded(){}isEmpty(){return!1}enableEditMode(){ot(this,C,!0)}disableEditMode(){ot(this,C,!1)}isInEditMode(){return t(this,C)}shouldGetKeyboardEvents(){return!1}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}rebuild(){var R,V;(R=this.div)==null||R.addEventListener("focusin",t(this,E)),(V=this.div)==null||V.addEventListener("focusout",t(this,p))}serialize(R=!1,V=null){(0,d.unreachable)("An editor must be serializable")}static deserialize(R,V,et){const S=new this.prototype.constructor({parent:V,id:V.getNextId(),uiManager:et});S.rotation=R.rotation;const[s,a]=S.pageDimensions,[g,L,O,N]=S.getRectInCurrentCoords(R.rect,a);return S.x=g/s,S.y=L/a,S.width=O/s,S.height=N/a,S}remove(){var R;this.div.removeEventListener("focusin",t(this,E)),this.div.removeEventListener("focusout",t(this,p)),this.isEmpty()||this.commit(),this.parent?this.parent.remove(this):this._uiManager.removeEditor(this),(R=t(this,f))==null||R.remove(),ot(this,f,null),ot(this,D,null)}get isResizable(){return!1}makeResizable(){this.isResizable&&(K(this,c,bn).call(this),t(this,m).classList.remove("hidden"))}select(){var R;this.makeResizable(),(R=this.div)==null||R.classList.add("selectedEditor")}unselect(){var R,V,et;(R=t(this,m))==null||R.classList.add("hidden"),(V=this.div)==null||V.classList.remove("selectedEditor"),(et=this.div)!=null&&et.contains(document.activeElement)&&this._uiManager.currentLayer.div.focus()}updateParams(R,V){}disableEditing(){t(this,f)&&(t(this,f).hidden=!0)}enableEditing(){t(this,f)&&(t(this,f).hidden=!1)}enterInEditMode(){}get contentDiv(){return this.div}get isEditing(){return t(this,b)}set isEditing(R){ot(this,b,R),this.parent&&(R?(this.parent.setSelected(this),this.parent.setActiveEditor(this)):this.parent.setActiveEditor(null))}setAspectRatio(R,V){ot(this,y,!0);const et=R/V,{style:S}=this.div;S.aspectRatio=et,S.height="auto"}static get MIN_SIZE(){return 16}};P=new WeakMap,x=new WeakMap,f=new WeakMap,D=new WeakMap,I=new WeakMap,y=new WeakMap,m=new WeakMap,E=new WeakMap,p=new WeakMap,u=new WeakMap,b=new WeakMap,C=new WeakMap,_=new WeakMap,i=new WeakMap,c=new WeakSet,Le=function([R,V],et,S){[et,S]=this.screenToPageTranslation(et,S),this.x+=et/R,this.y+=S/V,this.fixAndSetPosition()},Oe=function(){const[R,V]=this.parentDimensions,{_borderLineWidth:et}=G,S=et/R,s=et/V;switch(this.rotation){case 90:return[-S,s];case 180:return[S,s];case 270:return[S,-s];default:return[-S,-s]}},h=new WeakSet,Ie=function(R,V,et){switch(et){case 90:return[V,-R];case 180:return[-R,-V];case 270:return[-V,R];default:return[R,V]}},Ne=function(R){switch(R){case 90:{const[V,et]=this.pageDimensions;return[0,-V/et,et/V,0]}case 180:return[-1,0,0,-1];case 270:{const[V,et]=this.pageDimensions;return[0,V/et,-et/V,0]}default:return[1,0,0,1]}},bn=function(){if(t(this,m))return;ot(this,m,document.createElement("div")),t(this,m).classList.add("resizers");const R=["topLeft","topRight","bottomRight","bottomLeft"];this._willKeepAspectRatio||R.push("topMiddle","middleRight","bottomMiddle","middleLeft");for(const V of R){const et=document.createElement("div");t(this,m).append(et),et.classList.add("resizer",V),et.addEventListener("pointerdown",K(this,c,_n).bind(this,V)),et.addEventListener("contextmenu",H.noContextMenu)}this.div.prepend(t(this,m))},_n=function(R,V){V.preventDefault();const{isMac:et}=d.FeatureTest.platform;if(V.button!==0||V.ctrlKey&&et)return;const S=K(this,c,yn).bind(this,R),s=this._isDraggable;this._isDraggable=!1;const a={passive:!0,capture:!0};window.addEventListener("pointermove",S,a);const g=this.x,L=this.y,O=this.width,N=this.height,X=this.parent.div.style.cursor,nt=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(V.target).cursor;const ct=()=>{this._isDraggable=s,window.removeEventListener("pointerup",ct),window.removeEventListener("blur",ct),window.removeEventListener("pointermove",S,a),this.parent.div.style.cursor=X,this.div.style.cursor=nt;const ft=this.x,bt=this.y,mt=this.width,_t=this.height;ft===g&&bt===L&&mt===O&&_t===N||this.addCommands({cmd:()=>{this.width=mt,this.height=_t,this.x=ft,this.y=bt;const[J,Q]=this.parentDimensions;this.setDims(J*mt,Q*_t),this.fixAndSetPosition()},undo:()=>{this.width=O,this.height=N,this.x=g,this.y=L;const[J,Q]=this.parentDimensions;this.setDims(J*O,Q*N),this.fixAndSetPosition()},mustExec:!0})};window.addEventListener("pointerup",ct),window.addEventListener("blur",ct)},yn=function(R,V){const[et,S]=this.parentDimensions,s=this.x,a=this.y,g=this.width,L=this.height,O=G.MIN_SIZE/et,N=G.MIN_SIZE/S,X=kt=>Math.round(kt*1e4)/1e4,nt=K(this,c,Ne).call(this,this.rotation),ct=(kt,Ot)=>[nt[0]*kt+nt[2]*Ot,nt[1]*kt+nt[3]*Ot],ft=K(this,c,Ne).call(this,360-this.rotation),bt=(kt,Ot)=>[ft[0]*kt+ft[2]*Ot,ft[1]*kt+ft[3]*Ot];let mt,_t,J=!1,Q=!1;switch(R){case"topLeft":J=!0,mt=(kt,Ot)=>[0,0],_t=(kt,Ot)=>[kt,Ot];break;case"topMiddle":mt=(kt,Ot)=>[kt/2,0],_t=(kt,Ot)=>[kt/2,Ot];break;case"topRight":J=!0,mt=(kt,Ot)=>[kt,0],_t=(kt,Ot)=>[0,Ot];break;case"middleRight":Q=!0,mt=(kt,Ot)=>[kt,Ot/2],_t=(kt,Ot)=>[0,Ot/2];break;case"bottomRight":J=!0,mt=(kt,Ot)=>[kt,Ot],_t=(kt,Ot)=>[0,0];break;case"bottomMiddle":mt=(kt,Ot)=>[kt/2,Ot],_t=(kt,Ot)=>[kt/2,0];break;case"bottomLeft":J=!0,mt=(kt,Ot)=>[0,Ot],_t=(kt,Ot)=>[kt,0];break;case"middleLeft":Q=!0,mt=(kt,Ot)=>[0,Ot/2],_t=(kt,Ot)=>[kt,Ot/2];break}const A=mt(g,L),j=_t(g,L);let Y=ct(...j);const st=X(s+Y[0]),pt=X(a+Y[1]);let Et=1,At=1,[tt,wt]=this.screenToPageTranslation(V.movementX,V.movementY);if([tt,wt]=bt(tt/et,wt/S),J){const kt=Math.hypot(g,L);Et=At=Math.max(Math.min(Math.hypot(j[0]-A[0]-tt,j[1]-A[1]-wt)/kt,1/g,1/L),O/g,N/L)}else Q?Et=Math.max(O,Math.min(1,Math.abs(j[0]-A[0]-tt)))/g:At=Math.max(N,Math.min(1,Math.abs(j[1]-A[1]-wt)))/L;const xt=X(g*Et),Ut=X(L*At);Y=ct(..._t(xt,Ut));const jt=st-Y[0],Vt=pt-Y[1];this.width=xt,this.height=Ut,this.x=jt,this.y=Vt,this.setDims(et*xt,S*Ut),this.fixAndSetPosition()},je=async function(){var et;const R=t(this,f);if(!R)return;if(!t(this,P)&&!t(this,x)){R.classList.remove("done"),(et=t(this,D))==null||et.remove();return}G._l10nPromise.get("editor_alt_text_edit_button_label").then(S=>{R.setAttribute("aria-label",S)});let V=t(this,D);if(!V){ot(this,D,V=document.createElement("span")),V.className="tooltip",V.setAttribute("role","tooltip");const S=V.id=`alt-text-tooltip-${this.id}`;R.setAttribute("aria-describedby",S);const s=100;R.addEventListener("mouseenter",()=>{ot(this,I,setTimeout(()=>{ot(this,I,null),t(this,D).classList.add("show"),this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",subtype:this.editorType,data:{action:"alt_text_tooltip"}}})},s))}),R.addEventListener("mouseleave",()=>{var a;clearTimeout(t(this,I)),ot(this,I,null),(a=t(this,D))==null||a.classList.remove("show")})}R.classList.add("done"),V.innerText=t(this,x)?await G._l10nPromise.get("editor_alt_text_decorative_tooltip"):t(this,P),V.parentNode||R.append(V)},An=function(R){if(!this._isDraggable)return;const V=this._uiManager.isSelected(this);this._uiManager.setUpDragSession();let et,S;V&&(et={passive:!0,capture:!0},S=a=>{const[g,L]=this.screenToPageTranslation(a.movementX,a.movementY);this._uiManager.dragSelectedEditors(g,L)},window.addEventListener("pointermove",S,et));const s=()=>{if(window.removeEventListener("pointerup",s),window.removeEventListener("blur",s),V&&window.removeEventListener("pointermove",S,et),ot(this,u,!1),!this._uiManager.endDragSession()){const{isMac:a}=d.FeatureTest.platform;R.ctrlKey&&!a||R.shiftKey||R.metaKey&&a?this.parent.toggleSelected(this):this.parent.setSelected(this)}};window.addEventListener("pointerup",s),window.addEventListener("blur",s)},rt(G,h),Kt(G,"_borderLineWidth",-1),Kt(G,"_colorManager",new n.ColorManager),Kt(G,"_zIndex",1),Kt(G,"SMALL_EDITOR_SIZE",0);let W=G;e.AnnotationEditor=W;class ht extends W{constructor(R){super(R),this.annotationElementId=R.annotationElementId,this.deleted=!0}serialize(){return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex}}}},(T,e,B)=>{var y,m,E,p,u,Be,_,i,c,o,l,vn,r,v,k,Z,$,q,G,it,R,V,et,S,s,a,g,L,O,N,X,nt,ct,ft,bt,mt,_t,J,Q,A,j,Y,st,pt,Et,At,tt,En,Ue,We,Ae,He,Ge,Zt,de,Sn,xn,$e,ue,Ve;Object.defineProperty(e,"__esModule",{value:!0}),e.KeyboardManager=e.CommandManager=e.ColorManager=e.AnnotationEditorUIManager=void 0,e.bindEvents=H,e.opacityToHex=W;var n=B(1),d=B(6);function H($t,U,gt){for(const Tt of gt)U.addEventListener(Tt,$t[Tt].bind($t))}function W($t){return Math.round(Math.min(255,Math.max(1,255*$t))).toString(16).padStart(2,"0")}class ht{constructor(){rt(this,y,0)}getId(){return`${n.AnnotationEditorPrefix}${he(this,y)._++}`}}y=new WeakMap;const C=class C{constructor(){rt(this,u);rt(this,m,(0,n.getUuid)());rt(this,E,0);rt(this,p,null)}static get _isSVGFittingCanvas(){const U='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>',Tt=new OffscreenCanvas(1,3).getContext("2d"),Dt=new Image;Dt.src=U;const Nt=Dt.decode().then(()=>(Tt.drawImage(Dt,0,0,1,1,0,0,1,3),new Uint32Array(Tt.getImageData(0,0,1,1).data.buffer)[0]===0));return(0,n.shadow)(this,"_isSVGFittingCanvas",Nt)}async getFromFile(U){const{lastModified:gt,name:Tt,size:Dt,type:Nt}=U;return K(this,u,Be).call(this,`${gt}_${Tt}_${Dt}_${Nt}`,U)}async getFromUrl(U){return K(this,u,Be).call(this,U,U)}async getFromId(U){t(this,p)||ot(this,p,new Map);const gt=t(this,p).get(U);return gt?gt.bitmap?(gt.refCounter+=1,gt):gt.file?this.getFromFile(gt.file):this.getFromUrl(gt.url):null}getSvgUrl(U){const gt=t(this,p).get(U);return gt!=null&&gt.isSvg?gt.svgUrl:null}deleteId(U){t(this,p)||ot(this,p,new Map);const gt=t(this,p).get(U);gt&&(gt.refCounter-=1,gt.refCounter===0&&(gt.bitmap=null))}isValidId(U){return U.startsWith(`image_${t(this,m)}_`)}};m=new WeakMap,E=new WeakMap,p=new WeakMap,u=new WeakSet,Be=async function(U,gt){t(this,p)||ot(this,p,new Map);let Tt=t(this,p).get(U);if(Tt===null)return null;if(Tt!=null&&Tt.bitmap)return Tt.refCounter+=1,Tt;try{Tt||(Tt={bitmap:null,id:`image_${t(this,m)}_${he(this,E)._++}`,refCounter:0,isSvg:!1});let Dt;if(typeof gt=="string"){Tt.url=gt;const Nt=await fetch(gt);if(!Nt.ok)throw new Error(Nt.statusText);Dt=await Nt.blob()}else Dt=Tt.file=gt;if(Dt.type==="image/svg+xml"){const Nt=C._isSVGFittingCanvas,Ct=new FileReader,M=new Image,w=new Promise((z,dt)=>{M.onload=()=>{Tt.bitmap=M,Tt.isSvg=!0,z()},Ct.onload=async()=>{const ut=Tt.svgUrl=Ct.result;M.src=await Nt?`${ut}#svgView(preserveAspectRatio(none))`:ut},M.onerror=Ct.onerror=dt});Ct.readAsDataURL(Dt),await w}else Tt.bitmap=await createImageBitmap(Dt);Tt.refCounter=1}catch(Dt){console.error(Dt),Tt=null}return t(this,p).set(U,Tt),Tt&&t(this,p).set(Tt.id,Tt),Tt};let P=C;class x{constructor(U=128){rt(this,_,[]);rt(this,i,!1);rt(this,c);rt(this,o,-1);ot(this,c,U)}add({cmd:U,undo:gt,mustExec:Tt,type:Dt=NaN,overwriteIfSameType:Nt=!1,keepUndo:Ct=!1}){if(Tt&&U(),t(this,i))return;const M={cmd:U,undo:gt,type:Dt};if(t(this,o)===-1){t(this,_).length>0&&(t(this,_).length=0),ot(this,o,0),t(this,_).push(M);return}if(Nt&&t(this,_)[t(this,o)].type===Dt){Ct&&(M.undo=t(this,_)[t(this,o)].undo),t(this,_)[t(this,o)]=M;return}const w=t(this,o)+1;w===t(this,c)?t(this,_).splice(0,1):(ot(this,o,w),w<t(this,_).length&&t(this,_).splice(w)),t(this,_).push(M)}undo(){t(this,o)!==-1&&(ot(this,i,!0),t(this,_)[t(this,o)].undo(),ot(this,i,!1),ot(this,o,t(this,o)-1))}redo(){t(this,o)<t(this,_).length-1&&(ot(this,o,t(this,o)+1),ot(this,i,!0),t(this,_)[t(this,o)].cmd(),ot(this,i,!1))}hasSomethingToUndo(){return t(this,o)!==-1}hasSomethingToRedo(){return t(this,o)<t(this,_).length-1}destroy(){ot(this,_,null)}}_=new WeakMap,i=new WeakMap,c=new WeakMap,o=new WeakMap,e.CommandManager=x;class f{constructor(U){rt(this,l);this.buffer=[],this.callbacks=new Map,this.allKeys=new Set;const{isMac:gt}=n.FeatureTest.platform;for(const[Tt,Dt,Nt={}]of U)for(const Ct of Tt){const M=Ct.startsWith("mac+");gt&&M?(this.callbacks.set(Ct.slice(4),{callback:Dt,options:Nt}),this.allKeys.add(Ct.split("+").at(-1))):!gt&&!M&&(this.callbacks.set(Ct,{callback:Dt,options:Nt}),this.allKeys.add(Ct.split("+").at(-1)))}}exec(U,gt){if(!this.allKeys.has(gt.key))return;const Tt=this.callbacks.get(K(this,l,vn).call(this,gt));if(!Tt)return;const{callback:Dt,options:{bubbles:Nt=!1,args:Ct=[],checker:M=null}}=Tt;M&&!M(U,gt)||(Dt.bind(U,...Ct)(),Nt||(gt.stopPropagation(),gt.preventDefault()))}}l=new WeakSet,vn=function(U){U.altKey&&this.buffer.push("alt"),U.ctrlKey&&this.buffer.push("ctrl"),U.metaKey&&this.buffer.push("meta"),U.shiftKey&&this.buffer.push("shift"),this.buffer.push(U.key);const gt=this.buffer.join("+");return this.buffer.length=0,gt},e.KeyboardManager=f;const F=class F{get _colors(){const U=new Map([["CanvasText",null],["Canvas",null]]);return(0,d.getColorValues)(U),(0,n.shadow)(this,"_colors",U)}convert(U){const gt=(0,d.getRGB)(U);if(!window.matchMedia("(forced-colors: active)").matches)return gt;for(const[Tt,Dt]of this._colors)if(Dt.every((Nt,Ct)=>Nt===gt[Ct]))return F._colorsMapping.get(Tt);return gt}getHexCode(U){const gt=this._colors.get(U);return gt?n.Util.makeHexColor(...gt):U}};Kt(F,"_colorsMapping",new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]));let D=F;e.ColorManager=D;const zt=class zt{constructor(U,gt,Tt,Dt,Nt,Ct){rt(this,tt);rt(this,r,null);rt(this,v,new Map);rt(this,k,new Map);rt(this,Z,null);rt(this,$,null);rt(this,q,new x);rt(this,G,0);rt(this,it,new Set);rt(this,R,null);rt(this,V,null);rt(this,et,new Set);rt(this,S,null);rt(this,s,new ht);rt(this,a,!1);rt(this,g,!1);rt(this,L,null);rt(this,O,n.AnnotationEditorType.NONE);rt(this,N,new Set);rt(this,X,null);rt(this,nt,this.blur.bind(this));rt(this,ct,this.focus.bind(this));rt(this,ft,this.copy.bind(this));rt(this,bt,this.cut.bind(this));rt(this,mt,this.paste.bind(this));rt(this,_t,this.keydown.bind(this));rt(this,J,this.onEditingAction.bind(this));rt(this,Q,this.onPageChanging.bind(this));rt(this,A,this.onScaleChanging.bind(this));rt(this,j,this.onRotationChanging.bind(this));rt(this,Y,{isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1});rt(this,st,[0,0]);rt(this,pt,null);rt(this,Et,null);rt(this,At,null);ot(this,Et,U),ot(this,At,gt),ot(this,Z,Tt),this._eventBus=Dt,this._eventBus._on("editingaction",t(this,J)),this._eventBus._on("pagechanging",t(this,Q)),this._eventBus._on("scalechanging",t(this,A)),this._eventBus._on("rotationchanging",t(this,j)),ot(this,$,Nt.annotationStorage),ot(this,S,Nt.filterFactory),ot(this,X,Ct),this.viewParameters={realScale:d.PixelsPerInch.PDF_TO_CSS_UNITS,rotation:0}}static get _keyboardManager(){const U=zt.prototype,gt=Nt=>{const{activeElement:Ct}=document;return Ct&&t(Nt,Et).contains(Ct)&&Nt.hasSomethingToControl()},Tt=this.TRANSLATE_SMALL,Dt=this.TRANSLATE_BIG;return(0,n.shadow)(this,"_keyboardManager",new f([[["ctrl+a","mac+meta+a"],U.selectAll],[["ctrl+z","mac+meta+z"],U.undo],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],U.redo],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],U.delete],[["Escape","mac+Escape"],U.unselectAll],[["ArrowLeft","mac+ArrowLeft"],U.translateSelectedEditors,{args:[-Tt,0],checker:gt}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],U.translateSelectedEditors,{args:[-Dt,0],checker:gt}],[["ArrowRight","mac+ArrowRight"],U.translateSelectedEditors,{args:[Tt,0],checker:gt}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],U.translateSelectedEditors,{args:[Dt,0],checker:gt}],[["ArrowUp","mac+ArrowUp"],U.translateSelectedEditors,{args:[0,-Tt],checker:gt}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],U.translateSelectedEditors,{args:[0,-Dt],checker:gt}],[["ArrowDown","mac+ArrowDown"],U.translateSelectedEditors,{args:[0,Tt],checker:gt}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],U.translateSelectedEditors,{args:[0,Dt],checker:gt}]]))}destroy(){K(this,tt,Ae).call(this),K(this,tt,Ue).call(this),this._eventBus._off("editingaction",t(this,J)),this._eventBus._off("pagechanging",t(this,Q)),this._eventBus._off("scalechanging",t(this,A)),this._eventBus._off("rotationchanging",t(this,j));for(const U of t(this,k).values())U.destroy();t(this,k).clear(),t(this,v).clear(),t(this,et).clear(),ot(this,r,null),t(this,N).clear(),t(this,q).destroy(),t(this,Z).destroy()}get hcmFilter(){return(0,n.shadow)(this,"hcmFilter",t(this,X)?t(this,S).addHCMFilter(t(this,X).foreground,t(this,X).background):"none")}get direction(){return(0,n.shadow)(this,"direction",getComputedStyle(t(this,Et)).direction)}editAltText(U){var gt;(gt=t(this,Z))==null||gt.editAltText(this,U)}onPageChanging({pageNumber:U}){ot(this,G,U-1)}focusMainContainer(){t(this,Et).focus()}findParent(U,gt){for(const Tt of t(this,k).values()){const{x:Dt,y:Nt,width:Ct,height:M}=Tt.div.getBoundingClientRect();if(U>=Dt&&U<=Dt+Ct&&gt>=Nt&&gt<=Nt+M)return Tt}return null}disableUserSelect(U=!1){t(this,At).classList.toggle("noUserSelect",U)}addShouldRescale(U){t(this,et).add(U)}removeShouldRescale(U){t(this,et).delete(U)}onScaleChanging({scale:U}){this.commitOrRemove(),this.viewParameters.realScale=U*d.PixelsPerInch.PDF_TO_CSS_UNITS;for(const gt of t(this,et))gt.onScaleChanging()}onRotationChanging({pagesRotation:U}){this.commitOrRemove(),this.viewParameters.rotation=U}addToAnnotationStorage(U){!U.isEmpty()&&t(this,$)&&!t(this,$).has(U.id)&&t(this,$).setValue(U.id,U)}blur(){if(!this.hasSelection)return;const{activeElement:U}=document;for(const gt of t(this,N))if(gt.div.contains(U)){ot(this,L,[gt,U]),gt._focusEventsAllowed=!1;break}}focus(){if(!t(this,L))return;const[U,gt]=t(this,L);ot(this,L,null),gt.addEventListener("focusin",()=>{U._focusEventsAllowed=!0},{once:!0}),gt.focus()}addEditListeners(){K(this,tt,We).call(this),K(this,tt,He).call(this)}removeEditListeners(){K(this,tt,Ae).call(this),K(this,tt,Ge).call(this)}copy(U){var Tt;if(U.preventDefault(),(Tt=t(this,r))==null||Tt.commitOrRemove(),!this.hasSelection)return;const gt=[];for(const Dt of t(this,N)){const Nt=Dt.serialize(!0);Nt&&gt.push(Nt)}gt.length!==0&&U.clipboardData.setData("application/pdfjs",JSON.stringify(gt))}cut(U){this.copy(U),this.delete()}paste(U){U.preventDefault();const{clipboardData:gt}=U;for(const Nt of gt.items)for(const Ct of t(this,V))if(Ct.isHandlingMimeForPasting(Nt.type)){Ct.paste(Nt,this.currentLayer);return}let Tt=gt.getData("application/pdfjs");if(!Tt)return;try{Tt=JSON.parse(Tt)}catch(Nt){(0,n.warn)(`paste: "${Nt.message}".`);return}if(!Array.isArray(Tt))return;this.unselectAll();const Dt=this.currentLayer;try{const Nt=[];for(const w of Tt){const z=Dt.deserialize(w);if(!z)return;Nt.push(z)}const Ct=()=>{for(const w of Nt)K(this,tt,$e).call(this,w);K(this,tt,Ve).call(this,Nt)},M=()=>{for(const w of Nt)w.remove()};this.addCommands({cmd:Ct,undo:M,mustExec:!0})}catch(Nt){(0,n.warn)(`paste: "${Nt.message}".`)}}keydown(U){var gt;(gt=this.getActive())!=null&&gt.shouldGetKeyboardEvents()||zt._keyboardManager.exec(this,U)}onEditingAction(U){["undo","redo","delete","selectAll"].includes(U.name)&&this[U.name]()}setEditingState(U){U?(K(this,tt,En).call(this),K(this,tt,We).call(this),K(this,tt,He).call(this),K(this,tt,Zt).call(this,{isEditing:t(this,O)!==n.AnnotationEditorType.NONE,isEmpty:K(this,tt,ue).call(this),hasSomethingToUndo:t(this,q).hasSomethingToUndo(),hasSomethingToRedo:t(this,q).hasSomethingToRedo(),hasSelectedEditor:!1})):(K(this,tt,Ue).call(this),K(this,tt,Ae).call(this),K(this,tt,Ge).call(this),K(this,tt,Zt).call(this,{isEditing:!1}),this.disableUserSelect(!1))}registerEditorTypes(U){if(!t(this,V)){ot(this,V,U);for(const gt of t(this,V))K(this,tt,de).call(this,gt.defaultPropertiesToUpdate)}}getId(){return t(this,s).getId()}get currentLayer(){return t(this,k).get(t(this,G))}getLayer(U){return t(this,k).get(U)}get currentPageIndex(){return t(this,G)}addLayer(U){t(this,k).set(U.pageIndex,U),t(this,a)?U.enable():U.disable()}removeLayer(U){t(this,k).delete(U.pageIndex)}updateMode(U,gt=null){if(t(this,O)!==U){if(ot(this,O,U),U===n.AnnotationEditorType.NONE){this.setEditingState(!1),K(this,tt,xn).call(this);return}this.setEditingState(!0),K(this,tt,Sn).call(this),this.unselectAll();for(const Tt of t(this,k).values())Tt.updateMode(U);if(gt){for(const Tt of t(this,v).values())if(Tt.annotationElementId===gt){this.setSelected(Tt),Tt.enterInEditMode();break}}}}updateToolbar(U){U!==t(this,O)&&this._eventBus.dispatch("switchannotationeditormode",{source:this,mode:U})}updateParams(U,gt){if(t(this,V)){if(U===n.AnnotationEditorParamsType.CREATE){this.currentLayer.addNewEditor(U);return}for(const Tt of t(this,N))Tt.updateParams(U,gt);for(const Tt of t(this,V))Tt.updateDefaultParams(U,gt)}}enableWaiting(U=!1){if(t(this,g)!==U){ot(this,g,U);for(const gt of t(this,k).values())U?gt.disableClick():gt.enableClick(),gt.div.classList.toggle("waiting",U)}}getEditors(U){const gt=[];for(const Tt of t(this,v).values())Tt.pageIndex===U&&gt.push(Tt);return gt}getEditor(U){return t(this,v).get(U)}addEditor(U){t(this,v).set(U.id,U)}removeEditor(U){var gt;t(this,v).delete(U.id),this.unselect(U),(!U.annotationElementId||!t(this,it).has(U.annotationElementId))&&((gt=t(this,$))==null||gt.remove(U.id))}addDeletedAnnotationElement(U){t(this,it).add(U.annotationElementId),U.deleted=!0}isDeletedAnnotationElement(U){return t(this,it).has(U)}removeDeletedAnnotationElement(U){t(this,it).delete(U.annotationElementId),U.deleted=!1}setActiveEditor(U){t(this,r)!==U&&(ot(this,r,U),U&&K(this,tt,de).call(this,U.propertiesToUpdate))}toggleSelected(U){if(t(this,N).has(U)){t(this,N).delete(U),U.unselect(),K(this,tt,Zt).call(this,{hasSelectedEditor:this.hasSelection});return}t(this,N).add(U),U.select(),K(this,tt,de).call(this,U.propertiesToUpdate),K(this,tt,Zt).call(this,{hasSelectedEditor:!0})}setSelected(U){for(const gt of t(this,N))gt!==U&&gt.unselect();t(this,N).clear(),t(this,N).add(U),U.select(),K(this,tt,de).call(this,U.propertiesToUpdate),K(this,tt,Zt).call(this,{hasSelectedEditor:!0})}isSelected(U){return t(this,N).has(U)}unselect(U){U.unselect(),t(this,N).delete(U),K(this,tt,Zt).call(this,{hasSelectedEditor:this.hasSelection})}get hasSelection(){return t(this,N).size!==0}undo(){t(this,q).undo(),K(this,tt,Zt).call(this,{hasSomethingToUndo:t(this,q).hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:K(this,tt,ue).call(this)})}redo(){t(this,q).redo(),K(this,tt,Zt).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:t(this,q).hasSomethingToRedo(),isEmpty:K(this,tt,ue).call(this)})}addCommands(U){t(this,q).add(U),K(this,tt,Zt).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:K(this,tt,ue).call(this)})}delete(){if(this.commitOrRemove(),!this.hasSelection)return;const U=[...t(this,N)],gt=()=>{for(const Dt of U)Dt.remove()},Tt=()=>{for(const Dt of U)K(this,tt,$e).call(this,Dt)};this.addCommands({cmd:gt,undo:Tt,mustExec:!0})}commitOrRemove(){var U;(U=t(this,r))==null||U.commitOrRemove()}hasSomethingToControl(){return t(this,r)||this.hasSelection}selectAll(){for(const U of t(this,N))U.commit();K(this,tt,Ve).call(this,t(this,v).values())}unselectAll(){if(t(this,r)){t(this,r).commitOrRemove();return}if(this.hasSelection){for(const U of t(this,N))U.unselect();t(this,N).clear(),K(this,tt,Zt).call(this,{hasSelectedEditor:!1})}}translateSelectedEditors(U,gt,Tt=!1){if(Tt||this.commitOrRemove(),!this.hasSelection)return;t(this,st)[0]+=U,t(this,st)[1]+=gt;const[Dt,Nt]=t(this,st),Ct=[...t(this,N)],M=1e3;t(this,pt)&&clearTimeout(t(this,pt)),ot(this,pt,setTimeout(()=>{ot(this,pt,null),t(this,st)[0]=t(this,st)[1]=0,this.addCommands({cmd:()=>{for(const w of Ct)t(this,v).has(w.id)&&w.translateInPage(Dt,Nt)},undo:()=>{for(const w of Ct)t(this,v).has(w.id)&&w.translateInPage(-Dt,-Nt)},mustExec:!1})},M));for(const w of Ct)w.translateInPage(U,gt)}setUpDragSession(){if(this.hasSelection){this.disableUserSelect(!0),ot(this,R,new Map);for(const U of t(this,N))t(this,R).set(U,{savedX:U.x,savedY:U.y,savedPageIndex:U.pageIndex,newX:0,newY:0,newPageIndex:-1})}}endDragSession(){if(!t(this,R))return!1;this.disableUserSelect(!1);const U=t(this,R);ot(this,R,null);let gt=!1;for(const[{x:Dt,y:Nt,pageIndex:Ct},M]of U)M.newX=Dt,M.newY=Nt,M.newPageIndex=Ct,gt||(gt=Dt!==M.savedX||Nt!==M.savedY||Ct!==M.savedPageIndex);if(!gt)return!1;const Tt=(Dt,Nt,Ct,M)=>{if(t(this,v).has(Dt.id)){const w=t(this,k).get(M);w?Dt._setParentAndPosition(w,Nt,Ct):(Dt.pageIndex=M,Dt.x=Nt,Dt.y=Ct)}};return this.addCommands({cmd:()=>{for(const[Dt,{newX:Nt,newY:Ct,newPageIndex:M}]of U)Tt(Dt,Nt,Ct,M)},undo:()=>{for(const[Dt,{savedX:Nt,savedY:Ct,savedPageIndex:M}]of U)Tt(Dt,Nt,Ct,M)},mustExec:!0}),!0}dragSelectedEditors(U,gt){if(t(this,R))for(const Tt of t(this,R).keys())Tt.drag(U,gt)}rebuild(U){if(U.parent===null){const gt=this.getLayer(U.pageIndex);gt?(gt.changeParent(U),gt.addOrRebuild(U)):(this.addEditor(U),this.addToAnnotationStorage(U),U.rebuild())}else U.parent.addOrRebuild(U)}isActive(U){return t(this,r)===U}getActive(){return t(this,r)}getMode(){return t(this,O)}get imageManager(){return(0,n.shadow)(this,"imageManager",new P)}};r=new WeakMap,v=new WeakMap,k=new WeakMap,Z=new WeakMap,$=new WeakMap,q=new WeakMap,G=new WeakMap,it=new WeakMap,R=new WeakMap,V=new WeakMap,et=new WeakMap,S=new WeakMap,s=new WeakMap,a=new WeakMap,g=new WeakMap,L=new WeakMap,O=new WeakMap,N=new WeakMap,X=new WeakMap,nt=new WeakMap,ct=new WeakMap,ft=new WeakMap,bt=new WeakMap,mt=new WeakMap,_t=new WeakMap,J=new WeakMap,Q=new WeakMap,A=new WeakMap,j=new WeakMap,Y=new WeakMap,st=new WeakMap,pt=new WeakMap,Et=new WeakMap,At=new WeakMap,tt=new WeakSet,En=function(){window.addEventListener("focus",t(this,ct)),window.addEventListener("blur",t(this,nt))},Ue=function(){window.removeEventListener("focus",t(this,ct)),window.removeEventListener("blur",t(this,nt))},We=function(){window.addEventListener("keydown",t(this,_t),{capture:!0})},Ae=function(){window.removeEventListener("keydown",t(this,_t),{capture:!0})},He=function(){document.addEventListener("copy",t(this,ft)),document.addEventListener("cut",t(this,bt)),document.addEventListener("paste",t(this,mt))},Ge=function(){document.removeEventListener("copy",t(this,ft)),document.removeEventListener("cut",t(this,bt)),document.removeEventListener("paste",t(this,mt))},Zt=function(U){Object.entries(U).some(([Tt,Dt])=>t(this,Y)[Tt]!==Dt)&&this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(t(this,Y),U)})},de=function(U){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:U})},Sn=function(){if(!t(this,a)){ot(this,a,!0);for(const U of t(this,k).values())U.enable()}},xn=function(){if(this.unselectAll(),t(this,a)){ot(this,a,!1);for(const U of t(this,k).values())U.disable()}},$e=function(U){const gt=t(this,k).get(U.pageIndex);gt?gt.addOrRebuild(U):this.addEditor(U)},ue=function(){if(t(this,v).size===0)return!0;if(t(this,v).size===1)for(const U of t(this,v).values())return U.isEmpty();return!1},Ve=function(U){t(this,N).clear();for(const gt of U)gt.isEmpty()||(t(this,N).add(gt),gt.select());K(this,tt,Zt).call(this,{hasSelectedEditor:!0})},Kt(zt,"TRANSLATE_SMALL",1),Kt(zt,"TRANSLATE_BIG",10);let I=zt;e.AnnotationEditorUIManager=I},(T,e,B)=>{var G,it,R,V,et,S,s,a,g,L,O,N,ae,oe,ze,ve,Ee,fe,pe;Object.defineProperty(e,"__esModule",{value:!0}),e.StatTimer=e.RenderingCancelledException=e.PixelsPerInch=e.PageViewport=e.PDFDateString=e.DOMStandardFontDataFactory=e.DOMSVGFactory=e.DOMFilterFactory=e.DOMCanvasFactory=e.DOMCMapReaderFactory=void 0,e.deprecated=o,e.getColorValues=v,e.getCurrentTransform=k,e.getCurrentTransformInverse=Z,e.getFilenameFromUrl=u,e.getPdfFilenameFromUrl=b,e.getRGB=r,e.getXfaPageViewport=F,e.isDataScheme=E,e.isPdfFile=p,e.isValidFetchUrl=_,e.loadScript=c,e.noContextMenu=i,e.setLayerDimensions=$;var n=B(7),d=B(1);const H="http://www.w3.org/2000/svg",q=class q{};Kt(q,"CSS",96),Kt(q,"PDF",72),Kt(q,"PDF_TO_CSS_UNITS",q.CSS/q.PDF);let W=q;e.PixelsPerInch=W;class ht extends n.BaseFilterFactory{constructor({docId:A,ownerDocument:j=globalThis.document}={}){super();rt(this,N);rt(this,G);rt(this,it);rt(this,R);rt(this,V);rt(this,et);rt(this,S);rt(this,s);rt(this,a);rt(this,g);rt(this,L);rt(this,O,0);ot(this,R,A),ot(this,V,j)}addFilter(A){if(!A)return"none";let j=t(this,N,ae).get(A);if(j)return j;let Y,st,pt,Et;if(A.length===1){const xt=A[0],Ut=new Array(256);for(let jt=0;jt<256;jt++)Ut[jt]=xt[jt]/255;Et=Y=st=pt=Ut.join(",")}else{const[xt,Ut,jt]=A,Vt=new Array(256),kt=new Array(256),Ot=new Array(256);for(let Ht=0;Ht<256;Ht++)Vt[Ht]=xt[Ht]/255,kt[Ht]=Ut[Ht]/255,Ot[Ht]=jt[Ht]/255;Y=Vt.join(","),st=kt.join(","),pt=Ot.join(","),Et=`${Y}${st}${pt}`}if(j=t(this,N,ae).get(Et),j)return t(this,N,ae).set(A,j),j;const At=`g_${t(this,R)}_transfer_map_${he(this,O)._++}`,tt=`url(#${At})`;t(this,N,ae).set(A,tt),t(this,N,ae).set(Et,tt);const wt=K(this,N,ve).call(this,At);return K(this,N,fe).call(this,Y,st,pt,wt),tt}addHCMFilter(A,j){var Ut;const Y=`${A}-${j}`;if(t(this,S)===Y)return t(this,s);if(ot(this,S,Y),ot(this,s,"none"),(Ut=t(this,et))==null||Ut.remove(),!A||!j)return t(this,s);const st=K(this,N,pe).call(this,A);A=d.Util.makeHexColor(...st);const pt=K(this,N,pe).call(this,j);if(j=d.Util.makeHexColor(...pt),t(this,N,oe).style.color="",A==="#000000"&&j==="#ffffff"||A===j)return t(this,s);const Et=new Array(256);for(let jt=0;jt<=255;jt++){const Vt=jt/255;Et[jt]=Vt<=.03928?Vt/12.92:((Vt+.055)/1.055)**2.4}const At=Et.join(","),tt=`g_${t(this,R)}_hcm_filter`,wt=ot(this,a,K(this,N,ve).call(this,tt));K(this,N,fe).call(this,At,At,At,wt),K(this,N,ze).call(this,wt);const xt=(jt,Vt)=>{const kt=st[jt]/255,Ot=pt[jt]/255,Ht=new Array(Vt+1);for(let qt=0;qt<=Vt;qt++)Ht[qt]=kt+qt/Vt*(Ot-kt);return Ht.join(",")};return K(this,N,fe).call(this,xt(0,5),xt(1,5),xt(2,5),wt),ot(this,s,`url(#${tt})`),t(this,s)}addHighlightHCMFilter(A,j,Y,st){var Ot;const pt=`${A}-${j}-${Y}-${st}`;if(t(this,g)===pt)return t(this,L);if(ot(this,g,pt),ot(this,L,"none"),(Ot=t(this,a))==null||Ot.remove(),!A||!j)return t(this,L);const[Et,At]=[A,j].map(K(this,N,pe).bind(this));let tt=Math.round(.2126*Et[0]+.7152*Et[1]+.0722*Et[2]),wt=Math.round(.2126*At[0]+.7152*At[1]+.0722*At[2]),[xt,Ut]=[Y,st].map(K(this,N,pe).bind(this));wt<tt&&([tt,wt,xt,Ut]=[wt,tt,Ut,xt]),t(this,N,oe).style.color="";const jt=(Ht,qt,vt)=>{const at=new Array(256),lt=(wt-tt)/vt,Rt=Ht/255,zt=(qt-Ht)/(255*vt);let $t=0;for(let U=0;U<=vt;U++){const gt=Math.round(tt+U*lt),Tt=Rt+U*zt;for(let Dt=$t;Dt<=gt;Dt++)at[Dt]=Tt;$t=gt+1}for(let U=$t;U<256;U++)at[U]=at[$t-1];return at.join(",")},Vt=`g_${t(this,R)}_hcm_highlight_filter`,kt=ot(this,a,K(this,N,ve).call(this,Vt));return K(this,N,ze).call(this,kt),K(this,N,fe).call(this,jt(xt[0],Ut[0],5),jt(xt[1],Ut[1],5),jt(xt[2],Ut[2],5),kt),ot(this,L,`url(#${Vt})`),t(this,L)}destroy(A=!1){A&&(t(this,s)||t(this,L))||(t(this,it)&&(t(this,it).parentNode.parentNode.remove(),ot(this,it,null)),t(this,G)&&(t(this,G).clear(),ot(this,G,null)),ot(this,O,0))}}G=new WeakMap,it=new WeakMap,R=new WeakMap,V=new WeakMap,et=new WeakMap,S=new WeakMap,s=new WeakMap,a=new WeakMap,g=new WeakMap,L=new WeakMap,O=new WeakMap,N=new WeakSet,ae=function(){return t(this,G)||ot(this,G,new Map)},oe=function(){if(!t(this,it)){const A=t(this,V).createElement("div"),{style:j}=A;j.visibility="hidden",j.contain="strict",j.width=j.height=0,j.position="absolute",j.top=j.left=0,j.zIndex=-1;const Y=t(this,V).createElementNS(H,"svg");Y.setAttribute("width",0),Y.setAttribute("height",0),ot(this,it,t(this,V).createElementNS(H,"defs")),A.append(Y),Y.append(t(this,it)),t(this,V).body.append(A)}return t(this,it)},ze=function(A){const j=t(this,V).createElementNS(H,"feColorMatrix");j.setAttribute("type","matrix"),j.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0"),A.append(j)},ve=function(A){const j=t(this,V).createElementNS(H,"filter");return j.setAttribute("color-interpolation-filters","sRGB"),j.setAttribute("id",A),t(this,N,oe).append(j),j},Ee=function(A,j,Y){const st=t(this,V).createElementNS(H,j);st.setAttribute("type","discrete"),st.setAttribute("tableValues",Y),A.append(st)},fe=function(A,j,Y,st){const pt=t(this,V).createElementNS(H,"feComponentTransfer");st.append(pt),K(this,N,Ee).call(this,pt,"feFuncR",A),K(this,N,Ee).call(this,pt,"feFuncG",j),K(this,N,Ee).call(this,pt,"feFuncB",Y)},pe=function(A){return t(this,N,oe).style.color=A,r(getComputedStyle(t(this,N,oe)).getPropertyValue("color"))},e.DOMFilterFactory=ht;class P extends n.BaseCanvasFactory{constructor({ownerDocument:Q=globalThis.document}={}){super(),this._document=Q}_createCanvas(Q,A){const j=this._document.createElement("canvas");return j.width=Q,j.height=A,j}}e.DOMCanvasFactory=P;async function x(J,Q=!1){if(_(J,document.baseURI)){const A=await fetch(J);if(!A.ok)throw new Error(A.statusText);return Q?new Uint8Array(await A.arrayBuffer()):(0,d.stringToBytes)(await A.text())}return new Promise((A,j)=>{const Y=new XMLHttpRequest;Y.open("GET",J,!0),Q&&(Y.responseType="arraybuffer"),Y.onreadystatechange=()=>{if(Y.readyState===XMLHttpRequest.DONE){if(Y.status===200||Y.status===0){let st;if(Q&&Y.response?st=new Uint8Array(Y.response):!Q&&Y.responseText&&(st=(0,d.stringToBytes)(Y.responseText)),st){A(st);return}}j(new Error(Y.statusText))}},Y.send(null)})}class f extends n.BaseCMapReaderFactory{_fetchData(Q,A){return x(Q,this.isCompressed).then(j=>({cMapData:j,compressionType:A}))}}e.DOMCMapReaderFactory=f;class D extends n.BaseStandardFontDataFactory{_fetchData(Q){return x(Q,!0)}}e.DOMStandardFontDataFactory=D;class I extends n.BaseSVGFactory{_createSVG(Q){return document.createElementNS(H,Q)}}e.DOMSVGFactory=I;class y{constructor({viewBox:Q,scale:A,rotation:j,offsetX:Y=0,offsetY:st=0,dontFlip:pt=!1}){this.viewBox=Q,this.scale=A,this.rotation=j,this.offsetX=Y,this.offsetY=st;const Et=(Q[2]+Q[0])/2,At=(Q[3]+Q[1])/2;let tt,wt,xt,Ut;switch(j%=360,j<0&&(j+=360),j){case 180:tt=-1,wt=0,xt=0,Ut=1;break;case 90:tt=0,wt=1,xt=1,Ut=0;break;case 270:tt=0,wt=-1,xt=-1,Ut=0;break;case 0:tt=1,wt=0,xt=0,Ut=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}pt&&(xt=-xt,Ut=-Ut);let jt,Vt,kt,Ot;tt===0?(jt=Math.abs(At-Q[1])*A+Y,Vt=Math.abs(Et-Q[0])*A+st,kt=(Q[3]-Q[1])*A,Ot=(Q[2]-Q[0])*A):(jt=Math.abs(Et-Q[0])*A+Y,Vt=Math.abs(At-Q[1])*A+st,kt=(Q[2]-Q[0])*A,Ot=(Q[3]-Q[1])*A),this.transform=[tt*A,wt*A,xt*A,Ut*A,jt-tt*A*Et-xt*A*At,Vt-wt*A*Et-Ut*A*At],this.width=kt,this.height=Ot}get rawDims(){const{viewBox:Q}=this;return(0,d.shadow)(this,"rawDims",{pageWidth:Q[2]-Q[0],pageHeight:Q[3]-Q[1],pageX:Q[0],pageY:Q[1]})}clone({scale:Q=this.scale,rotation:A=this.rotation,offsetX:j=this.offsetX,offsetY:Y=this.offsetY,dontFlip:st=!1}={}){return new y({viewBox:this.viewBox.slice(),scale:Q,rotation:A,offsetX:j,offsetY:Y,dontFlip:st})}convertToViewportPoint(Q,A){return d.Util.applyTransform([Q,A],this.transform)}convertToViewportRectangle(Q){const A=d.Util.applyTransform([Q[0],Q[1]],this.transform),j=d.Util.applyTransform([Q[2],Q[3]],this.transform);return[A[0],A[1],j[0],j[1]]}convertToPdfPoint(Q,A){return d.Util.applyInverseTransform([Q,A],this.transform)}}e.PageViewport=y;class m extends d.BaseException{constructor(Q,A=0){super(Q,"RenderingCancelledException"),this.extraDelay=A}}e.RenderingCancelledException=m;function E(J){const Q=J.length;let A=0;for(;A<Q&&J[A].trim()==="";)A++;return J.substring(A,A+5).toLowerCase()==="data:"}function p(J){return typeof J=="string"&&/\.pdf$/i.test(J)}function u(J,Q=!1){return Q||([J]=J.split(/[#?]/,1)),J.substring(J.lastIndexOf("/")+1)}function b(J,Q="document.pdf"){if(typeof J!="string")return Q;if(E(J))return(0,d.warn)('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.'),Q;const A=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/,j=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,Y=A.exec(J);let st=j.exec(Y[1])||j.exec(Y[2])||j.exec(Y[3]);if(st&&(st=st[0],st.includes("%")))try{st=j.exec(decodeURIComponent(st))[0]}catch{}return st||Q}class C{constructor(){Kt(this,"started",Object.create(null));Kt(this,"times",[])}time(Q){Q in this.started&&(0,d.warn)(`Timer is already running for ${Q}`),this.started[Q]=Date.now()}timeEnd(Q){Q in this.started||(0,d.warn)(`Timer has not been started for ${Q}`),this.times.push({name:Q,start:this.started[Q],end:Date.now()}),delete this.started[Q]}toString(){const Q=[];let A=0;for(const{name:j}of this.times)A=Math.max(j.length,A);for(const{name:j,start:Y,end:st}of this.times)Q.push(`${j.padEnd(A)} ${st-Y}ms
`);return Q.join("")}}e.StatTimer=C;function _(J,Q){try{const{protocol:A}=Q?new URL(J,Q):new URL(J);return A==="http:"||A==="https:"}catch{return!1}}function i(J){J.preventDefault()}function c(J,Q=!1){return new Promise((A,j)=>{const Y=document.createElement("script");Y.src=J,Y.onload=function(st){Q&&Y.remove(),A(st)},Y.onerror=function(){j(new Error(`Cannot load script at: ${Y.src}`))},(document.head||document.documentElement).append(Y)})}function o(J){console.log("Deprecated API usage: "+J)}let l;class h{static toDateObject(Q){if(!Q||typeof Q!="string")return null;l||(l=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?"));const A=l.exec(Q);if(!A)return null;const j=parseInt(A[1],10);let Y=parseInt(A[2],10);Y=Y>=1&&Y<=12?Y-1:0;let st=parseInt(A[3],10);st=st>=1&&st<=31?st:1;let pt=parseInt(A[4],10);pt=pt>=0&&pt<=23?pt:0;let Et=parseInt(A[5],10);Et=Et>=0&&Et<=59?Et:0;let At=parseInt(A[6],10);At=At>=0&&At<=59?At:0;const tt=A[7]||"Z";let wt=parseInt(A[8],10);wt=wt>=0&&wt<=23?wt:0;let xt=parseInt(A[9],10)||0;return xt=xt>=0&&xt<=59?xt:0,tt==="-"?(pt+=wt,Et+=xt):tt==="+"&&(pt-=wt,Et-=xt),new Date(Date.UTC(j,Y,st,pt,Et,At))}}e.PDFDateString=h;function F(J,{scale:Q=1,rotation:A=0}){const{width:j,height:Y}=J.attributes.style,st=[0,0,parseInt(j),parseInt(Y)];return new y({viewBox:st,scale:Q,rotation:A})}function r(J){if(J.startsWith("#")){const Q=parseInt(J.slice(1),16);return[(Q&16711680)>>16,(Q&65280)>>8,Q&255]}return J.startsWith("rgb(")?J.slice(4,-1).split(",").map(Q=>parseInt(Q)):J.startsWith("rgba(")?J.slice(5,-1).split(",").map(Q=>parseInt(Q)).slice(0,3):((0,d.warn)(`Not a valid color format: "${J}"`),[0,0,0])}function v(J){const Q=document.createElement("span");Q.style.visibility="hidden",document.body.append(Q);for(const A of J.keys()){Q.style.color=A;const j=window.getComputedStyle(Q).color;J.set(A,r(j))}Q.remove()}function k(J){const{a:Q,b:A,c:j,d:Y,e:st,f:pt}=J.getTransform();return[Q,A,j,Y,st,pt]}function Z(J){const{a:Q,b:A,c:j,d:Y,e:st,f:pt}=J.getTransform().invertSelf();return[Q,A,j,Y,st,pt]}function $(J,Q,A=!1,j=!0){if(Q instanceof y){const{pageWidth:Y,pageHeight:st}=Q.rawDims,{style:pt}=J,Et=d.FeatureTest.isCSSRoundSupported,At=`var(--scale-factor) * ${Y}px`,tt=`var(--scale-factor) * ${st}px`,wt=Et?`round(${At}, 1px)`:`calc(${At})`,xt=Et?`round(${tt}, 1px)`:`calc(${tt})`;!A||Q.rotation%180===0?(pt.width=wt,pt.height=xt):(pt.width=xt,pt.height=wt)}j&&J.setAttribute("data-main-rotation",Q.rotation)}},(T,e,B)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.BaseStandardFontDataFactory=e.BaseSVGFactory=e.BaseFilterFactory=e.BaseCanvasFactory=e.BaseCMapReaderFactory=void 0;var n=B(1);class d{constructor(){this.constructor===d&&(0,n.unreachable)("Cannot initialize BaseFilterFactory.")}addFilter(f){return"none"}addHCMFilter(f,D){return"none"}addHighlightHCMFilter(f,D,I,y){return"none"}destroy(f=!1){}}e.BaseFilterFactory=d;class H{constructor(){this.constructor===H&&(0,n.unreachable)("Cannot initialize BaseCanvasFactory.")}create(f,D){if(f<=0||D<=0)throw new Error("Invalid canvas size");const I=this._createCanvas(f,D);return{canvas:I,context:I.getContext("2d")}}reset(f,D,I){if(!f.canvas)throw new Error("Canvas is not specified");if(D<=0||I<=0)throw new Error("Invalid canvas size");f.canvas.width=D,f.canvas.height=I}destroy(f){if(!f.canvas)throw new Error("Canvas is not specified");f.canvas.width=0,f.canvas.height=0,f.canvas=null,f.context=null}_createCanvas(f,D){(0,n.unreachable)("Abstract method `_createCanvas` called.")}}e.BaseCanvasFactory=H;class W{constructor({baseUrl:f=null,isCompressed:D=!0}){this.constructor===W&&(0,n.unreachable)("Cannot initialize BaseCMapReaderFactory."),this.baseUrl=f,this.isCompressed=D}async fetch({name:f}){if(!this.baseUrl)throw new Error('The CMap "baseUrl" parameter must be specified, ensure that the "cMapUrl" and "cMapPacked" API parameters are provided.');if(!f)throw new Error("CMap name must be specified.");const D=this.baseUrl+f+(this.isCompressed?".bcmap":""),I=this.isCompressed?n.CMapCompressionType.BINARY:n.CMapCompressionType.NONE;return this._fetchData(D,I).catch(y=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${D}`)})}_fetchData(f,D){(0,n.unreachable)("Abstract method `_fetchData` called.")}}e.BaseCMapReaderFactory=W;class ht{constructor({baseUrl:f=null}){this.constructor===ht&&(0,n.unreachable)("Cannot initialize BaseStandardFontDataFactory."),this.baseUrl=f}async fetch({filename:f}){if(!this.baseUrl)throw new Error('The standard font "baseUrl" parameter must be specified, ensure that the "standardFontDataUrl" API parameter is provided.');if(!f)throw new Error("Font filename must be specified.");const D=`${this.baseUrl}${f}`;return this._fetchData(D).catch(I=>{throw new Error(`Unable to load font data at: ${D}`)})}_fetchData(f){(0,n.unreachable)("Abstract method `_fetchData` called.")}}e.BaseStandardFontDataFactory=ht;class P{constructor(){this.constructor===P&&(0,n.unreachable)("Cannot initialize BaseSVGFactory.")}create(f,D,I=!1){if(f<=0||D<=0)throw new Error("Invalid SVG dimensions");const y=this._createSVG("svg:svg");return y.setAttribute("version","1.1"),I||(y.setAttribute("width",`${f}px`),y.setAttribute("height",`${D}px`)),y.setAttribute("preserveAspectRatio","none"),y.setAttribute("viewBox",`0 0 ${f} ${D}`),y}createElement(f){if(typeof f!="string")throw new Error("Invalid SVG element type");return this._createSVG(f)}_createSVG(f){(0,n.unreachable)("Abstract method `_createSVG` called.")}}e.BaseSVGFactory=P},(T,e,B)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.MurmurHash3_64=void 0;var n=B(1);const d=3285377520,H=4294901760,W=65535;class ht{constructor(x){this.h1=x?x&4294967295:d,this.h2=x?x&4294967295:d}update(x){let f,D;if(typeof x=="string"){f=new Uint8Array(x.length*2),D=0;for(let o=0,l=x.length;o<l;o++){const h=x.charCodeAt(o);h<=255?f[D++]=h:(f[D++]=h>>>8,f[D++]=h&255)}}else if((0,n.isArrayBuffer)(x))f=x.slice(),D=f.byteLength;else throw new Error("Wrong data format in MurmurHash3_64_update. Input must be a string or array.");const I=D>>2,y=D-I*4,m=new Uint32Array(f.buffer,0,I);let E=0,p=0,u=this.h1,b=this.h2;const C=3432918353,_=461845907,i=C&W,c=_&W;for(let o=0;o<I;o++)o&1?(E=m[o],E=E*C&H|E*i&W,E=E<<15|E>>>17,E=E*_&H|E*c&W,u^=E,u=u<<13|u>>>19,u=u*5+3864292196):(p=m[o],p=p*C&H|p*i&W,p=p<<15|p>>>17,p=p*_&H|p*c&W,b^=p,b=b<<13|b>>>19,b=b*5+3864292196);switch(E=0,y){case 3:E^=f[I*4+2]<<16;case 2:E^=f[I*4+1]<<8;case 1:E^=f[I*4],E=E*C&H|E*i&W,E=E<<15|E>>>17,E=E*_&H|E*c&W,I&1?u^=E:b^=E}this.h1=u,this.h2=b}hexdigest(){let x=this.h1,f=this.h2;return x^=f>>>1,x=x*3981806797&H|x*36045&W,f=f*4283543511&H|((f<<16|x>>>16)*2950163797&H)>>>16,x^=f>>>1,x=x*444984403&H|x*60499&W,f=f*3301882366&H|((f<<16|x>>>16)*3120437893&H)>>>16,x^=f>>>1,(x>>>0).toString(16).padStart(8,"0")+(f>>>0).toString(16).padStart(8,"0")}}e.MurmurHash3_64=ht},(T,e,B)=>{var W;Object.defineProperty(e,"__esModule",{value:!0}),e.FontLoader=e.FontFaceObject=void 0;var n=B(1);class d{constructor({ownerDocument:P=globalThis.document,styleElement:x=null}){rt(this,W,new Set);this._document=P,this.nativeFontFaces=new Set,this.styleElement=null,this.loadingRequests=[],this.loadTestFontId=0}addNativeFontFace(P){this.nativeFontFaces.add(P),this._document.fonts.add(P)}removeNativeFontFace(P){this.nativeFontFaces.delete(P),this._document.fonts.delete(P)}insertRule(P){this.styleElement||(this.styleElement=this._document.createElement("style"),this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement));const x=this.styleElement.sheet;x.insertRule(P,x.cssRules.length)}clear(){for(const P of this.nativeFontFaces)this._document.fonts.delete(P);this.nativeFontFaces.clear(),t(this,W).clear(),this.styleElement&&(this.styleElement.remove(),this.styleElement=null)}async loadSystemFont(P){if(!(!P||t(this,W).has(P.loadedName))){if((0,n.assert)(!this.disableFontFace,"loadSystemFont shouldn't be called when `disableFontFace` is set."),this.isFontLoadingAPISupported){const{loadedName:x,src:f,style:D}=P,I=new FontFace(x,f,D);this.addNativeFontFace(I);try{await I.load(),t(this,W).add(x)}catch{(0,n.warn)(`Cannot load system font: ${P.baseFontName}, installing it could help to improve PDF rendering.`),this.removeNativeFontFace(I)}return}(0,n.unreachable)("Not implemented: loadSystemFont without the Font Loading API.")}}async bind(P){if(P.attached||P.missingFile&&!P.systemFontInfo)return;if(P.attached=!0,P.systemFontInfo){await this.loadSystemFont(P.systemFontInfo);return}if(this.isFontLoadingAPISupported){const f=P.createNativeFontFace();if(f){this.addNativeFontFace(f);try{await f.loaded}catch(D){throw(0,n.warn)(`Failed to load font '${f.family}': '${D}'.`),P.disableFontFace=!0,D}}return}const x=P.createFontFaceRule();if(x){if(this.insertRule(x),this.isSyncFontLoadingSupported)return;await new Promise(f=>{const D=this._queueLoadingCallback(f);this._prepareFontLoadEvent(P,D)})}}get isFontLoadingAPISupported(){var x;const P=!!((x=this._document)!=null&&x.fonts);return(0,n.shadow)(this,"isFontLoadingAPISupported",P)}get isSyncFontLoadingSupported(){let P=!1;return(n.isNodeJS||typeof navigator<"u"&&/Mozilla\/5.0.*?rv:\d+.*? Gecko/.test(navigator.userAgent))&&(P=!0),(0,n.shadow)(this,"isSyncFontLoadingSupported",P)}_queueLoadingCallback(P){function x(){for((0,n.assert)(!D.done,"completeRequest() cannot be called twice."),D.done=!0;f.length>0&&f[0].done;){const I=f.shift();setTimeout(I.callback,0)}}const{loadingRequests:f}=this,D={done:!1,complete:x,callback:P};return f.push(D),D}get _loadTestFont(){const P=atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA==");return(0,n.shadow)(this,"_loadTestFont",P)}_prepareFontLoadEvent(P,x){function f(r,v){return r.charCodeAt(v)<<24|r.charCodeAt(v+1)<<16|r.charCodeAt(v+2)<<8|r.charCodeAt(v+3)&255}function D(r,v,k,Z){const $=r.substring(0,v),q=r.substring(v+k);return $+Z+q}let I,y;const m=this._document.createElement("canvas");m.width=1,m.height=1;const E=m.getContext("2d");let p=0;function u(r,v){if(++p>30){(0,n.warn)("Load test font never loaded."),v();return}if(E.font="30px "+r,E.fillText(".",0,20),E.getImageData(0,0,1,1).data[3]>0){v();return}setTimeout(u.bind(null,r,v))}const b=`lt${Date.now()}${this.loadTestFontId++}`;let C=this._loadTestFont;C=D(C,976,b.length,b);const i=16,c=1482184792;let o=f(C,i);for(I=0,y=b.length-3;I<y;I+=4)o=o-c+f(b,I)|0;I<b.length&&(o=o-c+f(b+"XXX",I)|0),C=D(C,i,4,(0,n.string32)(o));const l=`url(data:font/opentype;base64,${btoa(C)});`,h=`@font-face {font-family:"${b}";src:${l}}`;this.insertRule(h);const F=this._document.createElement("div");F.style.visibility="hidden",F.style.width=F.style.height="10px",F.style.position="absolute",F.style.top=F.style.left="0px";for(const r of[P.loadedName,b]){const v=this._document.createElement("span");v.textContent="Hi",v.style.fontFamily=r,F.append(v)}this._document.body.append(F),u(b,()=>{F.remove(),x.complete()})}}W=new WeakMap,e.FontLoader=d;class H{constructor(P,{isEvalSupported:x=!0,disableFontFace:f=!1,ignoreErrors:D=!1,inspectFont:I=null}){this.compiledGlyphs=Object.create(null);for(const y in P)this[y]=P[y];this.isEvalSupported=x!==!1,this.disableFontFace=f===!0,this.ignoreErrors=D===!0,this._inspectFont=I}createNativeFontFace(){var x;if(!this.data||this.disableFontFace)return null;let P;if(!this.cssFontInfo)P=new FontFace(this.loadedName,this.data,{});else{const f={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(f.style=`oblique ${this.cssFontInfo.italicAngle}deg`),P=new FontFace(this.cssFontInfo.fontFamily,this.data,f)}return(x=this._inspectFont)==null||x.call(this,this),P}createFontFaceRule(){var D;if(!this.data||this.disableFontFace)return null;const P=(0,n.bytesToString)(this.data),x=`url(data:${this.mimetype};base64,${btoa(P)});`;let f;if(!this.cssFontInfo)f=`@font-face {font-family:"${this.loadedName}";src:${x}}`;else{let I=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(I+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`),f=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${I}src:${x}}`}return(D=this._inspectFont)==null||D.call(this,this,x),f}getPathGenerator(P,x){if(this.compiledGlyphs[x]!==void 0)return this.compiledGlyphs[x];let f;try{f=P.get(this.loadedName+"_path_"+x)}catch(D){if(!this.ignoreErrors)throw D;return(0,n.warn)(`getPathGenerator - ignoring character: "${D}".`),this.compiledGlyphs[x]=function(I,y){}}if(this.isEvalSupported&&n.FeatureTest.isEvalSupported){const D=[];for(const I of f){const y=I.args!==void 0?I.args.join(","):"";D.push("c.",I.cmd,"(",y,`);
`)}return this.compiledGlyphs[x]=new Function("c","size",D.join(""))}return this.compiledGlyphs[x]=function(D,I){for(const y of f)y.cmd==="scale"&&(y.args=[I,-I]),D[y.cmd].apply(D,y.args)}}}e.FontFaceObject=H},(T,e,B)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.NodeStandardFontDataFactory=e.NodeFilterFactory=e.NodeCanvasFactory=e.NodeCMapReaderFactory=void 0;var n=B(7);B(1);const d=function(x){return new Promise((f,D)=>{require$$5.readFile(x,(y,m)=>{if(y||!m){D(new Error(y));return}f(new Uint8Array(m))})})};class H extends n.BaseFilterFactory{}e.NodeFilterFactory=H;class W extends n.BaseCanvasFactory{_createCanvas(f,D){return require$$5.createCanvas(f,D)}}e.NodeCanvasFactory=W;class ht extends n.BaseCMapReaderFactory{_fetchData(f,D){return d(f).then(I=>({cMapData:I,compressionType:D}))}}e.NodeCMapReaderFactory=ht;class P extends n.BaseStandardFontDataFactory{_fetchData(f){return d(f)}}e.NodeStandardFontDataFactory=P},(T,e,B)=>{var it,qe,Xe;Object.defineProperty(e,"__esModule",{value:!0}),e.CanvasGraphics=void 0;var n=B(1),d=B(6),H=B(12),W=B(13);const ht=16,P=100,x=4096,f=15,D=10,I=1e3,y=16;function m(S,s){if(S._removeMirroring)throw new Error("Context is already forwarding operations.");S.__originalSave=S.save,S.__originalRestore=S.restore,S.__originalRotate=S.rotate,S.__originalScale=S.scale,S.__originalTranslate=S.translate,S.__originalTransform=S.transform,S.__originalSetTransform=S.setTransform,S.__originalResetTransform=S.resetTransform,S.__originalClip=S.clip,S.__originalMoveTo=S.moveTo,S.__originalLineTo=S.lineTo,S.__originalBezierCurveTo=S.bezierCurveTo,S.__originalRect=S.rect,S.__originalClosePath=S.closePath,S.__originalBeginPath=S.beginPath,S._removeMirroring=()=>{S.save=S.__originalSave,S.restore=S.__originalRestore,S.rotate=S.__originalRotate,S.scale=S.__originalScale,S.translate=S.__originalTranslate,S.transform=S.__originalTransform,S.setTransform=S.__originalSetTransform,S.resetTransform=S.__originalResetTransform,S.clip=S.__originalClip,S.moveTo=S.__originalMoveTo,S.lineTo=S.__originalLineTo,S.bezierCurveTo=S.__originalBezierCurveTo,S.rect=S.__originalRect,S.closePath=S.__originalClosePath,S.beginPath=S.__originalBeginPath,delete S._removeMirroring},S.save=function(){s.save(),this.__originalSave()},S.restore=function(){s.restore(),this.__originalRestore()},S.translate=function(g,L){s.translate(g,L),this.__originalTranslate(g,L)},S.scale=function(g,L){s.scale(g,L),this.__originalScale(g,L)},S.transform=function(g,L,O,N,X,nt){s.transform(g,L,O,N,X,nt),this.__originalTransform(g,L,O,N,X,nt)},S.setTransform=function(g,L,O,N,X,nt){s.setTransform(g,L,O,N,X,nt),this.__originalSetTransform(g,L,O,N,X,nt)},S.resetTransform=function(){s.resetTransform(),this.__originalResetTransform()},S.rotate=function(g){s.rotate(g),this.__originalRotate(g)},S.clip=function(g){s.clip(g),this.__originalClip(g)},S.moveTo=function(a,g){s.moveTo(a,g),this.__originalMoveTo(a,g)},S.lineTo=function(a,g){s.lineTo(a,g),this.__originalLineTo(a,g)},S.bezierCurveTo=function(a,g,L,O,N,X){s.bezierCurveTo(a,g,L,O,N,X),this.__originalBezierCurveTo(a,g,L,O,N,X)},S.rect=function(a,g,L,O){s.rect(a,g,L,O),this.__originalRect(a,g,L,O)},S.closePath=function(){s.closePath(),this.__originalClosePath()},S.beginPath=function(){s.beginPath(),this.__originalBeginPath()}}class E{constructor(s){this.canvasFactory=s,this.cache=Object.create(null)}getCanvas(s,a,g){let L;return this.cache[s]!==void 0?(L=this.cache[s],this.canvasFactory.reset(L,a,g)):(L=this.canvasFactory.create(a,g),this.cache[s]=L),L}delete(s){delete this.cache[s]}clear(){for(const s in this.cache){const a=this.cache[s];this.canvasFactory.destroy(a),delete this.cache[s]}}}function p(S,s,a,g,L,O,N,X,nt,ct){const[ft,bt,mt,_t,J,Q]=(0,d.getCurrentTransform)(S);if(bt===0&&mt===0){const Y=N*ft+J,st=Math.round(Y),pt=X*_t+Q,Et=Math.round(pt),At=(N+nt)*ft+J,tt=Math.abs(Math.round(At)-st)||1,wt=(X+ct)*_t+Q,xt=Math.abs(Math.round(wt)-Et)||1;return S.setTransform(Math.sign(ft),0,0,Math.sign(_t),st,Et),S.drawImage(s,a,g,L,O,0,0,tt,xt),S.setTransform(ft,bt,mt,_t,J,Q),[tt,xt]}if(ft===0&&_t===0){const Y=X*mt+J,st=Math.round(Y),pt=N*bt+Q,Et=Math.round(pt),At=(X+ct)*mt+J,tt=Math.abs(Math.round(At)-st)||1,wt=(N+nt)*bt+Q,xt=Math.abs(Math.round(wt)-Et)||1;return S.setTransform(0,Math.sign(bt),Math.sign(mt),0,st,Et),S.drawImage(s,a,g,L,O,0,0,xt,tt),S.setTransform(ft,bt,mt,_t,J,Q),[xt,tt]}S.drawImage(s,a,g,L,O,N,X,nt,ct);const A=Math.hypot(ft,bt),j=Math.hypot(mt,_t);return[A*nt,j*ct]}function u(S){const{width:s,height:a}=S;if(s>I||a>I)return null;const g=1e3,L=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),O=s+1;let N=new Uint8Array(O*(a+1)),X,nt,ct;const ft=s+7&-8;let bt=new Uint8Array(ft*a),mt=0;for(const j of S.data){let Y=128;for(;Y>0;)bt[mt++]=j&Y?0:255,Y>>=1}let _t=0;for(mt=0,bt[mt]!==0&&(N[0]=1,++_t),nt=1;nt<s;nt++)bt[mt]!==bt[mt+1]&&(N[nt]=bt[mt]?2:1,++_t),mt++;for(bt[mt]!==0&&(N[nt]=2,++_t),X=1;X<a;X++){mt=X*ft,ct=X*O,bt[mt-ft]!==bt[mt]&&(N[ct]=bt[mt]?1:8,++_t);let j=(bt[mt]?4:0)+(bt[mt-ft]?8:0);for(nt=1;nt<s;nt++)j=(j>>2)+(bt[mt+1]?4:0)+(bt[mt-ft+1]?8:0),L[j]&&(N[ct+nt]=L[j],++_t),mt++;if(bt[mt-ft]!==bt[mt]&&(N[ct+nt]=bt[mt]?2:4,++_t),_t>g)return null}for(mt=ft*(a-1),ct=X*O,bt[mt]!==0&&(N[ct]=8,++_t),nt=1;nt<s;nt++)bt[mt]!==bt[mt+1]&&(N[ct+nt]=bt[mt]?4:8,++_t),mt++;if(bt[mt]!==0&&(N[ct+nt]=4,++_t),_t>g)return null;const J=new Int32Array([0,O,-1,0,-O,0,0,0,1]),Q=new Path2D;for(X=0;_t&&X<=a;X++){let j=X*O;const Y=j+s;for(;j<Y&&!N[j];)j++;if(j===Y)continue;Q.moveTo(j%O,X);const st=j;let pt=N[j];do{const Et=J[pt];do j+=Et;while(!N[j]);const At=N[j];At!==5&&At!==10?(pt=At,N[j]=0):(pt=At&51*pt>>4,N[j]&=pt>>2|pt<<2),Q.lineTo(j%O,j/O|0),N[j]||--_t}while(st!==j);--X}return bt=null,N=null,function(j){j.save(),j.scale(1/s,-1/a),j.translate(0,-a),j.fill(Q),j.beginPath(),j.restore()}}class b{constructor(s,a){this.alphaIsShape=!1,this.fontSize=0,this.fontSizeScale=1,this.textMatrix=n.IDENTITY_MATRIX,this.textMatrixScale=1,this.fontMatrix=n.FONT_IDENTITY_MATRIX,this.leading=0,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRenderingMode=n.TextRenderingMode.FILL,this.textRise=0,this.fillColor="#000000",this.strokeColor="#000000",this.patternFill=!1,this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.activeSMask=null,this.transferMaps="none",this.startNewPathAndClipBox([0,0,s,a])}clone(){const s=Object.create(this);return s.clipBox=this.clipBox.slice(),s}setCurrentPoint(s,a){this.x=s,this.y=a}updatePathMinMax(s,a,g){[a,g]=n.Util.applyTransform([a,g],s),this.minX=Math.min(this.minX,a),this.minY=Math.min(this.minY,g),this.maxX=Math.max(this.maxX,a),this.maxY=Math.max(this.maxY,g)}updateRectMinMax(s,a){const g=n.Util.applyTransform(a,s),L=n.Util.applyTransform(a.slice(2),s);this.minX=Math.min(this.minX,g[0],L[0]),this.minY=Math.min(this.minY,g[1],L[1]),this.maxX=Math.max(this.maxX,g[0],L[0]),this.maxY=Math.max(this.maxY,g[1],L[1])}updateScalingPathMinMax(s,a){n.Util.scaleMinMax(s,a),this.minX=Math.min(this.minX,a[0]),this.maxX=Math.max(this.maxX,a[1]),this.minY=Math.min(this.minY,a[2]),this.maxY=Math.max(this.maxY,a[3])}updateCurvePathMinMax(s,a,g,L,O,N,X,nt,ct,ft){const bt=n.Util.bezierBoundingBox(a,g,L,O,N,X,nt,ct);if(ft){ft[0]=Math.min(ft[0],bt[0],bt[2]),ft[1]=Math.max(ft[1],bt[0],bt[2]),ft[2]=Math.min(ft[2],bt[1],bt[3]),ft[3]=Math.max(ft[3],bt[1],bt[3]);return}this.updateRectMinMax(s,bt)}getPathBoundingBox(s=H.PathType.FILL,a=null){const g=[this.minX,this.minY,this.maxX,this.maxY];if(s===H.PathType.STROKE){a||(0,n.unreachable)("Stroke bounding box must include transform.");const L=n.Util.singularValueDecompose2dScale(a),O=L[0]*this.lineWidth/2,N=L[1]*this.lineWidth/2;g[0]-=O,g[1]-=N,g[2]+=O,g[3]+=N}return g}updateClipFromPath(){const s=n.Util.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(s||[0,0,0,0])}isEmptyClip(){return this.minX===1/0}startNewPathAndClipBox(s){this.clipBox=s,this.minX=1/0,this.minY=1/0,this.maxX=0,this.maxY=0}getClippedPathBoundingBox(s=H.PathType.FILL,a=null){return n.Util.intersect(this.clipBox,this.getPathBoundingBox(s,a))}}function C(S,s){if(typeof ImageData<"u"&&s instanceof ImageData){S.putImageData(s,0,0);return}const a=s.height,g=s.width,L=a%y,O=(a-L)/y,N=L===0?O:O+1,X=S.createImageData(g,y);let nt=0,ct;const ft=s.data,bt=X.data;let mt,_t,J,Q;if(s.kind===n.ImageKind.GRAYSCALE_1BPP){const A=ft.byteLength,j=new Uint32Array(bt.buffer,0,bt.byteLength>>2),Y=j.length,st=g+7>>3,pt=4294967295,Et=n.FeatureTest.isLittleEndian?4278190080:255;for(mt=0;mt<N;mt++){for(J=mt<O?y:L,ct=0,_t=0;_t<J;_t++){const At=A-nt;let tt=0;const wt=At>st?g:At*8-7,xt=wt&-8;let Ut=0,jt=0;for(;tt<xt;tt+=8)jt=ft[nt++],j[ct++]=jt&128?pt:Et,j[ct++]=jt&64?pt:Et,j[ct++]=jt&32?pt:Et,j[ct++]=jt&16?pt:Et,j[ct++]=jt&8?pt:Et,j[ct++]=jt&4?pt:Et,j[ct++]=jt&2?pt:Et,j[ct++]=jt&1?pt:Et;for(;tt<wt;tt++)Ut===0&&(jt=ft[nt++],Ut=128),j[ct++]=jt&Ut?pt:Et,Ut>>=1}for(;ct<Y;)j[ct++]=0;S.putImageData(X,0,mt*y)}}else if(s.kind===n.ImageKind.RGBA_32BPP){for(_t=0,Q=g*y*4,mt=0;mt<O;mt++)bt.set(ft.subarray(nt,nt+Q)),nt+=Q,S.putImageData(X,0,_t),_t+=y;mt<N&&(Q=g*L*4,bt.set(ft.subarray(nt,nt+Q)),S.putImageData(X,0,_t))}else if(s.kind===n.ImageKind.RGB_24BPP)for(J=y,Q=g*J,mt=0;mt<N;mt++){for(mt>=O&&(J=L,Q=g*J),ct=0,_t=Q;_t--;)bt[ct++]=ft[nt++],bt[ct++]=ft[nt++],bt[ct++]=ft[nt++],bt[ct++]=255;S.putImageData(X,0,mt*y)}else throw new Error(`bad image kind: ${s.kind}`)}function _(S,s){if(s.bitmap){S.drawImage(s.bitmap,0,0);return}const a=s.height,g=s.width,L=a%y,O=(a-L)/y,N=L===0?O:O+1,X=S.createImageData(g,y);let nt=0;const ct=s.data,ft=X.data;for(let bt=0;bt<N;bt++){const mt=bt<O?y:L;({srcPos:nt}=(0,W.convertBlackAndWhiteToRGBA)({src:ct,srcPos:nt,dest:ft,width:g,height:mt,nonBlackColor:0})),S.putImageData(X,0,bt*y)}}function i(S,s){const a=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"];for(const g of a)S[g]!==void 0&&(s[g]=S[g]);S.setLineDash!==void 0&&(s.setLineDash(S.getLineDash()),s.lineDashOffset=S.lineDashOffset)}function c(S){if(S.strokeStyle=S.fillStyle="#000000",S.fillRule="nonzero",S.globalAlpha=1,S.lineWidth=1,S.lineCap="butt",S.lineJoin="miter",S.miterLimit=10,S.globalCompositeOperation="source-over",S.font="10px sans-serif",S.setLineDash!==void 0&&(S.setLineDash([]),S.lineDashOffset=0),!n.isNodeJS){const{filter:s}=S;s!=="none"&&s!==""&&(S.filter="none")}}function o(S,s,a,g){const L=S.length;for(let O=3;O<L;O+=4){const N=S[O];if(N===0)S[O-3]=s,S[O-2]=a,S[O-1]=g;else if(N<255){const X=255-N;S[O-3]=S[O-3]*N+s*X>>8,S[O-2]=S[O-2]*N+a*X>>8,S[O-1]=S[O-1]*N+g*X>>8}}}function l(S,s,a){const g=S.length,L=1/255;for(let O=3;O<g;O+=4){const N=a?a[S[O]]:S[O];s[O]=s[O]*N*L|0}}function h(S,s,a){const g=S.length;for(let L=3;L<g;L+=4){const O=S[L-3]*77+S[L-2]*152+S[L-1]*28;s[L]=a?s[L]*a[O>>8]>>8:s[L]*O>>16}}function F(S,s,a,g,L,O,N,X,nt,ct,ft){const bt=!!O,mt=bt?O[0]:0,_t=bt?O[1]:0,J=bt?O[2]:0,Q=L==="Luminosity"?h:l,j=Math.min(g,Math.ceil(1048576/a));for(let Y=0;Y<g;Y+=j){const st=Math.min(j,g-Y),pt=S.getImageData(X-ct,Y+(nt-ft),a,st),Et=s.getImageData(X,Y+nt,a,st);bt&&o(pt.data,mt,_t,J),Q(pt.data,Et.data,N),s.putImageData(Et,X,Y+nt)}}function r(S,s,a,g){const L=g[0],O=g[1],N=g[2]-L,X=g[3]-O;N===0||X===0||(F(s.context,a,N,X,s.subtype,s.backdrop,s.transferMap,L,O,s.offsetX,s.offsetY),S.save(),S.globalAlpha=1,S.globalCompositeOperation="source-over",S.setTransform(1,0,0,1,0,0),S.drawImage(a.canvas,0,0),S.restore())}function v(S,s){const a=n.Util.singularValueDecompose2dScale(S);a[0]=Math.fround(a[0]),a[1]=Math.fround(a[1]);const g=Math.fround((globalThis.devicePixelRatio||1)*d.PixelsPerInch.PDF_TO_CSS_UNITS);return s!==void 0?s:a[0]<=g||a[1]<=g}const k=["butt","round","square"],Z=["miter","round","bevel"],$={},q={},et=class et{constructor(s,a,g,L,O,{optionalContentConfig:N,markedContentStack:X=null},nt,ct){rt(this,it);this.ctx=s,this.current=new b(this.ctx.canvas.width,this.ctx.canvas.height),this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=a,this.objs=g,this.canvasFactory=L,this.filterFactory=O,this.groupStack=[],this.processingType3=null,this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.suspendedCtx=null,this.contentVisible=!0,this.markedContentStack=X||[],this.optionalContentConfig=N,this.cachedCanvases=new E(this.canvasFactory),this.cachedPatterns=new Map,this.annotationCanvasMap=nt,this.viewportScale=1,this.outputScaleX=1,this.outputScaleY=1,this.pageColors=ct,this._cachedScaleForStroking=[-1,0],this._cachedGetSinglePixelWidth=null,this._cachedBitmapsMap=new Map}getObject(s,a=null){return typeof s=="string"?s.startsWith("g_")?this.commonObjs.get(s):this.objs.get(s):a}beginDrawing({transform:s,viewport:a,transparency:g=!1,background:L=null}){const O=this.ctx.canvas.width,N=this.ctx.canvas.height,X=this.ctx.fillStyle;if(this.ctx.fillStyle=L||"#ffffff",this.ctx.fillRect(0,0,O,N),this.ctx.fillStyle=X,g){const nt=this.cachedCanvases.getCanvas("transparent",O,N);this.compositeCtx=this.ctx,this.transparentCanvas=nt.canvas,this.ctx=nt.context,this.ctx.save(),this.ctx.transform(...(0,d.getCurrentTransform)(this.compositeCtx))}this.ctx.save(),c(this.ctx),s&&(this.ctx.transform(...s),this.outputScaleX=s[0],this.outputScaleY=s[0]),this.ctx.transform(...a.transform),this.viewportScale=a.scale,this.baseTransform=(0,d.getCurrentTransform)(this.ctx)}executeOperatorList(s,a,g,L){const O=s.argsArray,N=s.fnArray;let X=a||0;const nt=O.length;if(nt===X)return X;const ct=nt-X>D&&typeof g=="function",ft=ct?Date.now()+f:0;let bt=0;const mt=this.commonObjs,_t=this.objs;let J;for(;;){if(L!==void 0&&X===L.nextBreakPoint)return L.breakIt(X,g),X;if(J=N[X],J!==n.OPS.dependency)this[J].apply(this,O[X]);else for(const Q of O[X]){const A=Q.startsWith("g_")?mt:_t;if(!A.has(Q))return A.get(Q,g),X}if(X++,X===nt)return X;if(ct&&++bt>D){if(Date.now()>ft)return g(),X;bt=0}}}endDrawing(){K(this,it,qe).call(this),this.cachedCanvases.clear(),this.cachedPatterns.clear();for(const s of this._cachedBitmapsMap.values()){for(const a of s.values())typeof HTMLCanvasElement<"u"&&a instanceof HTMLCanvasElement&&(a.width=a.height=0);s.clear()}this._cachedBitmapsMap.clear(),K(this,it,Xe).call(this)}_scaleImage(s,a){const g=s.width,L=s.height;let O=Math.max(Math.hypot(a[0],a[1]),1),N=Math.max(Math.hypot(a[2],a[3]),1),X=g,nt=L,ct="prescale1",ft,bt;for(;O>2&&X>1||N>2&&nt>1;){let mt=X,_t=nt;O>2&&X>1&&(mt=X>=16384?Math.floor(X/2)-1||1:Math.ceil(X/2),O/=X/mt),N>2&&nt>1&&(_t=nt>=16384?Math.floor(nt/2)-1||1:Math.ceil(nt)/2,N/=nt/_t),ft=this.cachedCanvases.getCanvas(ct,mt,_t),bt=ft.context,bt.clearRect(0,0,mt,_t),bt.drawImage(s,0,0,X,nt,0,0,mt,_t),s=ft.canvas,X=mt,nt=_t,ct=ct==="prescale1"?"prescale2":"prescale1"}return{img:s,paintWidth:X,paintHeight:nt}}_createMaskCanvas(s){const a=this.ctx,{width:g,height:L}=s,O=this.current.fillColor,N=this.current.patternFill,X=(0,d.getCurrentTransform)(a);let nt,ct,ft,bt;if((s.bitmap||s.data)&&s.count>1){const tt=s.bitmap||s.data.buffer;ct=JSON.stringify(N?X:[X.slice(0,4),O]),nt=this._cachedBitmapsMap.get(tt),nt||(nt=new Map,this._cachedBitmapsMap.set(tt,nt));const wt=nt.get(ct);if(wt&&!N){const xt=Math.round(Math.min(X[0],X[2])+X[4]),Ut=Math.round(Math.min(X[1],X[3])+X[5]);return{canvas:wt,offsetX:xt,offsetY:Ut}}ft=wt}ft||(bt=this.cachedCanvases.getCanvas("maskCanvas",g,L),_(bt.context,s));let mt=n.Util.transform(X,[1/g,0,0,-1/L,0,0]);mt=n.Util.transform(mt,[1,0,0,1,0,-L]);const _t=n.Util.applyTransform([0,0],mt),J=n.Util.applyTransform([g,L],mt),Q=n.Util.normalizeRect([_t[0],_t[1],J[0],J[1]]),A=Math.round(Q[2]-Q[0])||1,j=Math.round(Q[3]-Q[1])||1,Y=this.cachedCanvases.getCanvas("fillCanvas",A,j),st=Y.context,pt=Math.min(_t[0],J[0]),Et=Math.min(_t[1],J[1]);st.translate(-pt,-Et),st.transform(...mt),ft||(ft=this._scaleImage(bt.canvas,(0,d.getCurrentTransformInverse)(st)),ft=ft.img,nt&&N&&nt.set(ct,ft)),st.imageSmoothingEnabled=v((0,d.getCurrentTransform)(st),s.interpolate),p(st,ft,0,0,ft.width,ft.height,0,0,g,L),st.globalCompositeOperation="source-in";const At=n.Util.transform((0,d.getCurrentTransformInverse)(st),[1,0,0,1,-pt,-Et]);return st.fillStyle=N?O.getPattern(a,this,At,H.PathType.FILL):O,st.fillRect(0,0,g,L),nt&&!N&&(this.cachedCanvases.delete("fillCanvas"),nt.set(ct,Y.canvas)),{canvas:Y.canvas,offsetX:Math.round(pt),offsetY:Math.round(Et)}}setLineWidth(s){s!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1),this.current.lineWidth=s,this.ctx.lineWidth=s}setLineCap(s){this.ctx.lineCap=k[s]}setLineJoin(s){this.ctx.lineJoin=Z[s]}setMiterLimit(s){this.ctx.miterLimit=s}setDash(s,a){const g=this.ctx;g.setLineDash!==void 0&&(g.setLineDash(s),g.lineDashOffset=a)}setRenderingIntent(s){}setFlatness(s){}setGState(s){for(const[a,g]of s)switch(a){case"LW":this.setLineWidth(g);break;case"LC":this.setLineCap(g);break;case"LJ":this.setLineJoin(g);break;case"ML":this.setMiterLimit(g);break;case"D":this.setDash(g[0],g[1]);break;case"RI":this.setRenderingIntent(g);break;case"FL":this.setFlatness(g);break;case"Font":this.setFont(g[0],g[1]);break;case"CA":this.current.strokeAlpha=g;break;case"ca":this.current.fillAlpha=g,this.ctx.globalAlpha=g;break;case"BM":this.ctx.globalCompositeOperation=g;break;case"SMask":this.current.activeSMask=g?this.tempSMask:null,this.tempSMask=null,this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(g);break}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const s=this.inSMaskMode;this.current.activeSMask&&!s?this.beginSMaskMode():!this.current.activeSMask&&s&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const s=this.ctx.canvas.width,a=this.ctx.canvas.height,g="smaskGroupAt"+this.groupLevel,L=this.cachedCanvases.getCanvas(g,s,a);this.suspendedCtx=this.ctx,this.ctx=L.context;const O=this.ctx;O.setTransform(...(0,d.getCurrentTransform)(this.suspendedCtx)),i(this.suspendedCtx,O),m(O,this.suspendedCtx),this.setGState([["BM","source-over"],["ca",1],["CA",1]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring(),i(this.ctx,this.suspendedCtx),this.ctx=this.suspendedCtx,this.suspendedCtx=null}compose(s){if(!this.current.activeSMask)return;s?(s[0]=Math.floor(s[0]),s[1]=Math.floor(s[1]),s[2]=Math.ceil(s[2]),s[3]=Math.ceil(s[3])):s=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const a=this.current.activeSMask,g=this.suspendedCtx;r(g,a,this.ctx,s),this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height),this.ctx.restore()}save(){this.inSMaskMode?(i(this.ctx,this.suspendedCtx),this.suspendedCtx.save()):this.ctx.save();const s=this.current;this.stateStack.push(s),this.current=s.clone()}restore(){this.stateStack.length===0&&this.inSMaskMode&&this.endSMaskMode(),this.stateStack.length!==0&&(this.current=this.stateStack.pop(),this.inSMaskMode?(this.suspendedCtx.restore(),i(this.suspendedCtx,this.ctx)):this.ctx.restore(),this.checkSMaskState(),this.pendingClip=null,this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null)}transform(s,a,g,L,O,N){this.ctx.transform(s,a,g,L,O,N),this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null}constructPath(s,a,g){const L=this.ctx,O=this.current;let N=O.x,X=O.y,nt,ct;const ft=(0,d.getCurrentTransform)(L),bt=ft[0]===0&&ft[3]===0||ft[1]===0&&ft[2]===0,mt=bt?g.slice(0):null;for(let _t=0,J=0,Q=s.length;_t<Q;_t++)switch(s[_t]|0){case n.OPS.rectangle:N=a[J++],X=a[J++];const A=a[J++],j=a[J++],Y=N+A,st=X+j;L.moveTo(N,X),A===0||j===0?L.lineTo(Y,st):(L.lineTo(Y,X),L.lineTo(Y,st),L.lineTo(N,st)),bt||O.updateRectMinMax(ft,[N,X,Y,st]),L.closePath();break;case n.OPS.moveTo:N=a[J++],X=a[J++],L.moveTo(N,X),bt||O.updatePathMinMax(ft,N,X);break;case n.OPS.lineTo:N=a[J++],X=a[J++],L.lineTo(N,X),bt||O.updatePathMinMax(ft,N,X);break;case n.OPS.curveTo:nt=N,ct=X,N=a[J+4],X=a[J+5],L.bezierCurveTo(a[J],a[J+1],a[J+2],a[J+3],N,X),O.updateCurvePathMinMax(ft,nt,ct,a[J],a[J+1],a[J+2],a[J+3],N,X,mt),J+=6;break;case n.OPS.curveTo2:nt=N,ct=X,L.bezierCurveTo(N,X,a[J],a[J+1],a[J+2],a[J+3]),O.updateCurvePathMinMax(ft,nt,ct,N,X,a[J],a[J+1],a[J+2],a[J+3],mt),N=a[J+2],X=a[J+3],J+=4;break;case n.OPS.curveTo3:nt=N,ct=X,N=a[J+2],X=a[J+3],L.bezierCurveTo(a[J],a[J+1],N,X,N,X),O.updateCurvePathMinMax(ft,nt,ct,a[J],a[J+1],N,X,N,X,mt),J+=4;break;case n.OPS.closePath:L.closePath();break}bt&&O.updateScalingPathMinMax(ft,mt),O.setCurrentPoint(N,X)}closePath(){this.ctx.closePath()}stroke(s=!0){const a=this.ctx,g=this.current.strokeColor;a.globalAlpha=this.current.strokeAlpha,this.contentVisible&&(typeof g=="object"&&(g!=null&&g.getPattern)?(a.save(),a.strokeStyle=g.getPattern(a,this,(0,d.getCurrentTransformInverse)(a),H.PathType.STROKE),this.rescaleAndStroke(!1),a.restore()):this.rescaleAndStroke(!0)),s&&this.consumePath(this.current.getClippedPathBoundingBox()),a.globalAlpha=this.current.fillAlpha}closeStroke(){this.closePath(),this.stroke()}fill(s=!0){const a=this.ctx,g=this.current.fillColor,L=this.current.patternFill;let O=!1;L&&(a.save(),a.fillStyle=g.getPattern(a,this,(0,d.getCurrentTransformInverse)(a),H.PathType.FILL),O=!0);const N=this.current.getClippedPathBoundingBox();this.contentVisible&&N!==null&&(this.pendingEOFill?(a.fill("evenodd"),this.pendingEOFill=!1):a.fill()),O&&a.restore(),s&&this.consumePath(N)}eoFill(){this.pendingEOFill=!0,this.fill()}fillStroke(){this.fill(!1),this.stroke(!1),this.consumePath()}eoFillStroke(){this.pendingEOFill=!0,this.fillStroke()}closeFillStroke(){this.closePath(),this.fillStroke()}closeEOFillStroke(){this.pendingEOFill=!0,this.closePath(),this.fillStroke()}endPath(){this.consumePath()}clip(){this.pendingClip=$}eoClip(){this.pendingClip=q}beginText(){this.current.textMatrix=n.IDENTITY_MATRIX,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}endText(){const s=this.pendingTextPaths,a=this.ctx;if(s===void 0){a.beginPath();return}a.save(),a.beginPath();for(const g of s)a.setTransform(...g.transform),a.translate(g.x,g.y),g.addToPath(a,g.fontSize);a.restore(),a.clip(),a.beginPath(),delete this.pendingTextPaths}setCharSpacing(s){this.current.charSpacing=s}setWordSpacing(s){this.current.wordSpacing=s}setHScale(s){this.current.textHScale=s/100}setLeading(s){this.current.leading=-s}setFont(s,a){var ft;const g=this.commonObjs.get(s),L=this.current;if(!g)throw new Error(`Can't find font for ${s}`);if(L.fontMatrix=g.fontMatrix||n.FONT_IDENTITY_MATRIX,(L.fontMatrix[0]===0||L.fontMatrix[3]===0)&&(0,n.warn)("Invalid font matrix for font "+s),a<0?(a=-a,L.fontDirection=-1):L.fontDirection=1,this.current.font=g,this.current.fontSize=a,g.isType3Font)return;const O=g.loadedName||"sans-serif",N=((ft=g.systemFontInfo)==null?void 0:ft.css)||`"${O}", ${g.fallbackName}`;let X="normal";g.black?X="900":g.bold&&(X="bold");const nt=g.italic?"italic":"normal";let ct=a;a<ht?ct=ht:a>P&&(ct=P),this.current.fontSizeScale=a/ct,this.ctx.font=`${nt} ${X} ${ct}px ${N}`}setTextRenderingMode(s){this.current.textRenderingMode=s}setTextRise(s){this.current.textRise=s}moveText(s,a){this.current.x=this.current.lineX+=s,this.current.y=this.current.lineY+=a}setLeadingMoveText(s,a){this.setLeading(-a),this.moveText(s,a)}setTextMatrix(s,a,g,L,O,N){this.current.textMatrix=[s,a,g,L,O,N],this.current.textMatrixScale=Math.hypot(s,a),this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}nextLine(){this.moveText(0,this.current.leading)}paintChar(s,a,g,L){const O=this.ctx,N=this.current,X=N.font,nt=N.textRenderingMode,ct=N.fontSize/N.fontSizeScale,ft=nt&n.TextRenderingMode.FILL_STROKE_MASK,bt=!!(nt&n.TextRenderingMode.ADD_TO_PATH_FLAG),mt=N.patternFill&&!X.missingFile;let _t;(X.disableFontFace||bt||mt)&&(_t=X.getPathGenerator(this.commonObjs,s)),X.disableFontFace||mt?(O.save(),O.translate(a,g),O.beginPath(),_t(O,ct),L&&O.setTransform(...L),(ft===n.TextRenderingMode.FILL||ft===n.TextRenderingMode.FILL_STROKE)&&O.fill(),(ft===n.TextRenderingMode.STROKE||ft===n.TextRenderingMode.FILL_STROKE)&&O.stroke(),O.restore()):((ft===n.TextRenderingMode.FILL||ft===n.TextRenderingMode.FILL_STROKE)&&O.fillText(s,a,g),(ft===n.TextRenderingMode.STROKE||ft===n.TextRenderingMode.FILL_STROKE)&&O.strokeText(s,a,g)),bt&&(this.pendingTextPaths||(this.pendingTextPaths=[])).push({transform:(0,d.getCurrentTransform)(O),x:a,y:g,fontSize:ct,addToPath:_t})}get isFontSubpixelAAEnabled(){const{context:s}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);s.scale(1.5,1),s.fillText("I",0,10);const a=s.getImageData(0,0,10,10).data;let g=!1;for(let L=3;L<a.length;L+=4)if(a[L]>0&&a[L]<255){g=!0;break}return(0,n.shadow)(this,"isFontSubpixelAAEnabled",g)}showText(s){const a=this.current,g=a.font;if(g.isType3Font)return this.showType3Text(s);const L=a.fontSize;if(L===0)return;const O=this.ctx,N=a.fontSizeScale,X=a.charSpacing,nt=a.wordSpacing,ct=a.fontDirection,ft=a.textHScale*ct,bt=s.length,mt=g.vertical,_t=mt?1:-1,J=g.defaultVMetrics,Q=L*a.fontMatrix[0],A=a.textRenderingMode===n.TextRenderingMode.FILL&&!g.disableFontFace&&!a.patternFill;O.save(),O.transform(...a.textMatrix),O.translate(a.x,a.y+a.textRise),ct>0?O.scale(ft,-1):O.scale(ft,1);let j;if(a.patternFill){O.save();const At=a.fillColor.getPattern(O,this,(0,d.getCurrentTransformInverse)(O),H.PathType.FILL);j=(0,d.getCurrentTransform)(O),O.restore(),O.fillStyle=At}let Y=a.lineWidth;const st=a.textMatrixScale;if(st===0||Y===0){const At=a.textRenderingMode&n.TextRenderingMode.FILL_STROKE_MASK;(At===n.TextRenderingMode.STROKE||At===n.TextRenderingMode.FILL_STROKE)&&(Y=this.getSinglePixelWidth())}else Y/=st;if(N!==1&&(O.scale(N,N),Y/=N),O.lineWidth=Y,g.isInvalidPDFjsFont){const At=[];let tt=0;for(const wt of s)At.push(wt.unicode),tt+=wt.width;O.fillText(At.join(""),0,0),a.x+=tt*Q*ft,O.restore(),this.compose();return}let pt=0,Et;for(Et=0;Et<bt;++Et){const At=s[Et];if(typeof At=="number"){pt+=_t*At*L/1e3;continue}let tt=!1;const wt=(At.isSpace?nt:0)+X,xt=At.fontChar,Ut=At.accent;let jt,Vt,kt=At.width;if(mt){const Ht=At.vmetric||J,qt=-(At.vmetric?Ht[1]:kt*.5)*Q,vt=Ht[2]*Q;kt=Ht?-Ht[0]:kt,jt=qt/N,Vt=(pt+vt)/N}else jt=pt/N,Vt=0;if(g.remeasure&&kt>0){const Ht=O.measureText(xt).width*1e3/L*N;if(kt<Ht&&this.isFontSubpixelAAEnabled){const qt=kt/Ht;tt=!0,O.save(),O.scale(qt,1),jt/=qt}else kt!==Ht&&(jt+=(kt-Ht)/2e3*L/N)}if(this.contentVisible&&(At.isInFont||g.missingFile)){if(A&&!Ut)O.fillText(xt,jt,Vt);else if(this.paintChar(xt,jt,Vt,j),Ut){const Ht=jt+L*Ut.offset.x/N,qt=Vt-L*Ut.offset.y/N;this.paintChar(Ut.fontChar,Ht,qt,j)}}const Ot=mt?kt*Q-wt*ct:kt*Q+wt*ct;pt+=Ot,tt&&O.restore()}mt?a.y-=pt:a.x+=pt*ft,O.restore(),this.compose()}showType3Text(s){const a=this.ctx,g=this.current,L=g.font,O=g.fontSize,N=g.fontDirection,X=L.vertical?1:-1,nt=g.charSpacing,ct=g.wordSpacing,ft=g.textHScale*N,bt=g.fontMatrix||n.FONT_IDENTITY_MATRIX,mt=s.length,_t=g.textRenderingMode===n.TextRenderingMode.INVISIBLE;let J,Q,A,j;if(!(_t||O===0)){for(this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null,a.save(),a.transform(...g.textMatrix),a.translate(g.x,g.y),a.scale(ft,N),J=0;J<mt;++J){if(Q=s[J],typeof Q=="number"){j=X*Q*O/1e3,this.ctx.translate(j,0),g.x+=j*ft;continue}const Y=(Q.isSpace?ct:0)+nt,st=L.charProcOperatorList[Q.operatorListId];if(!st){(0,n.warn)(`Type3 character "${Q.operatorListId}" is not available.`);continue}this.contentVisible&&(this.processingType3=Q,this.save(),a.scale(O,O),a.transform(...bt),this.executeOperatorList(st),this.restore()),A=n.Util.applyTransform([Q.width,0],bt)[0]*O+Y,a.translate(A,0),g.x+=A*ft}a.restore(),this.processingType3=null}}setCharWidth(s,a){}setCharWidthAndBounds(s,a,g,L,O,N){this.ctx.rect(g,L,O-g,N-L),this.ctx.clip(),this.endPath()}getColorN_Pattern(s){let a;if(s[0]==="TilingPattern"){const g=s[1],L=this.baseTransform||(0,d.getCurrentTransform)(this.ctx),O={createCanvasGraphics:N=>new et(N,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})};a=new H.TilingPattern(s,g,this.ctx,O,L)}else a=this._getPattern(s[1],s[2]);return a}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments)}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0}setStrokeRGBColor(s,a,g){const L=n.Util.makeHexColor(s,a,g);this.ctx.strokeStyle=L,this.current.strokeColor=L}setFillRGBColor(s,a,g){const L=n.Util.makeHexColor(s,a,g);this.ctx.fillStyle=L,this.current.fillColor=L,this.current.patternFill=!1}_getPattern(s,a=null){let g;return this.cachedPatterns.has(s)?g=this.cachedPatterns.get(s):(g=(0,H.getShadingPattern)(this.getObject(s)),this.cachedPatterns.set(s,g)),a&&(g.matrix=a),g}shadingFill(s){if(!this.contentVisible)return;const a=this.ctx;this.save();const g=this._getPattern(s);a.fillStyle=g.getPattern(a,this,(0,d.getCurrentTransformInverse)(a),H.PathType.SHADING);const L=(0,d.getCurrentTransformInverse)(a);if(L){const{width:O,height:N}=a.canvas,[X,nt,ct,ft]=n.Util.getAxialAlignedBoundingBox([0,0,O,N],L);this.ctx.fillRect(X,nt,ct-X,ft-nt)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox()),this.restore()}beginInlineImage(){(0,n.unreachable)("Should not call beginInlineImage")}beginImageData(){(0,n.unreachable)("Should not call beginImageData")}paintFormXObjectBegin(s,a){if(this.contentVisible&&(this.save(),this.baseTransformStack.push(this.baseTransform),Array.isArray(s)&&s.length===6&&this.transform(...s),this.baseTransform=(0,d.getCurrentTransform)(this.ctx),a)){const g=a[2]-a[0],L=a[3]-a[1];this.ctx.rect(a[0],a[1],g,L),this.current.updateRectMinMax((0,d.getCurrentTransform)(this.ctx),a),this.clip(),this.endPath()}}paintFormXObjectEnd(){this.contentVisible&&(this.restore(),this.baseTransform=this.baseTransformStack.pop())}beginGroup(s){if(!this.contentVisible)return;this.save(),this.inSMaskMode&&(this.endSMaskMode(),this.current.activeSMask=null);const a=this.ctx;s.isolated||(0,n.info)("TODO: Support non-isolated groups."),s.knockout&&(0,n.warn)("Knockout groups not supported.");const g=(0,d.getCurrentTransform)(a);if(s.matrix&&a.transform(...s.matrix),!s.bbox)throw new Error("Bounding box is required.");let L=n.Util.getAxialAlignedBoundingBox(s.bbox,(0,d.getCurrentTransform)(a));const O=[0,0,a.canvas.width,a.canvas.height];L=n.Util.intersect(L,O)||[0,0,0,0];const N=Math.floor(L[0]),X=Math.floor(L[1]);let nt=Math.max(Math.ceil(L[2])-N,1),ct=Math.max(Math.ceil(L[3])-X,1),ft=1,bt=1;nt>x&&(ft=nt/x,nt=x),ct>x&&(bt=ct/x,ct=x),this.current.startNewPathAndClipBox([0,0,nt,ct]);let mt="groupAt"+this.groupLevel;s.smask&&(mt+="_smask_"+this.smaskCounter++%2);const _t=this.cachedCanvases.getCanvas(mt,nt,ct),J=_t.context;J.scale(1/ft,1/bt),J.translate(-N,-X),J.transform(...g),s.smask?this.smaskStack.push({canvas:_t.canvas,context:J,offsetX:N,offsetY:X,scaleX:ft,scaleY:bt,subtype:s.smask.subtype,backdrop:s.smask.backdrop,transferMap:s.smask.transferMap||null,startTransformInverse:null}):(a.setTransform(1,0,0,1,0,0),a.translate(N,X),a.scale(ft,bt),a.save()),i(a,J),this.ctx=J,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(a),this.groupLevel++}endGroup(s){if(!this.contentVisible)return;this.groupLevel--;const a=this.ctx,g=this.groupStack.pop();if(this.ctx=g,this.ctx.imageSmoothingEnabled=!1,s.smask)this.tempSMask=this.smaskStack.pop(),this.restore();else{this.ctx.restore();const L=(0,d.getCurrentTransform)(this.ctx);this.restore(),this.ctx.save(),this.ctx.setTransform(...L);const O=n.Util.getAxialAlignedBoundingBox([0,0,a.canvas.width,a.canvas.height],L);this.ctx.drawImage(a.canvas,0,0),this.ctx.restore(),this.compose(O)}}beginAnnotation(s,a,g,L,O){if(K(this,it,qe).call(this),c(this.ctx),this.ctx.save(),this.save(),this.baseTransform&&this.ctx.setTransform(...this.baseTransform),Array.isArray(a)&&a.length===4){const N=a[2]-a[0],X=a[3]-a[1];if(O&&this.annotationCanvasMap){g=g.slice(),g[4]-=a[0],g[5]-=a[1],a=a.slice(),a[0]=a[1]=0,a[2]=N,a[3]=X;const[nt,ct]=n.Util.singularValueDecompose2dScale((0,d.getCurrentTransform)(this.ctx)),{viewportScale:ft}=this,bt=Math.ceil(N*this.outputScaleX*ft),mt=Math.ceil(X*this.outputScaleY*ft);this.annotationCanvas=this.canvasFactory.create(bt,mt);const{canvas:_t,context:J}=this.annotationCanvas;this.annotationCanvasMap.set(s,_t),this.annotationCanvas.savedCtx=this.ctx,this.ctx=J,this.ctx.save(),this.ctx.setTransform(nt,0,0,-ct,0,X*ct),c(this.ctx)}else c(this.ctx),this.ctx.rect(a[0],a[1],N,X),this.ctx.clip(),this.endPath()}this.current=new b(this.ctx.canvas.width,this.ctx.canvas.height),this.transform(...g),this.transform(...L)}endAnnotation(){this.annotationCanvas&&(this.ctx.restore(),K(this,it,Xe).call(this),this.ctx=this.annotationCanvas.savedCtx,delete this.annotationCanvas.savedCtx,delete this.annotationCanvas)}paintImageMaskXObject(s){if(!this.contentVisible)return;const a=s.count;s=this.getObject(s.data,s),s.count=a;const g=this.ctx,L=this.processingType3;if(L&&(L.compiled===void 0&&(L.compiled=u(s)),L.compiled)){L.compiled(g);return}const O=this._createMaskCanvas(s),N=O.canvas;g.save(),g.setTransform(1,0,0,1,0,0),g.drawImage(N,O.offsetX,O.offsetY),g.restore(),this.compose()}paintImageMaskXObjectRepeat(s,a,g=0,L=0,O,N){if(!this.contentVisible)return;s=this.getObject(s.data,s);const X=this.ctx;X.save();const nt=(0,d.getCurrentTransform)(X);X.transform(a,g,L,O,0,0);const ct=this._createMaskCanvas(s);X.setTransform(1,0,0,1,ct.offsetX-nt[4],ct.offsetY-nt[5]);for(let ft=0,bt=N.length;ft<bt;ft+=2){const mt=n.Util.transform(nt,[a,g,L,O,N[ft],N[ft+1]]),[_t,J]=n.Util.applyTransform([0,0],mt);X.drawImage(ct.canvas,_t,J)}X.restore(),this.compose()}paintImageMaskXObjectGroup(s){if(!this.contentVisible)return;const a=this.ctx,g=this.current.fillColor,L=this.current.patternFill;for(const O of s){const{data:N,width:X,height:nt,transform:ct}=O,ft=this.cachedCanvases.getCanvas("maskCanvas",X,nt),bt=ft.context;bt.save();const mt=this.getObject(N,O);_(bt,mt),bt.globalCompositeOperation="source-in",bt.fillStyle=L?g.getPattern(bt,this,(0,d.getCurrentTransformInverse)(a),H.PathType.FILL):g,bt.fillRect(0,0,X,nt),bt.restore(),a.save(),a.transform(...ct),a.scale(1,-1),p(a,ft.canvas,0,0,X,nt,0,-1,1,1),a.restore()}this.compose()}paintImageXObject(s){if(!this.contentVisible)return;const a=this.getObject(s);if(!a){(0,n.warn)("Dependent image isn't ready yet");return}this.paintInlineImageXObject(a)}paintImageXObjectRepeat(s,a,g,L){if(!this.contentVisible)return;const O=this.getObject(s);if(!O){(0,n.warn)("Dependent image isn't ready yet");return}const N=O.width,X=O.height,nt=[];for(let ct=0,ft=L.length;ct<ft;ct+=2)nt.push({transform:[a,0,0,g,L[ct],L[ct+1]],x:0,y:0,w:N,h:X});this.paintInlineImageXObjectGroup(O,nt)}applyTransferMapsToCanvas(s){return this.current.transferMaps!=="none"&&(s.filter=this.current.transferMaps,s.drawImage(s.canvas,0,0),s.filter="none"),s.canvas}applyTransferMapsToBitmap(s){if(this.current.transferMaps==="none")return s.bitmap;const{bitmap:a,width:g,height:L}=s,O=this.cachedCanvases.getCanvas("inlineImage",g,L),N=O.context;return N.filter=this.current.transferMaps,N.drawImage(a,0,0),N.filter="none",O.canvas}paintInlineImageXObject(s){if(!this.contentVisible)return;const a=s.width,g=s.height,L=this.ctx;if(this.save(),!n.isNodeJS){const{filter:X}=L;X!=="none"&&X!==""&&(L.filter="none")}L.scale(1/a,-1/g);let O;if(s.bitmap)O=this.applyTransferMapsToBitmap(s);else if(typeof HTMLElement=="function"&&s instanceof HTMLElement||!s.data)O=s;else{const nt=this.cachedCanvases.getCanvas("inlineImage",a,g).context;C(nt,s),O=this.applyTransferMapsToCanvas(nt)}const N=this._scaleImage(O,(0,d.getCurrentTransformInverse)(L));L.imageSmoothingEnabled=v((0,d.getCurrentTransform)(L),s.interpolate),p(L,N.img,0,0,N.paintWidth,N.paintHeight,0,-g,a,g),this.compose(),this.restore()}paintInlineImageXObjectGroup(s,a){if(!this.contentVisible)return;const g=this.ctx;let L;if(s.bitmap)L=s.bitmap;else{const O=s.width,N=s.height,nt=this.cachedCanvases.getCanvas("inlineImage",O,N).context;C(nt,s),L=this.applyTransferMapsToCanvas(nt)}for(const O of a)g.save(),g.transform(...O.transform),g.scale(1,-1),p(g,L,O.x,O.y,O.w,O.h,0,-1,1,1),g.restore();this.compose()}paintSolidColorImageMask(){this.contentVisible&&(this.ctx.fillRect(0,0,1,1),this.compose())}markPoint(s){}markPointProps(s,a){}beginMarkedContent(s){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(s,a){s==="OC"?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(a)}):this.markedContentStack.push({visible:!0}),this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop(),this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(s){const a=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath(),this.pendingClip||this.compose(s);const g=this.ctx;this.pendingClip&&(a||(this.pendingClip===q?g.clip("evenodd"):g.clip()),this.pendingClip=null),this.current.startNewPathAndClipBox(this.current.clipBox),g.beginPath()}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const s=(0,d.getCurrentTransform)(this.ctx);if(s[1]===0&&s[2]===0)this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(s[0]),Math.abs(s[3]));else{const a=Math.abs(s[0]*s[3]-s[2]*s[1]),g=Math.hypot(s[0],s[2]),L=Math.hypot(s[1],s[3]);this._cachedGetSinglePixelWidth=Math.max(g,L)/a}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(this._cachedScaleForStroking[0]===-1){const{lineWidth:s}=this.current,{a,b:g,c:L,d:O}=this.ctx.getTransform();let N,X;if(g===0&&L===0){const nt=Math.abs(a),ct=Math.abs(O);if(nt===ct)if(s===0)N=X=1/nt;else{const ft=nt*s;N=X=ft<1?1/ft:1}else if(s===0)N=1/nt,X=1/ct;else{const ft=nt*s,bt=ct*s;N=ft<1?1/ft:1,X=bt<1?1/bt:1}}else{const nt=Math.abs(a*O-g*L),ct=Math.hypot(a,g),ft=Math.hypot(L,O);if(s===0)N=ft/nt,X=ct/nt;else{const bt=s*nt;N=ft>bt?ft/bt:1,X=ct>bt?ct/bt:1}}this._cachedScaleForStroking[0]=N,this._cachedScaleForStroking[1]=X}return this._cachedScaleForStroking}rescaleAndStroke(s){const{ctx:a}=this,{lineWidth:g}=this.current,[L,O]=this.getScaleForStroking();if(a.lineWidth=g||1,L===1&&O===1){a.stroke();return}const N=a.getLineDash();if(s&&a.save(),a.scale(L,O),N.length>0){const X=Math.max(L,O);a.setLineDash(N.map(nt=>nt/X)),a.lineDashOffset/=X}a.stroke(),s&&a.restore()}isContentVisible(){for(let s=this.markedContentStack.length-1;s>=0;s--)if(!this.markedContentStack[s].visible)return!1;return!0}};it=new WeakSet,qe=function(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null)},Xe=function(){if(this.pageColors){const s=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if(s!=="none"){const a=this.ctx.filter;this.ctx.filter=s,this.ctx.drawImage(this.ctx.canvas,0,0),this.ctx.filter=a}}};let G=et;e.CanvasGraphics=G;for(const S in n.OPS)G.prototype[S]!==void 0&&(G.prototype[n.OPS[S]]=G.prototype[S])},(T,e,B)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.TilingPattern=e.PathType=void 0,e.getShadingPattern=y;var n=B(1),d=B(6);const H={FILL:"Fill",STROKE:"Stroke",SHADING:"Shading"};e.PathType=H;function W(u,b){if(!b)return;const C=b[2]-b[0],_=b[3]-b[1],i=new Path2D;i.rect(b[0],b[1],C,_),u.clip(i)}class ht{constructor(){this.constructor===ht&&(0,n.unreachable)("Cannot initialize BaseShadingPattern.")}getPattern(){(0,n.unreachable)("Abstract method `getPattern` called.")}}class P extends ht{constructor(b){super(),this._type=b[1],this._bbox=b[2],this._colorStops=b[3],this._p0=b[4],this._p1=b[5],this._r0=b[6],this._r1=b[7],this.matrix=null}_createGradient(b){let C;this._type==="axial"?C=b.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):this._type==="radial"&&(C=b.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const _ of this._colorStops)C.addColorStop(_[0],_[1]);return C}getPattern(b,C,_,i){let c;if(i===H.STROKE||i===H.FILL){const o=C.current.getClippedPathBoundingBox(i,(0,d.getCurrentTransform)(b))||[0,0,0,0],l=Math.ceil(o[2]-o[0])||1,h=Math.ceil(o[3]-o[1])||1,F=C.cachedCanvases.getCanvas("pattern",l,h,!0),r=F.context;r.clearRect(0,0,r.canvas.width,r.canvas.height),r.beginPath(),r.rect(0,0,r.canvas.width,r.canvas.height),r.translate(-o[0],-o[1]),_=n.Util.transform(_,[1,0,0,1,o[0],o[1]]),r.transform(...C.baseTransform),this.matrix&&r.transform(...this.matrix),W(r,this._bbox),r.fillStyle=this._createGradient(r),r.fill(),c=b.createPattern(F.canvas,"no-repeat");const v=new DOMMatrix(_);c.setTransform(v)}else W(b,this._bbox),c=this._createGradient(b);return c}}function x(u,b,C,_,i,c,o,l){const h=b.coords,F=b.colors,r=u.data,v=u.width*4;let k;h[C+1]>h[_+1]&&(k=C,C=_,_=k,k=c,c=o,o=k),h[_+1]>h[i+1]&&(k=_,_=i,i=k,k=o,o=l,l=k),h[C+1]>h[_+1]&&(k=C,C=_,_=k,k=c,c=o,o=k);const Z=(h[C]+b.offsetX)*b.scaleX,$=(h[C+1]+b.offsetY)*b.scaleY,q=(h[_]+b.offsetX)*b.scaleX,G=(h[_+1]+b.offsetY)*b.scaleY,it=(h[i]+b.offsetX)*b.scaleX,R=(h[i+1]+b.offsetY)*b.scaleY;if($>=R)return;const V=F[c],et=F[c+1],S=F[c+2],s=F[o],a=F[o+1],g=F[o+2],L=F[l],O=F[l+1],N=F[l+2],X=Math.round($),nt=Math.round(R);let ct,ft,bt,mt,_t,J,Q,A;for(let j=X;j<=nt;j++){if(j<G){const At=j<$?0:($-j)/($-G);ct=Z-(Z-q)*At,ft=V-(V-s)*At,bt=et-(et-a)*At,mt=S-(S-g)*At}else{let At;j>R?At=1:G===R?At=0:At=(G-j)/(G-R),ct=q-(q-it)*At,ft=s-(s-L)*At,bt=a-(a-O)*At,mt=g-(g-N)*At}let Y;j<$?Y=0:j>R?Y=1:Y=($-j)/($-R),_t=Z-(Z-it)*Y,J=V-(V-L)*Y,Q=et-(et-O)*Y,A=S-(S-N)*Y;const st=Math.round(Math.min(ct,_t)),pt=Math.round(Math.max(ct,_t));let Et=v*j+st*4;for(let At=st;At<=pt;At++)Y=(ct-At)/(ct-_t),Y<0?Y=0:Y>1&&(Y=1),r[Et++]=ft-(ft-J)*Y|0,r[Et++]=bt-(bt-Q)*Y|0,r[Et++]=mt-(mt-A)*Y|0,r[Et++]=255}}function f(u,b,C){const _=b.coords,i=b.colors;let c,o;switch(b.type){case"lattice":const l=b.verticesPerRow,h=Math.floor(_.length/l)-1,F=l-1;for(c=0;c<h;c++){let r=c*l;for(let v=0;v<F;v++,r++)x(u,C,_[r],_[r+1],_[r+l],i[r],i[r+1],i[r+l]),x(u,C,_[r+l+1],_[r+1],_[r+l],i[r+l+1],i[r+1],i[r+l])}break;case"triangles":for(c=0,o=_.length;c<o;c+=3)x(u,C,_[c],_[c+1],_[c+2],i[c],i[c+1],i[c+2]);break;default:throw new Error("illegal figure")}}class D extends ht{constructor(b){super(),this._coords=b[2],this._colors=b[3],this._figures=b[4],this._bounds=b[5],this._bbox=b[7],this._background=b[8],this.matrix=null}_createMeshCanvas(b,C,_){const l=Math.floor(this._bounds[0]),h=Math.floor(this._bounds[1]),F=Math.ceil(this._bounds[2])-l,r=Math.ceil(this._bounds[3])-h,v=Math.min(Math.ceil(Math.abs(F*b[0]*1.1)),3e3),k=Math.min(Math.ceil(Math.abs(r*b[1]*1.1)),3e3),Z=F/v,$=r/k,q={coords:this._coords,colors:this._colors,offsetX:-l,offsetY:-h,scaleX:1/Z,scaleY:1/$},G=v+2*2,it=k+2*2,R=_.getCanvas("mesh",G,it,!1),V=R.context,et=V.createImageData(v,k);if(C){const s=et.data;for(let a=0,g=s.length;a<g;a+=4)s[a]=C[0],s[a+1]=C[1],s[a+2]=C[2],s[a+3]=255}for(const s of this._figures)f(et,s,q);return V.putImageData(et,2,2),{canvas:R.canvas,offsetX:l-2*Z,offsetY:h-2*$,scaleX:Z,scaleY:$}}getPattern(b,C,_,i){W(b,this._bbox);let c;if(i===H.SHADING)c=n.Util.singularValueDecompose2dScale((0,d.getCurrentTransform)(b));else if(c=n.Util.singularValueDecompose2dScale(C.baseTransform),this.matrix){const l=n.Util.singularValueDecompose2dScale(this.matrix);c=[c[0]*l[0],c[1]*l[1]]}const o=this._createMeshCanvas(c,i===H.SHADING?null:this._background,C.cachedCanvases);return i!==H.SHADING&&(b.setTransform(...C.baseTransform),this.matrix&&b.transform(...this.matrix)),b.translate(o.offsetX,o.offsetY),b.scale(o.scaleX,o.scaleY),b.createPattern(o.canvas,"no-repeat")}}class I extends ht{getPattern(){return"hotpink"}}function y(u){switch(u[0]){case"RadialAxial":return new P(u);case"Mesh":return new D(u);case"Dummy":return new I}throw new Error(`Unknown IR type: ${u[0]}`)}const m={COLORED:1,UNCOLORED:2},p=class p{constructor(b,C,_,i,c){this.operatorList=b[2],this.matrix=b[3]||[1,0,0,1,0,0],this.bbox=b[4],this.xstep=b[5],this.ystep=b[6],this.paintType=b[7],this.tilingType=b[8],this.color=C,this.ctx=_,this.canvasGraphicsFactory=i,this.baseTransform=c}createPatternCanvas(b){const C=this.operatorList,_=this.bbox,i=this.xstep,c=this.ystep,o=this.paintType,l=this.tilingType,h=this.color,F=this.canvasGraphicsFactory;(0,n.info)("TilingType: "+l);const r=_[0],v=_[1],k=_[2],Z=_[3],$=n.Util.singularValueDecompose2dScale(this.matrix),q=n.Util.singularValueDecompose2dScale(this.baseTransform),G=[$[0]*q[0],$[1]*q[1]],it=this.getSizeAndScale(i,this.ctx.canvas.width,G[0]),R=this.getSizeAndScale(c,this.ctx.canvas.height,G[1]),V=b.cachedCanvases.getCanvas("pattern",it.size,R.size,!0),et=V.context,S=F.createCanvasGraphics(et);S.groupLevel=b.groupLevel,this.setFillAndStrokeStyleToContext(S,o,h);let s=r,a=v,g=k,L=Z;return r<0&&(s=0,g+=Math.abs(r)),v<0&&(a=0,L+=Math.abs(v)),et.translate(-(it.scale*s),-(R.scale*a)),S.transform(it.scale,0,0,R.scale,0,0),et.save(),this.clipBbox(S,s,a,g,L),S.baseTransform=(0,d.getCurrentTransform)(S.ctx),S.executeOperatorList(C),S.endDrawing(),{canvas:V.canvas,scaleX:it.scale,scaleY:R.scale,offsetX:s,offsetY:a}}getSizeAndScale(b,C,_){b=Math.abs(b);const i=Math.max(p.MAX_PATTERN_SIZE,C);let c=Math.ceil(b*_);return c>=i?c=i:_=c/b,{scale:_,size:c}}clipBbox(b,C,_,i,c){const o=i-C,l=c-_;b.ctx.rect(C,_,o,l),b.current.updateRectMinMax((0,d.getCurrentTransform)(b.ctx),[C,_,i,c]),b.clip(),b.endPath()}setFillAndStrokeStyleToContext(b,C,_){const i=b.ctx,c=b.current;switch(C){case m.COLORED:const o=this.ctx;i.fillStyle=o.fillStyle,i.strokeStyle=o.strokeStyle,c.fillColor=o.fillStyle,c.strokeColor=o.strokeStyle;break;case m.UNCOLORED:const l=n.Util.makeHexColor(_[0],_[1],_[2]);i.fillStyle=l,i.strokeStyle=l,c.fillColor=l,c.strokeColor=l;break;default:throw new n.FormatError(`Unsupported paint type: ${C}`)}}getPattern(b,C,_,i){let c=_;i!==H.SHADING&&(c=n.Util.transform(c,C.baseTransform),this.matrix&&(c=n.Util.transform(c,this.matrix)));const o=this.createPatternCanvas(C);let l=new DOMMatrix(c);l=l.translate(o.offsetX,o.offsetY),l=l.scale(1/o.scaleX,1/o.scaleY);const h=b.createPattern(o.canvas,"repeat");return h.setTransform(l),h}};Kt(p,"MAX_PATTERN_SIZE",3e3);let E=p;e.TilingPattern=E},(T,e,B)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.convertBlackAndWhiteToRGBA=H,e.convertToRGBA=d,e.grayToRGBA=ht;var n=B(1);function d(P){switch(P.kind){case n.ImageKind.GRAYSCALE_1BPP:return H(P);case n.ImageKind.RGB_24BPP:return W(P)}return null}function H({src:P,srcPos:x=0,dest:f,width:D,height:I,nonBlackColor:y=4294967295,inverseDecode:m=!1}){const E=n.FeatureTest.isLittleEndian?4278190080:255,[p,u]=m?[y,E]:[E,y],b=D>>3,C=D&7,_=P.length;f=new Uint32Array(f.buffer);let i=0;for(let c=0;c<I;c++){for(const l=x+b;x<l;x++){const h=x<_?P[x]:255;f[i++]=h&128?u:p,f[i++]=h&64?u:p,f[i++]=h&32?u:p,f[i++]=h&16?u:p,f[i++]=h&8?u:p,f[i++]=h&4?u:p,f[i++]=h&2?u:p,f[i++]=h&1?u:p}if(C===0)continue;const o=x<_?P[x++]:255;for(let l=0;l<C;l++)f[i++]=o&1<<7-l?u:p}return{srcPos:x,destPos:i}}function W({src:P,srcPos:x=0,dest:f,destPos:D=0,width:I,height:y}){let m=0;const E=P.length>>2,p=new Uint32Array(P.buffer,x,E);if(n.FeatureTest.isLittleEndian){for(;m<E-2;m+=3,D+=4){const u=p[m],b=p[m+1],C=p[m+2];f[D]=u|4278190080,f[D+1]=u>>>24|b<<8|4278190080,f[D+2]=b>>>16|C<<16|4278190080,f[D+3]=C>>>8|4278190080}for(let u=m*4,b=P.length;u<b;u+=3)f[D++]=P[u]|P[u+1]<<8|P[u+2]<<16|4278190080}else{for(;m<E-2;m+=3,D+=4){const u=p[m],b=p[m+1],C=p[m+2];f[D]=u|255,f[D+1]=u<<24|b>>>8|255,f[D+2]=b<<16|C>>>16|255,f[D+3]=C<<8|255}for(let u=m*4,b=P.length;u<b;u+=3)f[D++]=P[u]<<24|P[u+1]<<16|P[u+2]<<8|255}return{srcPos:x,destPos:D}}function ht(P,x){if(n.FeatureTest.isLittleEndian)for(let f=0,D=P.length;f<D;f++)x[f]=P[f]*65793|4278190080;else for(let f=0,D=P.length;f<D;f++)x[f]=P[f]*16843008|255}},(T,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.GlobalWorkerOptions=void 0;const B=Object.create(null);e.GlobalWorkerOptions=B,B.workerPort=null,B.workerSrc=""},(T,e,B)=>{var P,wn,Tn,Se;Object.defineProperty(e,"__esModule",{value:!0}),e.MessageHandler=void 0;var n=B(1);const d={DATA:1,ERROR:2},H={CANCEL:1,CANCEL_COMPLETE:2,CLOSE:3,ENQUEUE:4,ERROR:5,PULL:6,PULL_COMPLETE:7,START_COMPLETE:8};function W(I){switch(I instanceof Error||typeof I=="object"&&I!==null||(0,n.unreachable)('wrapReason: Expected "reason" to be a (possibly cloned) Error.'),I.name){case"AbortException":return new n.AbortException(I.message);case"MissingPDFException":return new n.MissingPDFException(I.message);case"PasswordException":return new n.PasswordException(I.message,I.code);case"UnexpectedResponseException":return new n.UnexpectedResponseException(I.message,I.status);case"UnknownErrorException":return new n.UnknownErrorException(I.message,I.details);default:return new n.UnknownErrorException(I.message,I.toString())}}class ht{constructor(y,m,E){rt(this,P);this.sourceName=y,this.targetName=m,this.comObj=E,this.callbackId=1,this.streamId=1,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null),this.callbackCapabilities=Object.create(null),this.actionHandler=Object.create(null),this._onComObjOnMessage=p=>{const u=p.data;if(u.targetName!==this.sourceName)return;if(u.stream){K(this,P,Tn).call(this,u);return}if(u.callback){const C=u.callbackId,_=this.callbackCapabilities[C];if(!_)throw new Error(`Cannot resolve callback ${C}`);if(delete this.callbackCapabilities[C],u.callback===d.DATA)_.resolve(u.data);else if(u.callback===d.ERROR)_.reject(W(u.reason));else throw new Error("Unexpected callback case");return}const b=this.actionHandler[u.action];if(!b)throw new Error(`Unknown action from worker: ${u.action}`);if(u.callbackId){const C=this.sourceName,_=u.sourceName;new Promise(function(i){i(b(u.data))}).then(function(i){E.postMessage({sourceName:C,targetName:_,callback:d.DATA,callbackId:u.callbackId,data:i})},function(i){E.postMessage({sourceName:C,targetName:_,callback:d.ERROR,callbackId:u.callbackId,reason:W(i)})});return}if(u.streamId){K(this,P,wn).call(this,u);return}b(u.data)},E.addEventListener("message",this._onComObjOnMessage)}on(y,m){const E=this.actionHandler;if(E[y])throw new Error(`There is already an actionName called "${y}"`);E[y]=m}send(y,m,E){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:y,data:m},E)}sendWithPromise(y,m,E){const p=this.callbackId++,u=new n.PromiseCapability;this.callbackCapabilities[p]=u;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:y,callbackId:p,data:m},E)}catch(b){u.reject(b)}return u.promise}sendWithStream(y,m,E,p){const u=this.streamId++,b=this.sourceName,C=this.targetName,_=this.comObj;return new ReadableStream({start:i=>{const c=new n.PromiseCapability;return this.streamControllers[u]={controller:i,startCall:c,pullCall:null,cancelCall:null,isClosed:!1},_.postMessage({sourceName:b,targetName:C,action:y,streamId:u,data:m,desiredSize:i.desiredSize},p),c.promise},pull:i=>{const c=new n.PromiseCapability;return this.streamControllers[u].pullCall=c,_.postMessage({sourceName:b,targetName:C,stream:H.PULL,streamId:u,desiredSize:i.desiredSize}),c.promise},cancel:i=>{(0,n.assert)(i instanceof Error,"cancel must have a valid reason");const c=new n.PromiseCapability;return this.streamControllers[u].cancelCall=c,this.streamControllers[u].isClosed=!0,_.postMessage({sourceName:b,targetName:C,stream:H.CANCEL,streamId:u,reason:W(i)}),c.promise}},E)}destroy(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}}P=new WeakSet,wn=function(y){const m=y.streamId,E=this.sourceName,p=y.sourceName,u=this.comObj,b=this,C=this.actionHandler[y.action],_={enqueue(i,c=1,o){if(this.isCancelled)return;const l=this.desiredSize;this.desiredSize-=c,l>0&&this.desiredSize<=0&&(this.sinkCapability=new n.PromiseCapability,this.ready=this.sinkCapability.promise),u.postMessage({sourceName:E,targetName:p,stream:H.ENQUEUE,streamId:m,chunk:i},o)},close(){this.isCancelled||(this.isCancelled=!0,u.postMessage({sourceName:E,targetName:p,stream:H.CLOSE,streamId:m}),delete b.streamSinks[m])},error(i){(0,n.assert)(i instanceof Error,"error must have a valid reason"),!this.isCancelled&&(this.isCancelled=!0,u.postMessage({sourceName:E,targetName:p,stream:H.ERROR,streamId:m,reason:W(i)}))},sinkCapability:new n.PromiseCapability,onPull:null,onCancel:null,isCancelled:!1,desiredSize:y.desiredSize,ready:null};_.sinkCapability.resolve(),_.ready=_.sinkCapability.promise,this.streamSinks[m]=_,new Promise(function(i){i(C(y.data,_))}).then(function(){u.postMessage({sourceName:E,targetName:p,stream:H.START_COMPLETE,streamId:m,success:!0})},function(i){u.postMessage({sourceName:E,targetName:p,stream:H.START_COMPLETE,streamId:m,reason:W(i)})})},Tn=function(y){const m=y.streamId,E=this.sourceName,p=y.sourceName,u=this.comObj,b=this.streamControllers[m],C=this.streamSinks[m];switch(y.stream){case H.START_COMPLETE:y.success?b.startCall.resolve():b.startCall.reject(W(y.reason));break;case H.PULL_COMPLETE:y.success?b.pullCall.resolve():b.pullCall.reject(W(y.reason));break;case H.PULL:if(!C){u.postMessage({sourceName:E,targetName:p,stream:H.PULL_COMPLETE,streamId:m,success:!0});break}C.desiredSize<=0&&y.desiredSize>0&&C.sinkCapability.resolve(),C.desiredSize=y.desiredSize,new Promise(function(_){var i;_((i=C.onPull)==null?void 0:i.call(C))}).then(function(){u.postMessage({sourceName:E,targetName:p,stream:H.PULL_COMPLETE,streamId:m,success:!0})},function(_){u.postMessage({sourceName:E,targetName:p,stream:H.PULL_COMPLETE,streamId:m,reason:W(_)})});break;case H.ENQUEUE:if((0,n.assert)(b,"enqueue should have stream controller"),b.isClosed)break;b.controller.enqueue(y.chunk);break;case H.CLOSE:if((0,n.assert)(b,"close should have stream controller"),b.isClosed)break;b.isClosed=!0,b.controller.close(),K(this,P,Se).call(this,b,m);break;case H.ERROR:(0,n.assert)(b,"error should have stream controller"),b.controller.error(W(y.reason)),K(this,P,Se).call(this,b,m);break;case H.CANCEL_COMPLETE:y.success?b.cancelCall.resolve():b.cancelCall.reject(W(y.reason)),K(this,P,Se).call(this,b,m);break;case H.CANCEL:if(!C)break;new Promise(function(_){var i;_((i=C.onCancel)==null?void 0:i.call(C,W(y.reason)))}).then(function(){u.postMessage({sourceName:E,targetName:p,stream:H.CANCEL_COMPLETE,streamId:m,success:!0})},function(_){u.postMessage({sourceName:E,targetName:p,stream:H.CANCEL_COMPLETE,streamId:m,reason:W(_)})}),C.sinkCapability.reject(W(y.reason)),C.isCancelled=!0,delete this.streamSinks[m];break;default:throw new Error("Unexpected stream case")}},Se=async function(y,m){var E,p,u;await Promise.allSettled([(E=y.startCall)==null?void 0:E.promise,(p=y.pullCall)==null?void 0:p.promise,(u=y.cancelCall)==null?void 0:u.promise]),delete this.streamControllers[m]},e.MessageHandler=ht},(T,e,B)=>{var H,W;Object.defineProperty(e,"__esModule",{value:!0}),e.Metadata=void 0;var n=B(1);class d{constructor({parsedData:P,rawData:x}){rt(this,H);rt(this,W);ot(this,H,P),ot(this,W,x)}getRaw(){return t(this,W)}get(P){return t(this,H).get(P)??null}getAll(){return(0,n.objectFromMap)(t(this,H))}has(P){return t(this,H).has(P)}}H=new WeakMap,W=new WeakMap,e.Metadata=d},(T,e,B)=>{var P,x,f,D,I,y,Ye;Object.defineProperty(e,"__esModule",{value:!0}),e.OptionalContentConfig=void 0;var n=B(1),d=B(8);const H=Symbol("INTERNAL");class W{constructor(p,u){rt(this,P,!0);this.name=p,this.intent=u}get visible(){return t(this,P)}_setVisible(p,u){p!==H&&(0,n.unreachable)("Internal method `_setVisible` called."),ot(this,P,u)}}P=new WeakMap;class ht{constructor(p){rt(this,y);rt(this,x,null);rt(this,f,new Map);rt(this,D,null);rt(this,I,null);if(this.name=null,this.creator=null,p!==null){this.name=p.name,this.creator=p.creator,ot(this,I,p.order);for(const u of p.groups)t(this,f).set(u.id,new W(u.name,u.intent));if(p.baseState==="OFF")for(const u of t(this,f).values())u._setVisible(H,!1);for(const u of p.on)t(this,f).get(u)._setVisible(H,!0);for(const u of p.off)t(this,f).get(u)._setVisible(H,!1);ot(this,D,this.getHash())}}isVisible(p){if(t(this,f).size===0)return!0;if(!p)return(0,n.warn)("Optional content group not defined."),!0;if(p.type==="OCG")return t(this,f).has(p.id)?t(this,f).get(p.id).visible:((0,n.warn)(`Optional content group not found: ${p.id}`),!0);if(p.type==="OCMD"){if(p.expression)return K(this,y,Ye).call(this,p.expression);if(!p.policy||p.policy==="AnyOn"){for(const u of p.ids){if(!t(this,f).has(u))return(0,n.warn)(`Optional content group not found: ${u}`),!0;if(t(this,f).get(u).visible)return!0}return!1}else if(p.policy==="AllOn"){for(const u of p.ids){if(!t(this,f).has(u))return(0,n.warn)(`Optional content group not found: ${u}`),!0;if(!t(this,f).get(u).visible)return!1}return!0}else if(p.policy==="AnyOff"){for(const u of p.ids){if(!t(this,f).has(u))return(0,n.warn)(`Optional content group not found: ${u}`),!0;if(!t(this,f).get(u).visible)return!0}return!1}else if(p.policy==="AllOff"){for(const u of p.ids){if(!t(this,f).has(u))return(0,n.warn)(`Optional content group not found: ${u}`),!0;if(t(this,f).get(u).visible)return!1}return!0}return(0,n.warn)(`Unknown optional content policy ${p.policy}.`),!0}return(0,n.warn)(`Unknown group type ${p.type}.`),!0}setVisibility(p,u=!0){if(!t(this,f).has(p)){(0,n.warn)(`Optional content group not found: ${p}`);return}t(this,f).get(p)._setVisible(H,!!u),ot(this,x,null)}get hasInitialVisibility(){return t(this,D)===null||this.getHash()===t(this,D)}getOrder(){return t(this,f).size?t(this,I)?t(this,I).slice():[...t(this,f).keys()]:null}getGroups(){return t(this,f).size>0?(0,n.objectFromMap)(t(this,f)):null}getGroup(p){return t(this,f).get(p)||null}getHash(){if(t(this,x)!==null)return t(this,x);const p=new d.MurmurHash3_64;for(const[u,b]of t(this,f))p.update(`${u}:${b.visible}`);return ot(this,x,p.hexdigest())}}x=new WeakMap,f=new WeakMap,D=new WeakMap,I=new WeakMap,y=new WeakSet,Ye=function(p){const u=p.length;if(u<2)return!0;const b=p[0];for(let C=1;C<u;C++){const _=p[C];let i;if(Array.isArray(_))i=K(this,y,Ye).call(this,_);else if(t(this,f).has(_))i=t(this,f).get(_).visible;else return(0,n.warn)(`Optional content group not found: ${_}`),!0;switch(b){case"And":if(!i)return!1;break;case"Or":if(i)return!0;break;case"Not":return!i;default:return!0}}return b==="And"},e.OptionalContentConfig=ht},(T,e,B)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.PDFDataTransportStream=void 0;var n=B(1),d=B(6);class H{constructor({length:x,initialData:f,progressiveDone:D=!1,contentDispositionFilename:I=null,disableRange:y=!1,disableStream:m=!1},E){if((0,n.assert)(E,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.'),this._queuedChunks=[],this._progressiveDone=D,this._contentDispositionFilename=I,(f==null?void 0:f.length)>0){const p=f instanceof Uint8Array&&f.byteLength===f.buffer.byteLength?f.buffer:new Uint8Array(f).buffer;this._queuedChunks.push(p)}this._pdfDataRangeTransport=E,this._isStreamingSupported=!m,this._isRangeSupported=!y,this._contentLength=x,this._fullRequestReader=null,this._rangeReaders=[],this._pdfDataRangeTransport.addRangeListener((p,u)=>{this._onReceiveData({begin:p,chunk:u})}),this._pdfDataRangeTransport.addProgressListener((p,u)=>{this._onProgress({loaded:p,total:u})}),this._pdfDataRangeTransport.addProgressiveReadListener(p=>{this._onReceiveData({chunk:p})}),this._pdfDataRangeTransport.addProgressiveDoneListener(()=>{this._onProgressiveDone()}),this._pdfDataRangeTransport.transportReady()}_onReceiveData({begin:x,chunk:f}){const D=f instanceof Uint8Array&&f.byteLength===f.buffer.byteLength?f.buffer:new Uint8Array(f).buffer;if(x===void 0)this._fullRequestReader?this._fullRequestReader._enqueue(D):this._queuedChunks.push(D);else{const I=this._rangeReaders.some(function(y){return y._begin!==x?!1:(y._enqueue(D),!0)});(0,n.assert)(I,"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){var x;return((x=this._fullRequestReader)==null?void 0:x._loaded)??0}_onProgress(x){var f,D,I,y;x.total===void 0?(D=(f=this._rangeReaders[0])==null?void 0:f.onProgress)==null||D.call(f,{loaded:x.loaded}):(y=(I=this._fullRequestReader)==null?void 0:I.onProgress)==null||y.call(I,{loaded:x.loaded,total:x.total})}_onProgressiveDone(){var x;(x=this._fullRequestReader)==null||x.progressiveDone(),this._progressiveDone=!0}_removeRangeReader(x){const f=this._rangeReaders.indexOf(x);f>=0&&this._rangeReaders.splice(f,1)}getFullReader(){(0,n.assert)(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const x=this._queuedChunks;return this._queuedChunks=null,new W(this,x,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(x,f){if(f<=this._progressiveDataLength)return null;const D=new ht(this,x,f);return this._pdfDataRangeTransport.requestDataRange(x,f),this._rangeReaders.push(D),D}cancelAllRequests(x){var f;(f=this._fullRequestReader)==null||f.cancel(x);for(const D of this._rangeReaders.slice(0))D.cancel(x);this._pdfDataRangeTransport.abort()}}e.PDFDataTransportStream=H;class W{constructor(x,f,D=!1,I=null){this._stream=x,this._done=D||!1,this._filename=(0,d.isPdfFile)(I)?I:null,this._queuedChunks=f||[],this._loaded=0;for(const y of this._queuedChunks)this._loaded+=y.byteLength;this._requests=[],this._headersReady=Promise.resolve(),x._fullRequestReader=this,this.onProgress=null}_enqueue(x){this._done||(this._requests.length>0?this._requests.shift().resolve({value:x,done:!1}):this._queuedChunks.push(x),this._loaded+=x.byteLength)}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0)return{value:this._queuedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};const x=new n.PromiseCapability;return this._requests.push(x),x.promise}cancel(x){this._done=!0;for(const f of this._requests)f.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class ht{constructor(x,f,D){this._stream=x,this._begin=f,this._end=D,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}_enqueue(x){if(!this._done){if(this._requests.length===0)this._queuedChunk=x;else{this._requests.shift().resolve({value:x,done:!1});for(const D of this._requests)D.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0,this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const f=this._queuedChunk;return this._queuedChunk=null,{value:f,done:!1}}if(this._done)return{value:void 0,done:!0};const x=new n.PromiseCapability;return this._requests.push(x),x.promise}cancel(x){this._done=!0;for(const f of this._requests)f.resolve({value:void 0,done:!0});this._requests.length=0,this._stream._removeRangeReader(this)}}},(T,e,B)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.PDFFetchStream=void 0;var n=B(1),d=B(20);function H(D,I,y){return{method:"GET",headers:D,signal:y.signal,mode:"cors",credentials:I?"include":"same-origin",redirect:"follow"}}function W(D){const I=new Headers;for(const y in D){const m=D[y];m!==void 0&&I.append(y,m)}return I}function ht(D){return D instanceof Uint8Array?D.buffer:D instanceof ArrayBuffer?D:((0,n.warn)(`getArrayBuffer - unexpected data format: ${D}`),new Uint8Array(D).buffer)}class P{constructor(I){this.source=I,this.isHttp=/^https?:/i.test(I.url),this.httpHeaders=this.isHttp&&I.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){var I;return((I=this._fullRequestReader)==null?void 0:I._loaded)??0}getFullReader(){return(0,n.assert)(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once."),this._fullRequestReader=new x(this),this._fullRequestReader}getRangeReader(I,y){if(y<=this._progressiveDataLength)return null;const m=new f(this,I,y);return this._rangeRequestReaders.push(m),m}cancelAllRequests(I){var y;(y=this._fullRequestReader)==null||y.cancel(I);for(const m of this._rangeRequestReaders.slice(0))m.cancel(I)}}e.PDFFetchStream=P;class x{constructor(I){this._stream=I,this._reader=null,this._loaded=0,this._filename=null;const y=I.source;this._withCredentials=y.withCredentials||!1,this._contentLength=y.length,this._headersCapability=new n.PromiseCapability,this._disableRange=y.disableRange||!1,this._rangeChunkSize=y.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._abortController=new AbortController,this._isStreamingSupported=!y.disableStream,this._isRangeSupported=!y.disableRange,this._headers=W(this._stream.httpHeaders);const m=y.url;fetch(m,H(this._headers,this._withCredentials,this._abortController)).then(E=>{if(!(0,d.validateResponseStatus)(E.status))throw(0,d.createResponseStatusError)(E.status,m);this._reader=E.body.getReader(),this._headersCapability.resolve();const p=C=>E.headers.get(C),{allowRangeRequests:u,suggestedLength:b}=(0,d.validateRangeRequestCapabilities)({getResponseHeader:p,isHttp:this._stream.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=u,this._contentLength=b||this._contentLength,this._filename=(0,d.extractFilenameFromHeader)(p),!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new n.AbortException("Streaming is disabled."))}).catch(this._headersCapability.reject),this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){var m;await this._headersCapability.promise;const{value:I,done:y}=await this._reader.read();return y?{value:I,done:y}:(this._loaded+=I.byteLength,(m=this.onProgress)==null||m.call(this,{loaded:this._loaded,total:this._contentLength}),{value:ht(I),done:!1})}cancel(I){var y;(y=this._reader)==null||y.cancel(I),this._abortController.abort()}}class f{constructor(I,y,m){this._stream=I,this._reader=null,this._loaded=0;const E=I.source;this._withCredentials=E.withCredentials||!1,this._readCapability=new n.PromiseCapability,this._isStreamingSupported=!E.disableStream,this._abortController=new AbortController,this._headers=W(this._stream.httpHeaders),this._headers.append("Range",`bytes=${y}-${m-1}`);const p=E.url;fetch(p,H(this._headers,this._withCredentials,this._abortController)).then(u=>{if(!(0,d.validateResponseStatus)(u.status))throw(0,d.createResponseStatusError)(u.status,p);this._readCapability.resolve(),this._reader=u.body.getReader()}).catch(this._readCapability.reject),this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){var m;await this._readCapability.promise;const{value:I,done:y}=await this._reader.read();return y?{value:I,done:y}:(this._loaded+=I.byteLength,(m=this.onProgress)==null||m.call(this,{loaded:this._loaded}),{value:ht(I),done:!1})}cancel(I){var y;(y=this._reader)==null||y.cancel(I),this._abortController.abort()}}},(T,e,B)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.createResponseStatusError=P,e.extractFilenameFromHeader=ht,e.validateRangeRequestCapabilities=W,e.validateResponseStatus=x;var n=B(1),d=B(21),H=B(6);function W({getResponseHeader:f,isHttp:D,rangeChunkSize:I,disableRange:y}){const m={allowRangeRequests:!1,suggestedLength:void 0},E=parseInt(f("Content-Length"),10);return!Number.isInteger(E)||(m.suggestedLength=E,E<=2*I)||y||!D||f("Accept-Ranges")!=="bytes"||(f("Content-Encoding")||"identity")!=="identity"||(m.allowRangeRequests=!0),m}function ht(f){const D=f("Content-Disposition");if(D){let I=(0,d.getFilenameFromContentDispositionHeader)(D);if(I.includes("%"))try{I=decodeURIComponent(I)}catch{}if((0,H.isPdfFile)(I))return I}return null}function P(f,D){return f===404||f===0&&D.startsWith("file:")?new n.MissingPDFException('Missing PDF "'+D+'".'):new n.UnexpectedResponseException(`Unexpected server response (${f}) while retrieving PDF "${D}".`,f)}function x(f){return f===200||f===206}},(T,e,B)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.getFilenameFromContentDispositionHeader=d;var n=B(1);function d(H){let W=!0,ht=P("filename\\*","i").exec(H);if(ht){ht=ht[1];let E=I(ht);return E=unescape(E),E=y(E),E=m(E),f(E)}if(ht=D(H),ht){const E=m(ht);return f(E)}if(ht=P("filename","i").exec(H),ht){ht=ht[1];let E=I(ht);return E=m(E),f(E)}function P(E,p){return new RegExp("(?:^|;)\\s*"+E+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',p)}function x(E,p){if(E){if(!/^[\x00-\xFF]+$/.test(p))return p;try{const u=new TextDecoder(E,{fatal:!0}),b=(0,n.stringToBytes)(p);p=u.decode(b),W=!1}catch{}}return p}function f(E){return W&&/[\x80-\xff]/.test(E)&&(E=x("utf-8",E),W&&(E=x("iso-8859-1",E))),E}function D(E){const p=[];let u;const b=P("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;(u=b.exec(E))!==null;){let[,_,i,c]=u;if(_=parseInt(_,10),_ in p){if(_===0)break;continue}p[_]=[i,c]}const C=[];for(let _=0;_<p.length&&_ in p;++_){let[i,c]=p[_];c=I(c),i&&(c=unescape(c),_===0&&(c=y(c))),C.push(c)}return C.join("")}function I(E){if(E.startsWith('"')){const p=E.slice(1).split('\\"');for(let u=0;u<p.length;++u){const b=p[u].indexOf('"');b!==-1&&(p[u]=p[u].slice(0,b),p.length=u+1),p[u]=p[u].replaceAll(/\\(.)/g,"$1")}E=p.join('"')}return E}function y(E){const p=E.indexOf("'");if(p===-1)return E;const u=E.slice(0,p),C=E.slice(p+1).replace(/^[^']*'/,"");return x(u,C)}function m(E){return!E.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(E)?E:E.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,function(p,u,b,C){if(b==="q"||b==="Q")return C=C.replaceAll("_"," "),C=C.replaceAll(/=([0-9a-fA-F]{2})/g,function(_,i){return String.fromCharCode(parseInt(i,16))}),x(u,C);try{C=atob(C)}catch{}return x(u,C)})}return""}},(T,e,B)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.PDFNetworkStream=void 0;var n=B(1),d=B(20);const H=200,W=206;function ht(I){const y=I.response;return typeof y!="string"?y:(0,n.stringToBytes)(y).buffer}class P{constructor(y,m={}){this.url=y,this.isHttp=/^https?:/i.test(y),this.httpHeaders=this.isHttp&&m.httpHeaders||Object.create(null),this.withCredentials=m.withCredentials||!1,this.currXhrId=0,this.pendingRequests=Object.create(null)}requestRange(y,m,E){const p={begin:y,end:m};for(const u in E)p[u]=E[u];return this.request(p)}requestFull(y){return this.request(y)}request(y){const m=new XMLHttpRequest,E=this.currXhrId++,p=this.pendingRequests[E]={xhr:m};m.open("GET",this.url),m.withCredentials=this.withCredentials;for(const u in this.httpHeaders){const b=this.httpHeaders[u];b!==void 0&&m.setRequestHeader(u,b)}return this.isHttp&&"begin"in y&&"end"in y?(m.setRequestHeader("Range",`bytes=${y.begin}-${y.end-1}`),p.expectedStatus=W):p.expectedStatus=H,m.responseType="arraybuffer",y.onError&&(m.onerror=function(u){y.onError(m.status)}),m.onreadystatechange=this.onStateChange.bind(this,E),m.onprogress=this.onProgress.bind(this,E),p.onHeadersReceived=y.onHeadersReceived,p.onDone=y.onDone,p.onError=y.onError,p.onProgress=y.onProgress,m.send(null),E}onProgress(y,m){var p;const E=this.pendingRequests[y];E&&((p=E.onProgress)==null||p.call(E,m))}onStateChange(y,m){var _,i,c;const E=this.pendingRequests[y];if(!E)return;const p=E.xhr;if(p.readyState>=2&&E.onHeadersReceived&&(E.onHeadersReceived(),delete E.onHeadersReceived),p.readyState!==4||!(y in this.pendingRequests))return;if(delete this.pendingRequests[y],p.status===0&&this.isHttp){(_=E.onError)==null||_.call(E,p.status);return}const u=p.status||H;if(!(u===H&&E.expectedStatus===W)&&u!==E.expectedStatus){(i=E.onError)==null||i.call(E,p.status);return}const C=ht(p);if(u===W){const o=p.getResponseHeader("Content-Range"),l=/bytes (\d+)-(\d+)\/(\d+)/.exec(o);E.onDone({begin:parseInt(l[1],10),chunk:C})}else C?E.onDone({begin:0,chunk:C}):(c=E.onError)==null||c.call(E,p.status)}getRequestXhr(y){return this.pendingRequests[y].xhr}isPendingRequest(y){return y in this.pendingRequests}abortRequest(y){const m=this.pendingRequests[y].xhr;delete this.pendingRequests[y],m.abort()}}class x{constructor(y){this._source=y,this._manager=new P(y.url,{httpHeaders:y.httpHeaders,withCredentials:y.withCredentials}),this._rangeChunkSize=y.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(y){const m=this._rangeRequestReaders.indexOf(y);m>=0&&this._rangeRequestReaders.splice(m,1)}getFullReader(){return(0,n.assert)(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once."),this._fullRequestReader=new f(this._manager,this._source),this._fullRequestReader}getRangeReader(y,m){const E=new D(this._manager,y,m);return E.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(E),E}cancelAllRequests(y){var m;(m=this._fullRequestReader)==null||m.cancel(y);for(const E of this._rangeRequestReaders.slice(0))E.cancel(y)}}e.PDFNetworkStream=x;class f{constructor(y,m){this._manager=y;const E={onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=m.url,this._fullRequestId=y.requestFull(E),this._headersReceivedCapability=new n.PromiseCapability,this._disableRange=m.disableRange||!1,this._contentLength=m.length,this._rangeChunkSize=m.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this._filename=null,this.onProgress=null}_onHeadersReceived(){const y=this._fullRequestId,m=this._manager.getRequestXhr(y),E=b=>m.getResponseHeader(b),{allowRangeRequests:p,suggestedLength:u}=(0,d.validateRangeRequestCapabilities)({getResponseHeader:E,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});p&&(this._isRangeSupported=!0),this._contentLength=u||this._contentLength,this._filename=(0,d.extractFilenameFromHeader)(E),this._isRangeSupported&&this._manager.abortRequest(y),this._headersReceivedCapability.resolve()}_onDone(y){if(y&&(this._requests.length>0?this._requests.shift().resolve({value:y.chunk,done:!1}):this._cachedChunks.push(y.chunk)),this._done=!0,!(this._cachedChunks.length>0)){for(const m of this._requests)m.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(y){this._storedError=(0,d.createResponseStatusError)(y,this._url),this._headersReceivedCapability.reject(this._storedError);for(const m of this._requests)m.reject(this._storedError);this._requests.length=0,this._cachedChunks.length=0}_onProgress(y){var m;(m=this.onProgress)==null||m.call(this,{loaded:y.loaded,total:y.lengthComputable?y.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersReceivedCapability.promise}async read(){if(this._storedError)throw this._storedError;if(this._cachedChunks.length>0)return{value:this._cachedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};const y=new n.PromiseCapability;return this._requests.push(y),y.promise}cancel(y){this._done=!0,this._headersReceivedCapability.reject(y);for(const m of this._requests)m.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}}class D{constructor(y,m,E){this._manager=y;const p={onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=y.url,this._requestId=y.requestRange(m,E,p),this._requests=[],this._queuedChunk=null,this._done=!1,this._storedError=void 0,this.onProgress=null,this.onClosed=null}_close(){var y;(y=this.onClosed)==null||y.call(this,this)}_onDone(y){const m=y.chunk;this._requests.length>0?this._requests.shift().resolve({value:m,done:!1}):this._queuedChunk=m,this._done=!0;for(const E of this._requests)E.resolve({value:void 0,done:!0});this._requests.length=0,this._close()}_onError(y){this._storedError=(0,d.createResponseStatusError)(y,this._url);for(const m of this._requests)m.reject(this._storedError);this._requests.length=0,this._queuedChunk=null}_onProgress(y){var m;this.isStreamingSupported||(m=this.onProgress)==null||m.call(this,{loaded:y.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(this._queuedChunk!==null){const m=this._queuedChunk;return this._queuedChunk=null,{value:m,done:!1}}if(this._done)return{value:void 0,done:!0};const y=new n.PromiseCapability;return this._requests.push(y),y.promise}cancel(y){this._done=!0;for(const m of this._requests)m.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}}},(T,e,B)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.PDFNodeStream=void 0;var n=B(1),d=B(20);const H=/^file:\/\/\/[a-zA-Z]:\//;function W(E){const p=require$$5,u=p.parse(E);return u.protocol==="file:"||u.host?u:/^[a-z]:[/\\]/i.test(E)?p.parse(`file:///${E}`):(u.host||(u.protocol="file:"),u)}class ht{constructor(p){this.source=p,this.url=W(p.url),this.isHttp=this.url.protocol==="http:"||this.url.protocol==="https:",this.isFsUrl=this.url.protocol==="file:",this.httpHeaders=this.isHttp&&p.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){var p;return((p=this._fullRequestReader)==null?void 0:p._loaded)??0}getFullReader(){return(0,n.assert)(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once."),this._fullRequestReader=this.isFsUrl?new y(this):new D(this),this._fullRequestReader}getRangeReader(p,u){if(u<=this._progressiveDataLength)return null;const b=this.isFsUrl?new m(this,p,u):new I(this,p,u);return this._rangeRequestReaders.push(b),b}cancelAllRequests(p){var u;(u=this._fullRequestReader)==null||u.cancel(p);for(const b of this._rangeRequestReaders.slice(0))b.cancel(p)}}e.PDFNodeStream=ht;class P{constructor(p){this._url=p.url,this._done=!1,this._storedError=null,this.onProgress=null;const u=p.source;this._contentLength=u.length,this._loaded=0,this._filename=null,this._disableRange=u.disableRange||!1,this._rangeChunkSize=u.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._isStreamingSupported=!u.disableStream,this._isRangeSupported=!u.disableRange,this._readableStream=null,this._readCapability=new n.PromiseCapability,this._headersCapability=new n.PromiseCapability}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){var b;if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const p=this._readableStream.read();return p===null?(this._readCapability=new n.PromiseCapability,this.read()):(this._loaded+=p.length,(b=this.onProgress)==null||b.call(this,{loaded:this._loaded,total:this._contentLength}),{value:new Uint8Array(p).buffer,done:!1})}cancel(p){if(!this._readableStream){this._error(p);return}this._readableStream.destroy(p)}_error(p){this._storedError=p,this._readCapability.resolve()}_setReadableStream(p){this._readableStream=p,p.on("readable",()=>{this._readCapability.resolve()}),p.on("end",()=>{p.destroy(),this._done=!0,this._readCapability.resolve()}),p.on("error",u=>{this._error(u)}),!this._isStreamingSupported&&this._isRangeSupported&&this._error(new n.AbortException("streaming is disabled")),this._storedError&&this._readableStream.destroy(this._storedError)}}class x{constructor(p){this._url=p.url,this._done=!1,this._storedError=null,this.onProgress=null,this._loaded=0,this._readableStream=null,this._readCapability=new n.PromiseCapability;const u=p.source;this._isStreamingSupported=!u.disableStream}get isStreamingSupported(){return this._isStreamingSupported}async read(){var b;if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const p=this._readableStream.read();return p===null?(this._readCapability=new n.PromiseCapability,this.read()):(this._loaded+=p.length,(b=this.onProgress)==null||b.call(this,{loaded:this._loaded}),{value:new Uint8Array(p).buffer,done:!1})}cancel(p){if(!this._readableStream){this._error(p);return}this._readableStream.destroy(p)}_error(p){this._storedError=p,this._readCapability.resolve()}_setReadableStream(p){this._readableStream=p,p.on("readable",()=>{this._readCapability.resolve()}),p.on("end",()=>{p.destroy(),this._done=!0,this._readCapability.resolve()}),p.on("error",u=>{this._error(u)}),this._storedError&&this._readableStream.destroy(this._storedError)}}function f(E,p){return{protocol:E.protocol,auth:E.auth,host:E.hostname,port:E.port,path:E.path,method:"GET",headers:p}}class D extends P{constructor(p){super(p);const u=b=>{if(b.statusCode===404){const c=new n.MissingPDFException(`Missing PDF "${this._url}".`);this._storedError=c,this._headersCapability.reject(c);return}this._headersCapability.resolve(),this._setReadableStream(b);const C=c=>this._readableStream.headers[c.toLowerCase()],{allowRangeRequests:_,suggestedLength:i}=(0,d.validateRangeRequestCapabilities)({getResponseHeader:C,isHttp:p.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=_,this._contentLength=i||this._contentLength,this._filename=(0,d.extractFilenameFromHeader)(C)};if(this._request=null,this._url.protocol==="http:"){const b=require$$5;this._request=b.request(f(this._url,p.httpHeaders),u)}else{const b=require$$5;this._request=b.request(f(this._url,p.httpHeaders),u)}this._request.on("error",b=>{this._storedError=b,this._headersCapability.reject(b)}),this._request.end()}}class I extends x{constructor(p,u,b){super(p),this._httpHeaders={};for(const _ in p.httpHeaders){const i=p.httpHeaders[_];i!==void 0&&(this._httpHeaders[_]=i)}this._httpHeaders.Range=`bytes=${u}-${b-1}`;const C=_=>{if(_.statusCode===404){const i=new n.MissingPDFException(`Missing PDF "${this._url}".`);this._storedError=i;return}this._setReadableStream(_)};if(this._request=null,this._url.protocol==="http:"){const _=require$$5;this._request=_.request(f(this._url,this._httpHeaders),C)}else{const _=require$$5;this._request=_.request(f(this._url,this._httpHeaders),C)}this._request.on("error",_=>{this._storedError=_}),this._request.end()}}class y extends P{constructor(p){super(p);let u=decodeURIComponent(this._url.path);H.test(this._url.href)&&(u=u.replace(/^\//,""));const b=require$$5;b.lstat(u,(C,_)=>{if(C){C.code==="ENOENT"&&(C=new n.MissingPDFException(`Missing PDF "${u}".`)),this._storedError=C,this._headersCapability.reject(C);return}this._contentLength=_.size,this._setReadableStream(b.createReadStream(u)),this._headersCapability.resolve()})}}class m extends x{constructor(p,u,b){super(p);let C=decodeURIComponent(this._url.path);H.test(this._url.href)&&(C=C.replace(/^\//,""));const _=require$$5;this._setReadableStream(_.createReadStream(C,{start:u,end:b-1}))}}},(T,e,B)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.SVGGraphics=void 0;var n=B(6),d=B(1);const H={fontStyle:"normal",fontWeight:"normal",fillColor:"#000000"},W="http://www.w3.org/XML/1998/namespace",ht="http://www.w3.org/1999/xlink",P=["butt","round","square"],x=["miter","round","bevel"],f=function(_,i="",c=!1){if(URL.createObjectURL&&typeof Blob<"u"&&!c)return URL.createObjectURL(new Blob([_],{type:i}));const o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";let l=`data:${i};base64,`;for(let h=0,F=_.length;h<F;h+=3){const r=_[h]&255,v=_[h+1]&255,k=_[h+2]&255,Z=r>>2,$=(r&3)<<4|v>>4,q=h+1<F?(v&15)<<2|k>>6:64,G=h+2<F?k&63:64;l+=o[Z]+o[$]+o[q]+o[G]}return l},D=function(){const _=new Uint8Array([137,80,78,71,13,10,26,10]),i=12,c=new Int32Array(256);for(let k=0;k<256;k++){let Z=k;for(let $=0;$<8;$++)Z=Z&1?3988292384^Z>>1&2147483647:Z>>1&2147483647;c[k]=Z}function o(k,Z,$){let q=-1;for(let G=Z;G<$;G++){const it=(q^k[G])&255,R=c[it];q=q>>>8^R}return q^-1}function l(k,Z,$,q){let G=q;const it=Z.length;$[G]=it>>24&255,$[G+1]=it>>16&255,$[G+2]=it>>8&255,$[G+3]=it&255,G+=4,$[G]=k.charCodeAt(0)&255,$[G+1]=k.charCodeAt(1)&255,$[G+2]=k.charCodeAt(2)&255,$[G+3]=k.charCodeAt(3)&255,G+=4,$.set(Z,G),G+=Z.length;const R=o($,q+4,G);$[G]=R>>24&255,$[G+1]=R>>16&255,$[G+2]=R>>8&255,$[G+3]=R&255}function h(k,Z,$){let q=1,G=0;for(let it=Z;it<$;++it)q=(q+(k[it]&255))%65521,G=(G+q)%65521;return G<<16|q}function F(k){if(!d.isNodeJS)return r(k);try{const Z=parseInt(process.versions.node)>=8?k:Buffer.from(k),$=require$$5.deflateSync(Z,{level:9});return $ instanceof Uint8Array?$:new Uint8Array($)}catch(Z){(0,d.warn)("Not compressing PNG because zlib.deflateSync is unavailable: "+Z)}return r(k)}function r(k){let Z=k.length;const $=65535,q=Math.ceil(Z/$),G=new Uint8Array(2+Z+q*5+4);let it=0;G[it++]=120,G[it++]=156;let R=0;for(;Z>$;)G[it++]=0,G[it++]=255,G[it++]=255,G[it++]=0,G[it++]=0,G.set(k.subarray(R,R+$),it),it+=$,R+=$,Z-=$;G[it++]=1,G[it++]=Z&255,G[it++]=Z>>8&255,G[it++]=~Z&65535&255,G[it++]=(~Z&65535)>>8&255,G.set(k.subarray(R),it),it+=k.length-R;const V=h(k,0,k.length);return G[it++]=V>>24&255,G[it++]=V>>16&255,G[it++]=V>>8&255,G[it++]=V&255,G}function v(k,Z,$,q){const G=k.width,it=k.height;let R,V,et;const S=k.data;switch(Z){case d.ImageKind.GRAYSCALE_1BPP:V=0,R=1,et=G+7>>3;break;case d.ImageKind.RGB_24BPP:V=2,R=8,et=G*3;break;case d.ImageKind.RGBA_32BPP:V=6,R=8,et=G*4;break;default:throw new Error("invalid format")}const s=new Uint8Array((1+et)*it);let a=0,g=0;for(let ct=0;ct<it;++ct)s[a++]=0,s.set(S.subarray(g,g+et),a),g+=et,a+=et;if(Z===d.ImageKind.GRAYSCALE_1BPP&&q){a=0;for(let ct=0;ct<it;ct++){a++;for(let ft=0;ft<et;ft++)s[a++]^=255}}const L=new Uint8Array([G>>24&255,G>>16&255,G>>8&255,G&255,it>>24&255,it>>16&255,it>>8&255,it&255,R,V,0,0,0]),O=F(s),N=_.length+i*3+L.length+O.length,X=new Uint8Array(N);let nt=0;return X.set(_,nt),nt+=_.length,l("IHDR",L,X,nt),nt+=i+L.length,l("IDATA",O,X,nt),nt+=i+O.length,l("IEND",new Uint8Array(0),X,nt),f(X,"image/png",$)}return function(Z,$,q){const G=Z.kind===void 0?d.ImageKind.GRAYSCALE_1BPP:Z.kind;return v(Z,G,$,q)}}();class I{constructor(){this.fontSizeScale=1,this.fontWeight=H.fontWeight,this.fontSize=0,this.textMatrix=d.IDENTITY_MATRIX,this.fontMatrix=d.FONT_IDENTITY_MATRIX,this.leading=0,this.textRenderingMode=d.TextRenderingMode.FILL,this.textMatrixScale=1,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRise=0,this.fillColor=H.fillColor,this.strokeColor="#000000",this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.lineJoin="",this.lineCap="",this.miterLimit=0,this.dashArray=[],this.dashPhase=0,this.dependencies=[],this.activeClipUrl=null,this.clipGroup=null,this.maskId=""}clone(){return Object.create(this)}setCurrentPoint(i,c){this.x=i,this.y=c}}function y(_){let i=[];const c=[];for(const o of _){if(o.fn==="save"){i.push({fnId:92,fn:"group",items:[]}),c.push(i),i=i.at(-1).items;continue}o.fn==="restore"?i=c.pop():i.push(o)}return i}function m(_){if(Number.isInteger(_))return _.toString();const i=_.toFixed(10);let c=i.length-1;if(i[c]!=="0")return i;do c--;while(i[c]==="0");return i.substring(0,i[c]==="."?c:c+1)}function E(_){if(_[4]===0&&_[5]===0){if(_[1]===0&&_[2]===0)return _[0]===1&&_[3]===1?"":`scale(${m(_[0])} ${m(_[3])})`;if(_[0]===_[3]&&_[1]===-_[2]){const i=Math.acos(_[0])*180/Math.PI;return`rotate(${m(i)})`}}else if(_[0]===1&&_[1]===0&&_[2]===0&&_[3]===1)return`translate(${m(_[4])} ${m(_[5])})`;return`matrix(${m(_[0])} ${m(_[1])} ${m(_[2])} ${m(_[3])} ${m(_[4])} ${m(_[5])})`}let p=0,u=0,b=0;class C{constructor(i,c,o=!1){(0,n.deprecated)("The SVG back-end is no longer maintained and *may* be removed in the future."),this.svgFactory=new n.DOMSVGFactory,this.current=new I,this.transformMatrix=d.IDENTITY_MATRIX,this.transformStack=[],this.extraStack=[],this.commonObjs=i,this.objs=c,this.pendingClip=null,this.pendingEOFill=!1,this.embedFonts=!1,this.embeddedFonts=Object.create(null),this.cssStyle=null,this.forceDataSchema=!!o,this._operatorIdMapping=[];for(const l in d.OPS)this._operatorIdMapping[d.OPS[l]]=l}getObject(i,c=null){return typeof i=="string"?i.startsWith("g_")?this.commonObjs.get(i):this.objs.get(i):c}save(){this.transformStack.push(this.transformMatrix);const i=this.current;this.extraStack.push(i),this.current=i.clone()}restore(){this.transformMatrix=this.transformStack.pop(),this.current=this.extraStack.pop(),this.pendingClip=null,this.tgrp=null}group(i){this.save(),this.executeOpTree(i),this.restore()}loadDependencies(i){const c=i.fnArray,o=i.argsArray;for(let l=0,h=c.length;l<h;l++)if(c[l]===d.OPS.dependency)for(const F of o[l]){const r=F.startsWith("g_")?this.commonObjs:this.objs,v=new Promise(k=>{r.get(F,k)});this.current.dependencies.push(v)}return Promise.all(this.current.dependencies)}transform(i,c,o,l,h,F){const r=[i,c,o,l,h,F];this.transformMatrix=d.Util.transform(this.transformMatrix,r),this.tgrp=null}getSVG(i,c){this.viewport=c;const o=this._initialize(c);return this.loadDependencies(i).then(()=>(this.transformMatrix=d.IDENTITY_MATRIX,this.executeOpTree(this.convertOpList(i)),o))}convertOpList(i){const c=this._operatorIdMapping,o=i.argsArray,l=i.fnArray,h=[];for(let F=0,r=l.length;F<r;F++){const v=l[F];h.push({fnId:v,fn:c[v],args:o[F]})}return y(h)}executeOpTree(i){for(const c of i){const o=c.fn,l=c.fnId,h=c.args;switch(l|0){case d.OPS.beginText:this.beginText();break;case d.OPS.dependency:break;case d.OPS.setLeading:this.setLeading(h);break;case d.OPS.setLeadingMoveText:this.setLeadingMoveText(h[0],h[1]);break;case d.OPS.setFont:this.setFont(h);break;case d.OPS.showText:this.showText(h[0]);break;case d.OPS.showSpacedText:this.showText(h[0]);break;case d.OPS.endText:this.endText();break;case d.OPS.moveText:this.moveText(h[0],h[1]);break;case d.OPS.setCharSpacing:this.setCharSpacing(h[0]);break;case d.OPS.setWordSpacing:this.setWordSpacing(h[0]);break;case d.OPS.setHScale:this.setHScale(h[0]);break;case d.OPS.setTextMatrix:this.setTextMatrix(h[0],h[1],h[2],h[3],h[4],h[5]);break;case d.OPS.setTextRise:this.setTextRise(h[0]);break;case d.OPS.setTextRenderingMode:this.setTextRenderingMode(h[0]);break;case d.OPS.setLineWidth:this.setLineWidth(h[0]);break;case d.OPS.setLineJoin:this.setLineJoin(h[0]);break;case d.OPS.setLineCap:this.setLineCap(h[0]);break;case d.OPS.setMiterLimit:this.setMiterLimit(h[0]);break;case d.OPS.setFillRGBColor:this.setFillRGBColor(h[0],h[1],h[2]);break;case d.OPS.setStrokeRGBColor:this.setStrokeRGBColor(h[0],h[1],h[2]);break;case d.OPS.setStrokeColorN:this.setStrokeColorN(h);break;case d.OPS.setFillColorN:this.setFillColorN(h);break;case d.OPS.shadingFill:this.shadingFill(h[0]);break;case d.OPS.setDash:this.setDash(h[0],h[1]);break;case d.OPS.setRenderingIntent:this.setRenderingIntent(h[0]);break;case d.OPS.setFlatness:this.setFlatness(h[0]);break;case d.OPS.setGState:this.setGState(h[0]);break;case d.OPS.fill:this.fill();break;case d.OPS.eoFill:this.eoFill();break;case d.OPS.stroke:this.stroke();break;case d.OPS.fillStroke:this.fillStroke();break;case d.OPS.eoFillStroke:this.eoFillStroke();break;case d.OPS.clip:this.clip("nonzero");break;case d.OPS.eoClip:this.clip("evenodd");break;case d.OPS.paintSolidColorImageMask:this.paintSolidColorImageMask();break;case d.OPS.paintImageXObject:this.paintImageXObject(h[0]);break;case d.OPS.paintInlineImageXObject:this.paintInlineImageXObject(h[0]);break;case d.OPS.paintImageMaskXObject:this.paintImageMaskXObject(h[0]);break;case d.OPS.paintFormXObjectBegin:this.paintFormXObjectBegin(h[0],h[1]);break;case d.OPS.paintFormXObjectEnd:this.paintFormXObjectEnd();break;case d.OPS.closePath:this.closePath();break;case d.OPS.closeStroke:this.closeStroke();break;case d.OPS.closeFillStroke:this.closeFillStroke();break;case d.OPS.closeEOFillStroke:this.closeEOFillStroke();break;case d.OPS.nextLine:this.nextLine();break;case d.OPS.transform:this.transform(h[0],h[1],h[2],h[3],h[4],h[5]);break;case d.OPS.constructPath:this.constructPath(h[0],h[1]);break;case d.OPS.endPath:this.endPath();break;case 92:this.group(c.items);break;default:(0,d.warn)(`Unimplemented operator ${o}`);break}}}setWordSpacing(i){this.current.wordSpacing=i}setCharSpacing(i){this.current.charSpacing=i}nextLine(){this.moveText(0,this.current.leading)}setTextMatrix(i,c,o,l,h,F){const r=this.current;r.textMatrix=r.lineMatrix=[i,c,o,l,h,F],r.textMatrixScale=Math.hypot(i,c),r.x=r.lineX=0,r.y=r.lineY=0,r.xcoords=[],r.ycoords=[],r.tspan=this.svgFactory.createElement("svg:tspan"),r.tspan.setAttributeNS(null,"font-family",r.fontFamily),r.tspan.setAttributeNS(null,"font-size",`${m(r.fontSize)}px`),r.tspan.setAttributeNS(null,"y",m(-r.y)),r.txtElement=this.svgFactory.createElement("svg:text"),r.txtElement.append(r.tspan)}beginText(){const i=this.current;i.x=i.lineX=0,i.y=i.lineY=0,i.textMatrix=d.IDENTITY_MATRIX,i.lineMatrix=d.IDENTITY_MATRIX,i.textMatrixScale=1,i.tspan=this.svgFactory.createElement("svg:tspan"),i.txtElement=this.svgFactory.createElement("svg:text"),i.txtgrp=this.svgFactory.createElement("svg:g"),i.xcoords=[],i.ycoords=[]}moveText(i,c){const o=this.current;o.x=o.lineX+=i,o.y=o.lineY+=c,o.xcoords=[],o.ycoords=[],o.tspan=this.svgFactory.createElement("svg:tspan"),o.tspan.setAttributeNS(null,"font-family",o.fontFamily),o.tspan.setAttributeNS(null,"font-size",`${m(o.fontSize)}px`),o.tspan.setAttributeNS(null,"y",m(-o.y))}showText(i){const c=this.current,o=c.font,l=c.fontSize;if(l===0)return;const h=c.fontSizeScale,F=c.charSpacing,r=c.wordSpacing,v=c.fontDirection,k=c.textHScale*v,Z=o.vertical,$=Z?1:-1,q=o.defaultVMetrics,G=l*c.fontMatrix[0];let it=0;for(const et of i){if(et===null){it+=v*r;continue}else if(typeof et=="number"){it+=$*et*l/1e3;continue}const S=(et.isSpace?r:0)+F,s=et.fontChar;let a,g,L=et.width;if(Z){let N;const X=et.vmetric||q;N=et.vmetric?X[1]:L*.5,N=-N*G;const nt=X[2]*G;L=X?-X[0]:L,a=N/h,g=(it+nt)/h}else a=it/h,g=0;(et.isInFont||o.missingFile)&&(c.xcoords.push(c.x+a),Z&&c.ycoords.push(-c.y+g),c.tspan.textContent+=s);const O=Z?L*G-S*v:L*G+S*v;it+=O}c.tspan.setAttributeNS(null,"x",c.xcoords.map(m).join(" ")),Z?c.tspan.setAttributeNS(null,"y",c.ycoords.map(m).join(" ")):c.tspan.setAttributeNS(null,"y",m(-c.y)),Z?c.y-=it:c.x+=it*k,c.tspan.setAttributeNS(null,"font-family",c.fontFamily),c.tspan.setAttributeNS(null,"font-size",`${m(c.fontSize)}px`),c.fontStyle!==H.fontStyle&&c.tspan.setAttributeNS(null,"font-style",c.fontStyle),c.fontWeight!==H.fontWeight&&c.tspan.setAttributeNS(null,"font-weight",c.fontWeight);const R=c.textRenderingMode&d.TextRenderingMode.FILL_STROKE_MASK;if(R===d.TextRenderingMode.FILL||R===d.TextRenderingMode.FILL_STROKE?(c.fillColor!==H.fillColor&&c.tspan.setAttributeNS(null,"fill",c.fillColor),c.fillAlpha<1&&c.tspan.setAttributeNS(null,"fill-opacity",c.fillAlpha)):c.textRenderingMode===d.TextRenderingMode.ADD_TO_PATH?c.tspan.setAttributeNS(null,"fill","transparent"):c.tspan.setAttributeNS(null,"fill","none"),R===d.TextRenderingMode.STROKE||R===d.TextRenderingMode.FILL_STROKE){const et=1/(c.textMatrixScale||1);this._setStrokeAttributes(c.tspan,et)}let V=c.textMatrix;c.textRise!==0&&(V=V.slice(),V[5]+=c.textRise),c.txtElement.setAttributeNS(null,"transform",`${E(V)} scale(${m(k)}, -1)`),c.txtElement.setAttributeNS(W,"xml:space","preserve"),c.txtElement.append(c.tspan),c.txtgrp.append(c.txtElement),this._ensureTransformGroup().append(c.txtElement)}setLeadingMoveText(i,c){this.setLeading(-c),this.moveText(i,c)}addFontStyle(i){if(!i.data)throw new Error('addFontStyle: No font data available, ensure that the "fontExtraProperties" API parameter is set.');this.cssStyle||(this.cssStyle=this.svgFactory.createElement("svg:style"),this.cssStyle.setAttributeNS(null,"type","text/css"),this.defs.append(this.cssStyle));const c=f(i.data,i.mimetype,this.forceDataSchema);this.cssStyle.textContent+=`@font-face { font-family: "${i.loadedName}"; src: url(${c}); }
`}setFont(i){const c=this.current,o=this.commonObjs.get(i[0]);let l=i[1];c.font=o,this.embedFonts&&!o.missingFile&&!this.embeddedFonts[o.loadedName]&&(this.addFontStyle(o),this.embeddedFonts[o.loadedName]=o),c.fontMatrix=o.fontMatrix||d.FONT_IDENTITY_MATRIX;let h="normal";o.black?h="900":o.bold&&(h="bold");const F=o.italic?"italic":"normal";l<0?(l=-l,c.fontDirection=-1):c.fontDirection=1,c.fontSize=l,c.fontFamily=o.loadedName,c.fontWeight=h,c.fontStyle=F,c.tspan=this.svgFactory.createElement("svg:tspan"),c.tspan.setAttributeNS(null,"y",m(-c.y)),c.xcoords=[],c.ycoords=[]}endText(){var c;const i=this.current;i.textRenderingMode&d.TextRenderingMode.ADD_TO_PATH_FLAG&&((c=i.txtElement)!=null&&c.hasChildNodes())&&(i.element=i.txtElement,this.clip("nonzero"),this.endPath())}setLineWidth(i){i>0&&(this.current.lineWidth=i)}setLineCap(i){this.current.lineCap=P[i]}setLineJoin(i){this.current.lineJoin=x[i]}setMiterLimit(i){this.current.miterLimit=i}setStrokeAlpha(i){this.current.strokeAlpha=i}setStrokeRGBColor(i,c,o){this.current.strokeColor=d.Util.makeHexColor(i,c,o)}setFillAlpha(i){this.current.fillAlpha=i}setFillRGBColor(i,c,o){this.current.fillColor=d.Util.makeHexColor(i,c,o),this.current.tspan=this.svgFactory.createElement("svg:tspan"),this.current.xcoords=[],this.current.ycoords=[]}setStrokeColorN(i){this.current.strokeColor=this._makeColorN_Pattern(i)}setFillColorN(i){this.current.fillColor=this._makeColorN_Pattern(i)}shadingFill(i){const{width:c,height:o}=this.viewport,l=d.Util.inverseTransform(this.transformMatrix),[h,F,r,v]=d.Util.getAxialAlignedBoundingBox([0,0,c,o],l),k=this.svgFactory.createElement("svg:rect");k.setAttributeNS(null,"x",h),k.setAttributeNS(null,"y",F),k.setAttributeNS(null,"width",r-h),k.setAttributeNS(null,"height",v-F),k.setAttributeNS(null,"fill",this._makeShadingPattern(i)),this.current.fillAlpha<1&&k.setAttributeNS(null,"fill-opacity",this.current.fillAlpha),this._ensureTransformGroup().append(k)}_makeColorN_Pattern(i){return i[0]==="TilingPattern"?this._makeTilingPattern(i):this._makeShadingPattern(i)}_makeTilingPattern(i){const c=i[1],o=i[2],l=i[3]||d.IDENTITY_MATRIX,[h,F,r,v]=i[4],k=i[5],Z=i[6],$=i[7],q=`shading${b++}`,[G,it,R,V]=d.Util.normalizeRect([...d.Util.applyTransform([h,F],l),...d.Util.applyTransform([r,v],l)]),[et,S]=d.Util.singularValueDecompose2dScale(l),s=k*et,a=Z*S,g=this.svgFactory.createElement("svg:pattern");g.setAttributeNS(null,"id",q),g.setAttributeNS(null,"patternUnits","userSpaceOnUse"),g.setAttributeNS(null,"width",s),g.setAttributeNS(null,"height",a),g.setAttributeNS(null,"x",`${G}`),g.setAttributeNS(null,"y",`${it}`);const L=this.svg,O=this.transformMatrix,N=this.current.fillColor,X=this.current.strokeColor,nt=this.svgFactory.create(R-G,V-it);if(this.svg=nt,this.transformMatrix=l,$===2){const ct=d.Util.makeHexColor(...c);this.current.fillColor=ct,this.current.strokeColor=ct}return this.executeOpTree(this.convertOpList(o)),this.svg=L,this.transformMatrix=O,this.current.fillColor=N,this.current.strokeColor=X,g.append(nt.childNodes[0]),this.defs.append(g),`url(#${q})`}_makeShadingPattern(i){switch(typeof i=="string"&&(i=this.objs.get(i)),i[0]){case"RadialAxial":const c=`shading${b++}`,o=i[3];let l;switch(i[1]){case"axial":const h=i[4],F=i[5];l=this.svgFactory.createElement("svg:linearGradient"),l.setAttributeNS(null,"id",c),l.setAttributeNS(null,"gradientUnits","userSpaceOnUse"),l.setAttributeNS(null,"x1",h[0]),l.setAttributeNS(null,"y1",h[1]),l.setAttributeNS(null,"x2",F[0]),l.setAttributeNS(null,"y2",F[1]);break;case"radial":const r=i[4],v=i[5],k=i[6],Z=i[7];l=this.svgFactory.createElement("svg:radialGradient"),l.setAttributeNS(null,"id",c),l.setAttributeNS(null,"gradientUnits","userSpaceOnUse"),l.setAttributeNS(null,"cx",v[0]),l.setAttributeNS(null,"cy",v[1]),l.setAttributeNS(null,"r",Z),l.setAttributeNS(null,"fx",r[0]),l.setAttributeNS(null,"fy",r[1]),l.setAttributeNS(null,"fr",k);break;default:throw new Error(`Unknown RadialAxial type: ${i[1]}`)}for(const h of o){const F=this.svgFactory.createElement("svg:stop");F.setAttributeNS(null,"offset",h[0]),F.setAttributeNS(null,"stop-color",h[1]),l.append(F)}return this.defs.append(l),`url(#${c})`;case"Mesh":return(0,d.warn)("Unimplemented pattern Mesh"),null;case"Dummy":return"hotpink";default:throw new Error(`Unknown IR type: ${i[0]}`)}}setDash(i,c){this.current.dashArray=i,this.current.dashPhase=c}constructPath(i,c){const o=this.current;let l=o.x,h=o.y,F=[],r=0;for(const v of i)switch(v|0){case d.OPS.rectangle:l=c[r++],h=c[r++];const k=c[r++],Z=c[r++],$=l+k,q=h+Z;F.push("M",m(l),m(h),"L",m($),m(h),"L",m($),m(q),"L",m(l),m(q),"Z");break;case d.OPS.moveTo:l=c[r++],h=c[r++],F.push("M",m(l),m(h));break;case d.OPS.lineTo:l=c[r++],h=c[r++],F.push("L",m(l),m(h));break;case d.OPS.curveTo:l=c[r+4],h=c[r+5],F.push("C",m(c[r]),m(c[r+1]),m(c[r+2]),m(c[r+3]),m(l),m(h)),r+=6;break;case d.OPS.curveTo2:F.push("C",m(l),m(h),m(c[r]),m(c[r+1]),m(c[r+2]),m(c[r+3])),l=c[r+2],h=c[r+3],r+=4;break;case d.OPS.curveTo3:l=c[r+2],h=c[r+3],F.push("C",m(c[r]),m(c[r+1]),m(l),m(h),m(l),m(h)),r+=4;break;case d.OPS.closePath:F.push("Z");break}F=F.join(" "),o.path&&i.length>0&&i[0]!==d.OPS.rectangle&&i[0]!==d.OPS.moveTo?F=o.path.getAttributeNS(null,"d")+F:(o.path=this.svgFactory.createElement("svg:path"),this._ensureTransformGroup().append(o.path)),o.path.setAttributeNS(null,"d",F),o.path.setAttributeNS(null,"fill","none"),o.element=o.path,o.setCurrentPoint(l,h)}endPath(){const i=this.current;if(i.path=null,!this.pendingClip)return;if(!i.element){this.pendingClip=null;return}const c=`clippath${p++}`,o=this.svgFactory.createElement("svg:clipPath");o.setAttributeNS(null,"id",c),o.setAttributeNS(null,"transform",E(this.transformMatrix));const l=i.element.cloneNode(!0);if(this.pendingClip==="evenodd"?l.setAttributeNS(null,"clip-rule","evenodd"):l.setAttributeNS(null,"clip-rule","nonzero"),this.pendingClip=null,o.append(l),this.defs.append(o),i.activeClipUrl){i.clipGroup=null;for(const h of this.extraStack)h.clipGroup=null;o.setAttributeNS(null,"clip-path",i.activeClipUrl)}i.activeClipUrl=`url(#${c})`,this.tgrp=null}clip(i){this.pendingClip=i}closePath(){const i=this.current;if(i.path){const c=`${i.path.getAttributeNS(null,"d")}Z`;i.path.setAttributeNS(null,"d",c)}}setLeading(i){this.current.leading=-i}setTextRise(i){this.current.textRise=i}setTextRenderingMode(i){this.current.textRenderingMode=i}setHScale(i){this.current.textHScale=i/100}setRenderingIntent(i){}setFlatness(i){}setGState(i){for(const[c,o]of i)switch(c){case"LW":this.setLineWidth(o);break;case"LC":this.setLineCap(o);break;case"LJ":this.setLineJoin(o);break;case"ML":this.setMiterLimit(o);break;case"D":this.setDash(o[0],o[1]);break;case"RI":this.setRenderingIntent(o);break;case"FL":this.setFlatness(o);break;case"Font":this.setFont(o);break;case"CA":this.setStrokeAlpha(o);break;case"ca":this.setFillAlpha(o);break;default:(0,d.warn)(`Unimplemented graphic state operator ${c}`);break}}fill(){const i=this.current;i.element&&(i.element.setAttributeNS(null,"fill",i.fillColor),i.element.setAttributeNS(null,"fill-opacity",i.fillAlpha),this.endPath())}stroke(){const i=this.current;i.element&&(this._setStrokeAttributes(i.element),i.element.setAttributeNS(null,"fill","none"),this.endPath())}_setStrokeAttributes(i,c=1){const o=this.current;let l=o.dashArray;c!==1&&l.length>0&&(l=l.map(function(h){return c*h})),i.setAttributeNS(null,"stroke",o.strokeColor),i.setAttributeNS(null,"stroke-opacity",o.strokeAlpha),i.setAttributeNS(null,"stroke-miterlimit",m(o.miterLimit)),i.setAttributeNS(null,"stroke-linecap",o.lineCap),i.setAttributeNS(null,"stroke-linejoin",o.lineJoin),i.setAttributeNS(null,"stroke-width",m(c*o.lineWidth)+"px"),i.setAttributeNS(null,"stroke-dasharray",l.map(m).join(" ")),i.setAttributeNS(null,"stroke-dashoffset",m(c*o.dashPhase)+"px")}eoFill(){var i;(i=this.current.element)==null||i.setAttributeNS(null,"fill-rule","evenodd"),this.fill()}fillStroke(){this.stroke(),this.fill()}eoFillStroke(){var i;(i=this.current.element)==null||i.setAttributeNS(null,"fill-rule","evenodd"),this.fillStroke()}closeStroke(){this.closePath(),this.stroke()}closeFillStroke(){this.closePath(),this.fillStroke()}closeEOFillStroke(){this.closePath(),this.eoFillStroke()}paintSolidColorImageMask(){const i=this.svgFactory.createElement("svg:rect");i.setAttributeNS(null,"x","0"),i.setAttributeNS(null,"y","0"),i.setAttributeNS(null,"width","1px"),i.setAttributeNS(null,"height","1px"),i.setAttributeNS(null,"fill",this.current.fillColor),this._ensureTransformGroup().append(i)}paintImageXObject(i){const c=this.getObject(i);if(!c){(0,d.warn)(`Dependent image with object ID ${i} is not ready yet`);return}this.paintInlineImageXObject(c)}paintInlineImageXObject(i,c){const o=i.width,l=i.height,h=D(i,this.forceDataSchema,!!c),F=this.svgFactory.createElement("svg:rect");F.setAttributeNS(null,"x","0"),F.setAttributeNS(null,"y","0"),F.setAttributeNS(null,"width",m(o)),F.setAttributeNS(null,"height",m(l)),this.current.element=F,this.clip("nonzero");const r=this.svgFactory.createElement("svg:image");r.setAttributeNS(ht,"xlink:href",h),r.setAttributeNS(null,"x","0"),r.setAttributeNS(null,"y",m(-l)),r.setAttributeNS(null,"width",m(o)+"px"),r.setAttributeNS(null,"height",m(l)+"px"),r.setAttributeNS(null,"transform",`scale(${m(1/o)} ${m(-1/l)})`),c?c.append(r):this._ensureTransformGroup().append(r)}paintImageMaskXObject(i){const c=this.getObject(i.data,i);if(c.bitmap){(0,d.warn)("paintImageMaskXObject: ImageBitmap support is not implemented, ensure that the `isOffscreenCanvasSupported` API parameter is disabled.");return}const o=this.current,l=c.width,h=c.height,F=o.fillColor;o.maskId=`mask${u++}`;const r=this.svgFactory.createElement("svg:mask");r.setAttributeNS(null,"id",o.maskId);const v=this.svgFactory.createElement("svg:rect");v.setAttributeNS(null,"x","0"),v.setAttributeNS(null,"y","0"),v.setAttributeNS(null,"width",m(l)),v.setAttributeNS(null,"height",m(h)),v.setAttributeNS(null,"fill",F),v.setAttributeNS(null,"mask",`url(#${o.maskId})`),this.defs.append(r),this._ensureTransformGroup().append(v),this.paintInlineImageXObject(c,r)}paintFormXObjectBegin(i,c){if(Array.isArray(i)&&i.length===6&&this.transform(i[0],i[1],i[2],i[3],i[4],i[5]),c){const o=c[2]-c[0],l=c[3]-c[1],h=this.svgFactory.createElement("svg:rect");h.setAttributeNS(null,"x",c[0]),h.setAttributeNS(null,"y",c[1]),h.setAttributeNS(null,"width",m(o)),h.setAttributeNS(null,"height",m(l)),this.current.element=h,this.clip("nonzero"),this.endPath()}}paintFormXObjectEnd(){}_initialize(i){const c=this.svgFactory.create(i.width,i.height),o=this.svgFactory.createElement("svg:defs");c.append(o),this.defs=o;const l=this.svgFactory.createElement("svg:g");return l.setAttributeNS(null,"transform",E(i.transform)),c.append(l),this.svg=l,c}_ensureClipGroup(){if(!this.current.clipGroup){const i=this.svgFactory.createElement("svg:g");i.setAttributeNS(null,"clip-path",this.current.activeClipUrl),this.svg.append(i),this.current.clipGroup=i}return this.current.clipGroup}_ensureTransformGroup(){return this.tgrp||(this.tgrp=this.svgFactory.createElement("svg:g"),this.tgrp.setAttributeNS(null,"transform",E(this.transformMatrix)),this.current.activeClipUrl?this._ensureClipGroup().append(this.tgrp):this.svg.append(this.tgrp)),this.tgrp}}e.SVGGraphics=C},(T,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.XfaText=void 0;class B{static textContent(d){const H=[],W={items:H,styles:Object.create(null)};function ht(P){var D;if(!P)return;let x=null;const f=P.name;if(f==="#text")x=P.value;else if(B.shouldBuildText(f))(D=P==null?void 0:P.attributes)!=null&&D.textContent?x=P.attributes.textContent:P.value&&(x=P.value);else return;if(x!==null&&H.push({str:x}),!!P.children)for(const I of P.children)ht(I)}return ht(d),W}static shouldBuildText(d){return!(d==="textarea"||d==="input"||d==="option"||d==="select")}}e.XfaText=B},(T,e,B)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.TextLayerRenderTask=void 0,e.renderTextLayer=E,e.updateTextLayer=p;var n=B(1),d=B(6);const H=1e5,W=30,ht=.8,P=new Map;function x(u,b){let C;if(b&&n.FeatureTest.isOffscreenCanvasSupported)C=new OffscreenCanvas(u,u).getContext("2d",{alpha:!1});else{const _=document.createElement("canvas");_.width=_.height=u,C=_.getContext("2d",{alpha:!1})}return C}function f(u,b){const C=P.get(u);if(C)return C;const _=x(W,b);_.font=`${W}px ${u}`;const i=_.measureText("");let c=i.fontBoundingBoxAscent,o=Math.abs(i.fontBoundingBoxDescent);if(c){const h=c/(c+o);return P.set(u,h),_.canvas.width=_.canvas.height=0,h}_.strokeStyle="red",_.clearRect(0,0,W,W),_.strokeText("g",0,0);let l=_.getImageData(0,0,W,W).data;o=0;for(let h=l.length-1-3;h>=0;h-=4)if(l[h]>0){o=Math.ceil(h/4/W);break}_.clearRect(0,0,W,W),_.strokeText("A",0,W),l=_.getImageData(0,0,W,W).data,c=0;for(let h=0,F=l.length;h<F;h+=4)if(l[h]>0){c=W-Math.floor(h/4/W);break}if(_.canvas.width=_.canvas.height=0,c){const h=c/(c+o);return P.set(u,h),h}return P.set(u,ht),ht}function D(u,b,C){const _=document.createElement("span"),i={angle:0,canvasWidth:0,hasText:b.str!=="",hasEOL:b.hasEOL,fontSize:0};u._textDivs.push(_);const c=n.Util.transform(u._transform,b.transform);let o=Math.atan2(c[1],c[0]);const l=C[b.fontName];l.vertical&&(o+=Math.PI/2);const h=Math.hypot(c[2],c[3]),F=h*f(l.fontFamily,u._isOffscreenCanvasSupported);let r,v;o===0?(r=c[4],v=c[5]-F):(r=c[4]+F*Math.sin(o),v=c[5]-F*Math.cos(o));const k="calc(var(--scale-factor)*",Z=_.style;u._container===u._rootContainer?(Z.left=`${(100*r/u._pageWidth).toFixed(2)}%`,Z.top=`${(100*v/u._pageHeight).toFixed(2)}%`):(Z.left=`${k}${r.toFixed(2)}px)`,Z.top=`${k}${v.toFixed(2)}px)`),Z.fontSize=`${k}${h.toFixed(2)}px)`,Z.fontFamily=l.fontFamily,i.fontSize=h,_.setAttribute("role","presentation"),_.textContent=b.str,_.dir=b.dir,u._fontInspectorEnabled&&(_.dataset.fontName=b.fontName),o!==0&&(i.angle=o*(180/Math.PI));let $=!1;if(b.str.length>1)$=!0;else if(b.str!==" "&&b.transform[0]!==b.transform[3]){const q=Math.abs(b.transform[0]),G=Math.abs(b.transform[3]);q!==G&&Math.max(q,G)/Math.min(q,G)>1.5&&($=!0)}$&&(i.canvasWidth=l.vertical?b.height:b.width),u._textDivProperties.set(_,i),u._isReadableStream&&u._layoutText(_)}function I(u){const{div:b,scale:C,properties:_,ctx:i,prevFontSize:c,prevFontFamily:o}=u,{style:l}=b;let h="";if(_.canvasWidth!==0&&_.hasText){const{fontFamily:F}=l,{canvasWidth:r,fontSize:v}=_;(c!==v||o!==F)&&(i.font=`${v*C}px ${F}`,u.prevFontSize=v,u.prevFontFamily=F);const{width:k}=i.measureText(b.textContent);k>0&&(h=`scaleX(${r*C/k})`)}_.angle!==0&&(h=`rotate(${_.angle}deg) ${h}`),h.length>0&&(l.transform=h)}function y(u){if(u._canceled)return;const b=u._textDivs,C=u._capability;if(b.length>H){C.resolve();return}if(!u._isReadableStream)for(const i of b)u._layoutText(i);C.resolve()}class m{constructor({textContentSource:b,container:C,viewport:_,textDivs:i,textDivProperties:c,textContentItemsStr:o,isOffscreenCanvasSupported:l}){var k;this._textContentSource=b,this._isReadableStream=b instanceof ReadableStream,this._container=this._rootContainer=C,this._textDivs=i||[],this._textContentItemsStr=o||[],this._isOffscreenCanvasSupported=l,this._fontInspectorEnabled=!!((k=globalThis.FontInspector)!=null&&k.enabled),this._reader=null,this._textDivProperties=c||new WeakMap,this._canceled=!1,this._capability=new n.PromiseCapability,this._layoutTextParams={prevFontSize:null,prevFontFamily:null,div:null,scale:_.scale*(globalThis.devicePixelRatio||1),properties:null,ctx:x(0,l)};const{pageWidth:h,pageHeight:F,pageX:r,pageY:v}=_.rawDims;this._transform=[1,0,0,-1,-r,v+F],this._pageWidth=h,this._pageHeight=F,(0,d.setLayerDimensions)(C,_),this._capability.promise.finally(()=>{this._layoutTextParams=null}).catch(()=>{})}get promise(){return this._capability.promise}cancel(){this._canceled=!0,this._reader&&(this._reader.cancel(new n.AbortException("TextLayer task cancelled.")).catch(()=>{}),this._reader=null),this._capability.reject(new n.AbortException("TextLayer task cancelled."))}_processItems(b,C){for(const _ of b){if(_.str===void 0){if(_.type==="beginMarkedContentProps"||_.type==="beginMarkedContent"){const i=this._container;this._container=document.createElement("span"),this._container.classList.add("markedContent"),_.id!==null&&this._container.setAttribute("id",`${_.id}`),i.append(this._container)}else _.type==="endMarkedContent"&&(this._container=this._container.parentNode);continue}this._textContentItemsStr.push(_.str),D(this,_,C)}}_layoutText(b){const C=this._layoutTextParams.properties=this._textDivProperties.get(b);if(this._layoutTextParams.div=b,I(this._layoutTextParams),C.hasText&&this._container.append(b),C.hasEOL){const _=document.createElement("br");_.setAttribute("role","presentation"),this._container.append(_)}}_render(){const b=new n.PromiseCapability;let C=Object.create(null);if(this._isReadableStream){const _=()=>{this._reader.read().then(({value:i,done:c})=>{if(c){b.resolve();return}Object.assign(C,i.styles),this._processItems(i.items,C),_()},b.reject)};this._reader=this._textContentSource.getReader(),_()}else if(this._textContentSource){const{items:_,styles:i}=this._textContentSource;this._processItems(_,i),b.resolve()}else throw new Error('No "textContentSource" parameter specified.');b.promise.then(()=>{C=null,y(this)},this._capability.reject)}}e.TextLayerRenderTask=m;function E(u){!u.textContentSource&&(u.textContent||u.textContentStream)&&((0,d.deprecated)("The TextLayerRender `textContent`/`textContentStream` parameters will be removed in the future, please use `textContentSource` instead."),u.textContentSource=u.textContent||u.textContentStream);const{container:b,viewport:C}=u,_=getComputedStyle(b),i=_.getPropertyValue("visibility"),c=parseFloat(_.getPropertyValue("--scale-factor"));i==="visible"&&(!c||Math.abs(c-C.scale)>1e-5)&&console.error("The `--scale-factor` CSS-variable must be set, to the same value as `viewport.scale`, either on the `container`-element itself or higher up in the DOM.");const o=new m(u);return o._render(),o}function p({container:u,viewport:b,textDivs:C,textDivProperties:_,isOffscreenCanvasSupported:i,mustRotate:c=!0,mustRescale:o=!0}){if(c&&(0,d.setLayerDimensions)(u,{rotation:b.rotation}),o){const l=x(0,i),F={prevFontSize:null,prevFontFamily:null,div:null,scale:b.scale*(globalThis.devicePixelRatio||1),properties:null,ctx:l};for(const r of C)F.properties=_.get(r),F.div=r,I(F)}}},(T,e,B)=>{var f,D,I,y,m,E,p,u,b,C,_,Ke,xe,Je,Qe;Object.defineProperty(e,"__esModule",{value:!0}),e.AnnotationEditorLayer=void 0;var n=B(1),d=B(4),H=B(28),W=B(33),ht=B(6),P=B(34);const h=class h{constructor({uiManager:r,pageIndex:v,div:k,accessibilityManager:Z,annotationLayer:$,viewport:q,l10n:G}){rt(this,_);rt(this,f);rt(this,D,!1);rt(this,I,null);rt(this,y,this.pointerup.bind(this));rt(this,m,this.pointerdown.bind(this));rt(this,E,new Map);rt(this,p,!1);rt(this,u,!1);rt(this,b,!1);rt(this,C);const it=[H.FreeTextEditor,W.InkEditor,P.StampEditor];if(!h._initialized){h._initialized=!0;for(const R of it)R.initialize(G)}r.registerEditorTypes(it),ot(this,C,r),this.pageIndex=v,this.div=k,ot(this,f,Z),ot(this,I,$),this.viewport=q,t(this,C).addLayer(this)}get isEmpty(){return t(this,E).size===0}updateToolbar(r){t(this,C).updateToolbar(r)}updateMode(r=t(this,C).getMode()){K(this,_,Qe).call(this),r===n.AnnotationEditorType.INK?(this.addInkEditorIfNeeded(!1),this.disableClick()):this.enableClick(),r!==n.AnnotationEditorType.NONE&&(this.div.classList.toggle("freeTextEditing",r===n.AnnotationEditorType.FREETEXT),this.div.classList.toggle("inkEditing",r===n.AnnotationEditorType.INK),this.div.classList.toggle("stampEditing",r===n.AnnotationEditorType.STAMP),this.div.hidden=!1)}addInkEditorIfNeeded(r){if(!r&&t(this,C).getMode()!==n.AnnotationEditorType.INK)return;if(!r){for(const k of t(this,E).values())if(k.isEmpty()){k.setInBackground();return}}K(this,_,xe).call(this,{offsetX:0,offsetY:0},!1).setInBackground()}setEditingState(r){t(this,C).setEditingState(r)}addCommands(r){t(this,C).addCommands(r)}enable(){this.div.style.pointerEvents="auto";const r=new Set;for(const k of t(this,E).values())k.enableEditing(),k.annotationElementId&&r.add(k.annotationElementId);if(!t(this,I))return;const v=t(this,I).getEditableAnnotations();for(const k of v){if(k.hide(),t(this,C).isDeletedAnnotationElement(k.data.id)||r.has(k.data.id))continue;const Z=this.deserialize(k);Z&&(this.addOrRebuild(Z),Z.enableEditing())}}disable(){var v;ot(this,b,!0),this.div.style.pointerEvents="none";const r=new Set;for(const k of t(this,E).values()){if(k.disableEditing(),!k.annotationElementId||k.serialize()!==null){r.add(k.annotationElementId);continue}(v=this.getEditableAnnotation(k.annotationElementId))==null||v.show(),k.remove()}if(t(this,I)){const k=t(this,I).getEditableAnnotations();for(const Z of k){const{id:$}=Z.data;r.has($)||t(this,C).isDeletedAnnotationElement($)||Z.show()}}K(this,_,Qe).call(this),this.isEmpty&&(this.div.hidden=!0),ot(this,b,!1)}getEditableAnnotation(r){var v;return((v=t(this,I))==null?void 0:v.getEditableAnnotation(r))||null}setActiveEditor(r){t(this,C).getActive()!==r&&t(this,C).setActiveEditor(r)}enableClick(){this.div.addEventListener("pointerdown",t(this,m)),this.div.addEventListener("pointerup",t(this,y))}disableClick(){this.div.removeEventListener("pointerdown",t(this,m)),this.div.removeEventListener("pointerup",t(this,y))}attach(r){t(this,E).set(r.id,r);const{annotationElementId:v}=r;v&&t(this,C).isDeletedAnnotationElement(v)&&t(this,C).removeDeletedAnnotationElement(r)}detach(r){var v;t(this,E).delete(r.id),(v=t(this,f))==null||v.removePointerInTextLayer(r.contentDiv),!t(this,b)&&r.annotationElementId&&t(this,C).addDeletedAnnotationElement(r)}remove(r){this.detach(r),t(this,C).removeEditor(r),r.div.contains(document.activeElement)&&setTimeout(()=>{t(this,C).focusMainContainer()},0),r.div.remove(),r.isAttachedToDOM=!1,t(this,u)||this.addInkEditorIfNeeded(!1)}changeParent(r){var v;r.parent!==this&&(r.annotationElementId&&(t(this,C).addDeletedAnnotationElement(r.annotationElementId),d.AnnotationEditor.deleteAnnotationElement(r),r.annotationElementId=null),this.attach(r),(v=r.parent)==null||v.detach(r),r.setParent(this),r.div&&r.isAttachedToDOM&&(r.div.remove(),this.div.append(r.div)))}add(r){if(this.changeParent(r),t(this,C).addEditor(r),this.attach(r),!r.isAttachedToDOM){const v=r.render();this.div.append(v),r.isAttachedToDOM=!0}r.fixAndSetPosition(),r.onceAdded(),t(this,C).addToAnnotationStorage(r)}moveEditorInDOM(r){var k;if(!r.isAttachedToDOM)return;const{activeElement:v}=document;r.div.contains(v)&&(r._focusEventsAllowed=!1,setTimeout(()=>{r.div.contains(document.activeElement)?r._focusEventsAllowed=!0:(r.div.addEventListener("focusin",()=>{r._focusEventsAllowed=!0},{once:!0}),v.focus())},0)),r._structTreeParentId=(k=t(this,f))==null?void 0:k.moveElementInDOM(this.div,r.div,r.contentDiv,!0)}addOrRebuild(r){r.needsToBeRebuilt()?r.rebuild():this.add(r)}addUndoableEditor(r){const v=()=>r._uiManager.rebuild(r),k=()=>{r.remove()};this.addCommands({cmd:v,undo:k,mustExec:!1})}getNextId(){return t(this,C).getId()}pasteEditor(r,v){t(this,C).updateToolbar(r),t(this,C).updateMode(r);const{offsetX:k,offsetY:Z}=K(this,_,Je).call(this),$=this.getNextId(),q=K(this,_,Ke).call(this,{parent:this,id:$,x:k,y:Z,uiManager:t(this,C),isCentered:!0,...v});q&&this.add(q)}deserialize(r){switch(r.annotationType??r.annotationEditorType){case n.AnnotationEditorType.FREETEXT:return H.FreeTextEditor.deserialize(r,this,t(this,C));case n.AnnotationEditorType.INK:return W.InkEditor.deserialize(r,this,t(this,C));case n.AnnotationEditorType.STAMP:return P.StampEditor.deserialize(r,this,t(this,C))}return null}addNewEditor(){K(this,_,xe).call(this,K(this,_,Je).call(this),!0)}setSelected(r){t(this,C).setSelected(r)}toggleSelected(r){t(this,C).toggleSelected(r)}isSelected(r){return t(this,C).isSelected(r)}unselect(r){t(this,C).unselect(r)}pointerup(r){const{isMac:v}=n.FeatureTest.platform;if(!(r.button!==0||r.ctrlKey&&v)&&r.target===this.div&&t(this,p)){if(ot(this,p,!1),!t(this,D)){ot(this,D,!0);return}if(t(this,C).getMode()===n.AnnotationEditorType.STAMP){t(this,C).unselectAll();return}K(this,_,xe).call(this,r,!1)}}pointerdown(r){if(t(this,p)){ot(this,p,!1);return}const{isMac:v}=n.FeatureTest.platform;if(r.button!==0||r.ctrlKey&&v||r.target!==this.div)return;ot(this,p,!0);const k=t(this,C).getActive();ot(this,D,!k||k.isEmpty())}findNewParent(r,v,k){const Z=t(this,C).findParent(v,k);return Z===null||Z===this?!1:(Z.changeParent(r),!0)}destroy(){var r,v;((r=t(this,C).getActive())==null?void 0:r.parent)===this&&(t(this,C).commitOrRemove(),t(this,C).setActiveEditor(null));for(const k of t(this,E).values())(v=t(this,f))==null||v.removePointerInTextLayer(k.contentDiv),k.setParent(null),k.isAttachedToDOM=!1,k.div.remove();this.div=null,t(this,E).clear(),t(this,C).removeLayer(this)}render({viewport:r}){this.viewport=r,(0,ht.setLayerDimensions)(this.div,r);for(const v of t(this,C).getEditors(this.pageIndex))this.add(v);this.updateMode()}update({viewport:r}){t(this,C).commitOrRemove(),this.viewport=r,(0,ht.setLayerDimensions)(this.div,{rotation:r.rotation}),this.updateMode()}get pageDimensions(){const{pageWidth:r,pageHeight:v}=this.viewport.rawDims;return[r,v]}};f=new WeakMap,D=new WeakMap,I=new WeakMap,y=new WeakMap,m=new WeakMap,E=new WeakMap,p=new WeakMap,u=new WeakMap,b=new WeakMap,C=new WeakMap,_=new WeakSet,Ke=function(r){switch(t(this,C).getMode()){case n.AnnotationEditorType.FREETEXT:return new H.FreeTextEditor(r);case n.AnnotationEditorType.INK:return new W.InkEditor(r);case n.AnnotationEditorType.STAMP:return new P.StampEditor(r)}return null},xe=function(r,v){const k=this.getNextId(),Z=K(this,_,Ke).call(this,{parent:this,id:k,x:r.offsetX,y:r.offsetY,uiManager:t(this,C),isCentered:v});return Z&&this.add(Z),Z},Je=function(){const{x:r,y:v,width:k,height:Z}=this.div.getBoundingClientRect(),$=Math.max(0,r),q=Math.max(0,v),G=Math.min(window.innerWidth,r+k),it=Math.min(window.innerHeight,v+Z),R=($+G)/2-r,V=(q+it)/2-v,[et,S]=this.viewport.rotation%180===0?[R,V]:[V,R];return{offsetX:et,offsetY:S}},Qe=function(){ot(this,u,!0);for(const r of t(this,E).values())r.isEmpty()&&r.remove();ot(this,u,!1)},Kt(h,"_initialized",!1);let x=h;e.AnnotationEditorLayer=x},(T,e,B)=>{var P,x,f,D,I,y,m,E,p,u,Pn,Cn,Rn,ge,Ze,kn,tn;Object.defineProperty(e,"__esModule",{value:!0}),e.FreeTextEditor=void 0;var n=B(1),d=B(5),H=B(4),W=B(29);const h=class h extends H.AnnotationEditor{constructor(v){super({...v,name:"freeTextEditor"});rt(this,u);rt(this,P,this.editorDivBlur.bind(this));rt(this,x,this.editorDivFocus.bind(this));rt(this,f,this.editorDivInput.bind(this));rt(this,D,this.editorDivKeydown.bind(this));rt(this,I);rt(this,y,"");rt(this,m,`${this.id}-editor`);rt(this,E);rt(this,p,null);ot(this,I,v.color||h._defaultColor||H.AnnotationEditor._defaultLineColor),ot(this,E,v.fontSize||h._defaultFontSize)}static get _keyboardManager(){const v=h.prototype,k=q=>q.isEmpty(),Z=d.AnnotationEditorUIManager.TRANSLATE_SMALL,$=d.AnnotationEditorUIManager.TRANSLATE_BIG;return(0,n.shadow)(this,"_keyboardManager",new d.KeyboardManager([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],v.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],v.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],v._translateEmpty,{args:[-Z,0],checker:k}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],v._translateEmpty,{args:[-$,0],checker:k}],[["ArrowRight","mac+ArrowRight"],v._translateEmpty,{args:[Z,0],checker:k}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],v._translateEmpty,{args:[$,0],checker:k}],[["ArrowUp","mac+ArrowUp"],v._translateEmpty,{args:[0,-Z],checker:k}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],v._translateEmpty,{args:[0,-$],checker:k}],[["ArrowDown","mac+ArrowDown"],v._translateEmpty,{args:[0,Z],checker:k}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],v._translateEmpty,{args:[0,$],checker:k}]]))}static initialize(v){H.AnnotationEditor.initialize(v,{strings:["free_text2_default_content","editor_free_text2_aria_label"]});const k=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(k.getPropertyValue("--freetext-padding"))}static updateDefaultParams(v,k){switch(v){case n.AnnotationEditorParamsType.FREETEXT_SIZE:h._defaultFontSize=k;break;case n.AnnotationEditorParamsType.FREETEXT_COLOR:h._defaultColor=k;break}}updateParams(v,k){switch(v){case n.AnnotationEditorParamsType.FREETEXT_SIZE:K(this,u,Pn).call(this,k);break;case n.AnnotationEditorParamsType.FREETEXT_COLOR:K(this,u,Cn).call(this,k);break}}static get defaultPropertiesToUpdate(){return[[n.AnnotationEditorParamsType.FREETEXT_SIZE,h._defaultFontSize],[n.AnnotationEditorParamsType.FREETEXT_COLOR,h._defaultColor||H.AnnotationEditor._defaultLineColor]]}get propertiesToUpdate(){return[[n.AnnotationEditorParamsType.FREETEXT_SIZE,t(this,E)],[n.AnnotationEditorParamsType.FREETEXT_COLOR,t(this,I)]]}_translateEmpty(v,k){this._uiManager.translateSelectedEditors(v,k,!0)}getInitialTranslation(){const v=this.parentScale;return[-h._internalPadding*v,-(h._internalPadding+t(this,E))*v]}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(this.isAttachedToDOM||this.parent.add(this)))}enableEditMode(){this.isInEditMode()||(this.parent.setEditingState(!1),this.parent.updateToolbar(n.AnnotationEditorType.FREETEXT),super.enableEditMode(),this.overlayDiv.classList.remove("enabled"),this.editorDiv.contentEditable=!0,this._isDraggable=!1,this.div.removeAttribute("aria-activedescendant"),this.editorDiv.addEventListener("keydown",t(this,D)),this.editorDiv.addEventListener("focus",t(this,x)),this.editorDiv.addEventListener("blur",t(this,P)),this.editorDiv.addEventListener("input",t(this,f)))}disableEditMode(){this.isInEditMode()&&(this.parent.setEditingState(!0),super.disableEditMode(),this.overlayDiv.classList.add("enabled"),this.editorDiv.contentEditable=!1,this.div.setAttribute("aria-activedescendant",t(this,m)),this._isDraggable=!0,this.editorDiv.removeEventListener("keydown",t(this,D)),this.editorDiv.removeEventListener("focus",t(this,x)),this.editorDiv.removeEventListener("blur",t(this,P)),this.editorDiv.removeEventListener("input",t(this,f)),this.div.focus({preventScroll:!0}),this.isEditing=!1,this.parent.div.classList.add("freeTextEditing"))}focusin(v){this._focusEventsAllowed&&(super.focusin(v),v.target!==this.editorDiv&&this.editorDiv.focus())}onceAdded(){var v;if(this.width){K(this,u,tn).call(this);return}this.enableEditMode(),this.editorDiv.focus(),(v=this._initialOptions)!=null&&v.isCentered&&this.center(),this._initialOptions=null}isEmpty(){return!this.editorDiv||this.editorDiv.innerText.trim()===""}remove(){this.isEditing=!1,this.parent&&(this.parent.setEditingState(!0),this.parent.div.classList.add("freeTextEditing")),super.remove()}commit(){if(!this.isInEditMode())return;super.commit(),this.disableEditMode();const v=t(this,y),k=ot(this,y,K(this,u,Rn).call(this).trimEnd());if(v===k)return;const Z=$=>{if(ot(this,y,$),!$){this.remove();return}K(this,u,Ze).call(this),this._uiManager.rebuild(this),K(this,u,ge).call(this)};this.addCommands({cmd:()=>{Z(k)},undo:()=>{Z(v)},mustExec:!1}),K(this,u,ge).call(this)}shouldGetKeyboardEvents(){return this.isInEditMode()}enterInEditMode(){this.enableEditMode(),this.editorDiv.focus()}dblclick(v){this.enterInEditMode()}keydown(v){v.target===this.div&&v.key==="Enter"&&(this.enterInEditMode(),v.preventDefault())}editorDivKeydown(v){h._keyboardManager.exec(this,v)}editorDivFocus(v){this.isEditing=!0}editorDivBlur(v){this.isEditing=!1}editorDivInput(v){this.parent.div.classList.toggle("freeTextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment"),this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox"),this.editorDiv.setAttribute("aria-multiline",!0)}render(){if(this.div)return this.div;let v,k;this.width&&(v=this.x,k=this.y),super.render(),this.editorDiv=document.createElement("div"),this.editorDiv.className="internal",this.editorDiv.setAttribute("id",t(this,m)),this.enableEditing(),H.AnnotationEditor._l10nPromise.get("editor_free_text2_aria_label").then($=>{var q;return(q=this.editorDiv)==null?void 0:q.setAttribute("aria-label",$)}),H.AnnotationEditor._l10nPromise.get("free_text2_default_content").then($=>{var q;return(q=this.editorDiv)==null?void 0:q.setAttribute("default-content",$)}),this.editorDiv.contentEditable=!0;const{style:Z}=this.editorDiv;if(Z.fontSize=`calc(${t(this,E)}px * var(--scale-factor))`,Z.color=t(this,I),this.div.append(this.editorDiv),this.overlayDiv=document.createElement("div"),this.overlayDiv.classList.add("overlay","enabled"),this.div.append(this.overlayDiv),(0,d.bindEvents)(this,this.div,["dblclick","keydown"]),this.width){const[$,q]=this.parentDimensions;if(this.annotationElementId){const{position:G}=t(this,p);let[it,R]=this.getInitialTranslation();[it,R]=this.pageTranslationToScreen(it,R);const[V,et]=this.pageDimensions,[S,s]=this.pageTranslation;let a,g;switch(this.rotation){case 0:a=v+(G[0]-S)/V,g=k+this.height-(G[1]-s)/et;break;case 90:a=v+(G[0]-S)/V,g=k-(G[1]-s)/et,[it,R]=[R,-it];break;case 180:a=v-this.width+(G[0]-S)/V,g=k-(G[1]-s)/et,[it,R]=[-it,-R];break;case 270:a=v+(G[0]-S-this.height*et)/V,g=k+(G[1]-s-this.width*V)/et,[it,R]=[-R,it];break}this.setAt(a*$,g*q,it,R)}else this.setAt(v*$,k*q,this.width*$,this.height*q);K(this,u,Ze).call(this),this._isDraggable=!0,this.editorDiv.contentEditable=!1}else this._isDraggable=!1,this.editorDiv.contentEditable=!0;return this.div}get contentDiv(){return this.editorDiv}static deserialize(v,k,Z){let $=null;if(v instanceof W.FreeTextAnnotationElement){const{data:{defaultAppearanceData:{fontSize:G,fontColor:it},rect:R,rotation:V,id:et},textContent:S,textPosition:s,parent:{page:{pageNumber:a}}}=v;if(!S||S.length===0)return null;$=v={annotationType:n.AnnotationEditorType.FREETEXT,color:Array.from(it),fontSize:G,value:S.join(`
`),position:s,pageIndex:a-1,rect:R,rotation:V,id:et,deleted:!1}}const q=super.deserialize(v,k,Z);return ot(q,E,v.fontSize),ot(q,I,n.Util.makeHexColor(...v.color)),ot(q,y,v.value),q.annotationElementId=v.id||null,ot(q,p,$),q}serialize(v=!1){if(this.isEmpty())return null;if(this.deleted)return{pageIndex:this.pageIndex,id:this.annotationElementId,deleted:!0};const k=h._internalPadding*this.parentScale,Z=this.getRect(k,k),$=H.AnnotationEditor._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:t(this,I)),q={annotationType:n.AnnotationEditorType.FREETEXT,color:$,fontSize:t(this,E),value:t(this,y),pageIndex:this.pageIndex,rect:Z,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return v?q:this.annotationElementId&&!K(this,u,kn).call(this,q)?null:(q.id=this.annotationElementId,q)}};P=new WeakMap,x=new WeakMap,f=new WeakMap,D=new WeakMap,I=new WeakMap,y=new WeakMap,m=new WeakMap,E=new WeakMap,p=new WeakMap,u=new WeakSet,Pn=function(v){const k=$=>{this.editorDiv.style.fontSize=`calc(${$}px * var(--scale-factor))`,this.translate(0,-($-t(this,E))*this.parentScale),ot(this,E,$),K(this,u,ge).call(this)},Z=t(this,E);this.addCommands({cmd:()=>{k(v)},undo:()=>{k(Z)},mustExec:!0,type:n.AnnotationEditorParamsType.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})},Cn=function(v){const k=t(this,I);this.addCommands({cmd:()=>{ot(this,I,this.editorDiv.style.color=v)},undo:()=>{ot(this,I,this.editorDiv.style.color=k)},mustExec:!0,type:n.AnnotationEditorParamsType.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})},Rn=function(){const v=this.editorDiv.getElementsByTagName("div");if(v.length===0)return this.editorDiv.innerText;const k=[];for(const Z of v)k.push(Z.innerText.replace(/\r\n?|\n/,""));return k.join(`
`)},ge=function(){const[v,k]=this.parentDimensions;let Z;if(this.isAttachedToDOM)Z=this.div.getBoundingClientRect();else{const{currentLayer:$,div:q}=this,G=q.style.display;q.style.display="hidden",$.div.append(this.div),Z=q.getBoundingClientRect(),q.remove(),q.style.display=G}this.rotation%180===this.parentRotation%180?(this.width=Z.width/v,this.height=Z.height/k):(this.width=Z.height/v,this.height=Z.width/k),this.fixAndSetPosition()},Ze=function(){if(this.editorDiv.replaceChildren(),!!t(this,y))for(const v of t(this,y).split(`
`)){const k=document.createElement("div");k.append(v?document.createTextNode(v):document.createElement("br")),this.editorDiv.append(k)}},kn=function(v){const{value:k,fontSize:Z,color:$,rect:q,pageIndex:G}=t(this,p);return v.value!==k||v.fontSize!==Z||v.rect.some((it,R)=>Math.abs(it-q[R])>=1)||v.color.some((it,R)=>it!==$[R])||v.pageIndex!==G},tn=function(v=!1){if(!this.annotationElementId)return;if(K(this,u,ge).call(this),!v&&(this.width===0||this.height===0)){setTimeout(()=>K(this,u,tn).call(this,!0),0);return}const k=h._internalPadding*this.parentScale;t(this,p).rect=this.getRect(k,k)},Kt(h,"_freeTextDefaultContent",""),Kt(h,"_internalPadding",0),Kt(h,"_defaultColor",null),Kt(h,"_defaultFontSize",10),Kt(h,"_type","freetext");let ht=h;e.FreeTextEditor=ht},(T,e,B)=>{var g,O,ie,Fn,nt,ct,ft,bt,mt,_t,J,Q,A,j,Y,st,pt,Et,At,tt,wt,xt,Mn,we,en,nn,Ot,Ht,qt,vt,at,lt,Rt,sn,$t,U,gt,Tt,Dn,rn;Object.defineProperty(e,"__esModule",{value:!0}),e.StampAnnotationElement=e.InkAnnotationElement=e.FreeTextAnnotationElement=e.AnnotationLayer=void 0;var n=B(1),d=B(6),H=B(3),W=B(30),ht=B(31),P=B(32);const x=1e3,f=9,D=new WeakSet;function I(Ct){return{width:Ct[2]-Ct[0],height:Ct[3]-Ct[1]}}class y{static create(M){switch(M.data.annotationType){case n.AnnotationType.LINK:return new E(M);case n.AnnotationType.TEXT:return new p(M);case n.AnnotationType.WIDGET:switch(M.data.fieldType){case"Tx":return new b(M);case"Btn":return M.data.radioButton?new i(M):M.data.checkBox?new _(M):new c(M);case"Ch":return new o(M);case"Sig":return new C(M)}return new u(M);case n.AnnotationType.POPUP:return new l(M);case n.AnnotationType.FREETEXT:return new F(M);case n.AnnotationType.LINE:return new r(M);case n.AnnotationType.SQUARE:return new v(M);case n.AnnotationType.CIRCLE:return new k(M);case n.AnnotationType.POLYLINE:return new Z(M);case n.AnnotationType.CARET:return new q(M);case n.AnnotationType.INK:return new G(M);case n.AnnotationType.POLYGON:return new $(M);case n.AnnotationType.HIGHLIGHT:return new it(M);case n.AnnotationType.UNDERLINE:return new R(M);case n.AnnotationType.SQUIGGLY:return new V(M);case n.AnnotationType.STRIKEOUT:return new et(M);case n.AnnotationType.STAMP:return new S(M);case n.AnnotationType.FILEATTACHMENT:return new s(M);default:return new m(M)}}}const L=class L{constructor(M,{isRenderable:w=!1,ignoreBorder:z=!1,createQuadrilaterals:dt=!1}={}){rt(this,g,!1);this.isRenderable=w,this.data=M.data,this.layer=M.layer,this.linkService=M.linkService,this.downloadManager=M.downloadManager,this.imageResourcesPath=M.imageResourcesPath,this.renderForms=M.renderForms,this.svgFactory=M.svgFactory,this.annotationStorage=M.annotationStorage,this.enableScripting=M.enableScripting,this.hasJSActions=M.hasJSActions,this._fieldObjects=M.fieldObjects,this.parent=M.parent,w&&(this.container=this._createContainer(z)),dt&&this._createQuadrilaterals()}static _hasPopupData({titleObj:M,contentsObj:w,richText:z}){return!!(M!=null&&M.str||w!=null&&w.str||z!=null&&z.str)}get hasPopupData(){return L._hasPopupData(this.data)}_createContainer(M){const{data:w,parent:{page:z,viewport:dt}}=this,ut=document.createElement("section");ut.setAttribute("data-annotation-id",w.id),this instanceof u||(ut.tabIndex=x),ut.style.zIndex=this.parent.zIndex++,this.data.popupRef&&ut.setAttribute("aria-haspopup","dialog"),w.noRotate&&ut.classList.add("norotate");const{pageWidth:yt,pageHeight:St,pageX:Lt,pageY:Ft}=dt.rawDims;if(!w.rect||this instanceof l){const{rotation:Bt}=w;return!w.hasOwnCanvas&&Bt!==0&&this.setRotation(Bt,ut),ut}const{width:Pt,height:Wt}=I(w.rect),Mt=n.Util.normalizeRect([w.rect[0],z.view[3]-w.rect[1]+z.view[1],w.rect[2],z.view[3]-w.rect[3]+z.view[1]]);if(!M&&w.borderStyle.width>0){ut.style.borderWidth=`${w.borderStyle.width}px`;const Bt=w.borderStyle.horizontalCornerRadius,Gt=w.borderStyle.verticalCornerRadius;if(Bt>0||Gt>0){const Yt=`calc(${Bt}px * var(--scale-factor)) / calc(${Gt}px * var(--scale-factor))`;ut.style.borderRadius=Yt}else if(this instanceof i){const Yt=`calc(${Pt}px * var(--scale-factor)) / calc(${Wt}px * var(--scale-factor))`;ut.style.borderRadius=Yt}switch(w.borderStyle.style){case n.AnnotationBorderStyleType.SOLID:ut.style.borderStyle="solid";break;case n.AnnotationBorderStyleType.DASHED:ut.style.borderStyle="dashed";break;case n.AnnotationBorderStyleType.BEVELED:(0,n.warn)("Unimplemented border style: beveled");break;case n.AnnotationBorderStyleType.INSET:(0,n.warn)("Unimplemented border style: inset");break;case n.AnnotationBorderStyleType.UNDERLINE:ut.style.borderBottomStyle="solid";break}const Xt=w.borderColor||null;Xt?(ot(this,g,!0),ut.style.borderColor=n.Util.makeHexColor(Xt[0]|0,Xt[1]|0,Xt[2]|0)):ut.style.borderWidth=0}ut.style.left=`${100*(Mt[0]-Lt)/yt}%`,ut.style.top=`${100*(Mt[1]-Ft)/St}%`;const{rotation:It}=w;return w.hasOwnCanvas||It===0?(ut.style.width=`${100*Pt/yt}%`,ut.style.height=`${100*Wt/St}%`):this.setRotation(It,ut),ut}setRotation(M,w=this.container){if(!this.data.rect)return;const{pageWidth:z,pageHeight:dt}=this.parent.viewport.rawDims,{width:ut,height:yt}=I(this.data.rect);let St,Lt;M%180===0?(St=100*ut/z,Lt=100*yt/dt):(St=100*yt/z,Lt=100*ut/dt),w.style.width=`${St}%`,w.style.height=`${Lt}%`,w.setAttribute("data-main-rotation",(360-M)%360)}get _commonActions(){const M=(w,z,dt)=>{const ut=dt.detail[w],yt=ut[0],St=ut.slice(1);dt.target.style[z]=W.ColorConverters[`${yt}_HTML`](St),this.annotationStorage.setValue(this.data.id,{[z]:W.ColorConverters[`${yt}_rgb`](St)})};return(0,n.shadow)(this,"_commonActions",{display:w=>{const{display:z}=w.detail,dt=z%2===1;this.container.style.visibility=dt?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noView:dt,noPrint:z===1||z===2})},print:w=>{this.annotationStorage.setValue(this.data.id,{noPrint:!w.detail.print})},hidden:w=>{const{hidden:z}=w.detail;this.container.style.visibility=z?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noPrint:z,noView:z})},focus:w=>{setTimeout(()=>w.target.focus({preventScroll:!1}),0)},userName:w=>{w.target.title=w.detail.userName},readonly:w=>{w.target.disabled=w.detail.readonly},required:w=>{this._setRequired(w.target,w.detail.required)},bgColor:w=>{M("bgColor","backgroundColor",w)},fillColor:w=>{M("fillColor","backgroundColor",w)},fgColor:w=>{M("fgColor","color",w)},textColor:w=>{M("textColor","color",w)},borderColor:w=>{M("borderColor","borderColor",w)},strokeColor:w=>{M("strokeColor","borderColor",w)},rotation:w=>{const z=w.detail.rotation;this.setRotation(z),this.annotationStorage.setValue(this.data.id,{rotation:z})}})}_dispatchEventFromSandbox(M,w){const z=this._commonActions;for(const dt of Object.keys(w.detail)){const ut=M[dt]||z[dt];ut==null||ut(w)}}_setDefaultPropertiesFromJS(M){if(!this.enableScripting)return;const w=this.annotationStorage.getRawValue(this.data.id);if(!w)return;const z=this._commonActions;for(const[dt,ut]of Object.entries(w)){const yt=z[dt];if(yt){const St={detail:{[dt]:ut},target:M};yt(St),delete w[dt]}}}_createQuadrilaterals(){if(!this.container)return;const{quadPoints:M}=this.data;if(!M)return;const[w,z,dt,ut]=this.data.rect;if(M.length===1){const[,{x:Gt,y:Xt},{x:Yt,y:Qt}]=M[0];if(dt===Gt&&ut===Xt&&w===Yt&&z===Qt)return}const{style:yt}=this.container;let St;if(t(this,g)){const{borderColor:Gt,borderWidth:Xt}=yt;yt.borderWidth=0,St=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">',`<g fill="transparent" stroke="${Gt}" stroke-width="${Xt}">`],this.container.classList.add("hasBorder")}const Lt=dt-w,Ft=ut-z,{svgFactory:Pt}=this,Wt=Pt.createElement("svg");Wt.classList.add("quadrilateralsContainer"),Wt.setAttribute("width",0),Wt.setAttribute("height",0);const Mt=Pt.createElement("defs");Wt.append(Mt);const It=Pt.createElement("clipPath"),Bt=`clippath_${this.data.id}`;It.setAttribute("id",Bt),It.setAttribute("clipPathUnits","objectBoundingBox"),Mt.append(It);for(const[,{x:Gt,y:Xt},{x:Yt,y:Qt}]of M){const Jt=Pt.createElement("rect"),te=(Yt-w)/Lt,ne=(ut-Xt)/Ft,se=(Gt-Yt)/Lt,pn=(Xt-Qt)/Ft;Jt.setAttribute("x",te),Jt.setAttribute("y",ne),Jt.setAttribute("width",se),Jt.setAttribute("height",pn),It.append(Jt),St==null||St.push(`<rect vector-effect="non-scaling-stroke" x="${te}" y="${ne}" width="${se}" height="${pn}"/>`)}t(this,g)&&(St.push("</g></svg>')"),yt.backgroundImage=St.join("")),this.container.append(Wt),this.container.style.clipPath=`url(#${Bt})`}_createPopup(){const{container:M,data:w}=this;M.setAttribute("aria-haspopup","dialog");const z=new l({data:{color:w.color,titleObj:w.titleObj,modificationDate:w.modificationDate,contentsObj:w.contentsObj,richText:w.richText,parentRect:w.rect,borderStyle:0,id:`popup_${w.id}`,rotation:w.rotation},parent:this.parent,elements:[this]});this.parent.div.append(z.render())}render(){(0,n.unreachable)("Abstract method `AnnotationElement.render` called")}_getElementsByName(M,w=null){const z=[];if(this._fieldObjects){const dt=this._fieldObjects[M];if(dt)for(const{page:ut,id:yt,exportValues:St}of dt){if(ut===-1||yt===w)continue;const Lt=typeof St=="string"?St:null,Ft=document.querySelector(`[data-element-id="${yt}"]`);if(Ft&&!D.has(Ft)){(0,n.warn)(`_getElementsByName - element not allowed: ${yt}`);continue}z.push({id:yt,exportValue:Lt,domElement:Ft})}return z}for(const dt of document.getElementsByName(M)){const{exportValue:ut}=dt,yt=dt.getAttribute("data-element-id");yt!==w&&D.has(dt)&&z.push({id:yt,exportValue:ut,domElement:dt})}return z}show(){var M;this.container&&(this.container.hidden=!1),(M=this.popup)==null||M.maybeShow()}hide(){var M;this.container&&(this.container.hidden=!0),(M=this.popup)==null||M.forceHide()}getElementsToTriggerPopup(){return this.container}addHighlightArea(){const M=this.getElementsToTriggerPopup();if(Array.isArray(M))for(const w of M)w.classList.add("highlightArea");else M.classList.add("highlightArea")}_editOnDoubleClick(){const{annotationEditorType:M,data:{id:w}}=this;this.container.addEventListener("dblclick",()=>{var z;(z=this.linkService.eventBus)==null||z.dispatch("switchannotationeditormode",{source:this,mode:M,editId:w})})}};g=new WeakMap;let m=L;class E extends m{constructor(w,z=null){super(w,{isRenderable:!0,ignoreBorder:!!(z!=null&&z.ignoreBorder),createQuadrilaterals:!0});rt(this,O);this.isTooltipOnly=w.data.isTooltipOnly}render(){const{data:w,linkService:z}=this,dt=document.createElement("a");dt.setAttribute("data-element-id",w.id);let ut=!1;return w.url?(z.addLinkAttributes(dt,w.url,w.newWindow),ut=!0):w.action?(this._bindNamedAction(dt,w.action),ut=!0):w.attachment?(this._bindAttachment(dt,w.attachment),ut=!0):w.setOCGState?(K(this,O,Fn).call(this,dt,w.setOCGState),ut=!0):w.dest?(this._bindLink(dt,w.dest),ut=!0):(w.actions&&(w.actions.Action||w.actions["Mouse Up"]||w.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions&&(this._bindJSAction(dt,w),ut=!0),w.resetForm?(this._bindResetFormAction(dt,w.resetForm),ut=!0):this.isTooltipOnly&&!ut&&(this._bindLink(dt,""),ut=!0)),this.container.classList.add("linkAnnotation"),ut&&this.container.append(dt),this.container}_bindLink(w,z){w.href=this.linkService.getDestinationHash(z),w.onclick=()=>(z&&this.linkService.goToDestination(z),!1),(z||z==="")&&K(this,O,ie).call(this)}_bindNamedAction(w,z){w.href=this.linkService.getAnchorUrl(""),w.onclick=()=>(this.linkService.executeNamedAction(z),!1),K(this,O,ie).call(this)}_bindAttachment(w,z){w.href=this.linkService.getAnchorUrl(""),w.onclick=()=>{var dt;return(dt=this.downloadManager)==null||dt.openOrDownloadData(this.container,z.content,z.filename),!1},K(this,O,ie).call(this)}_bindJSAction(w,z){w.href=this.linkService.getAnchorUrl("");const dt=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const ut of Object.keys(z.actions)){const yt=dt.get(ut);yt&&(w[yt]=()=>{var St;return(St=this.linkService.eventBus)==null||St.dispatch("dispatcheventinsandbox",{source:this,detail:{id:z.id,name:ut}}),!1})}w.onclick||(w.onclick=()=>!1),K(this,O,ie).call(this)}_bindResetFormAction(w,z){const dt=w.onclick;if(dt||(w.href=this.linkService.getAnchorUrl("")),K(this,O,ie).call(this),!this._fieldObjects){(0,n.warn)('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.'),dt||(w.onclick=()=>!1);return}w.onclick=()=>{var Wt;dt==null||dt();const{fields:ut,refs:yt,include:St}=z,Lt=[];if(ut.length!==0||yt.length!==0){const Mt=new Set(yt);for(const It of ut){const Bt=this._fieldObjects[It]||[];for(const{id:Gt}of Bt)Mt.add(Gt)}for(const It of Object.values(this._fieldObjects))for(const Bt of It)Mt.has(Bt.id)===St&&Lt.push(Bt)}else for(const Mt of Object.values(this._fieldObjects))Lt.push(...Mt);const Ft=this.annotationStorage,Pt=[];for(const Mt of Lt){const{id:It}=Mt;switch(Pt.push(It),Mt.type){case"text":{const Gt=Mt.defaultValue||"";Ft.setValue(It,{value:Gt});break}case"checkbox":case"radiobutton":{const Gt=Mt.defaultValue===Mt.exportValues;Ft.setValue(It,{value:Gt});break}case"combobox":case"listbox":{const Gt=Mt.defaultValue||"";Ft.setValue(It,{value:Gt});break}default:continue}const Bt=document.querySelector(`[data-element-id="${It}"]`);if(Bt){if(!D.has(Bt)){(0,n.warn)(`_bindResetFormAction - element not allowed: ${It}`);continue}}else continue;Bt.dispatchEvent(new Event("resetform"))}return this.enableScripting&&((Wt=this.linkService.eventBus)==null||Wt.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:Pt,name:"ResetForm"}})),!1}}}O=new WeakSet,ie=function(){this.container.setAttribute("data-internal-link","")},Fn=function(w,z){w.href=this.linkService.getAnchorUrl(""),w.onclick=()=>(this.linkService.executeSetOCGState(z),!1),K(this,O,ie).call(this)};class p extends m{constructor(M){super(M,{isRenderable:!0})}render(){this.container.classList.add("textAnnotation");const M=document.createElement("img");return M.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",M.alt="[{{type}} Annotation]",M.dataset.l10nId="text_annotation_type",M.dataset.l10nArgs=JSON.stringify({type:this.data.name}),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.append(M),this.container}}class u extends m{render(){return this.data.alternativeText&&(this.container.title=this.data.alternativeText),this.container}showElementAndHideCanvas(M){var w;this.data.hasOwnCanvas&&(((w=M.previousSibling)==null?void 0:w.nodeName)==="CANVAS"&&(M.previousSibling.hidden=!0),M.hidden=!1)}_getKeyModifier(M){const{isWin:w,isMac:z}=n.FeatureTest.platform;return w&&M.ctrlKey||z&&M.metaKey}_setEventListener(M,w,z,dt,ut){z.includes("mouse")?M.addEventListener(z,yt=>{var St;(St=this.linkService.eventBus)==null||St.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:dt,value:ut(yt),shift:yt.shiftKey,modifier:this._getKeyModifier(yt)}})}):M.addEventListener(z,yt=>{var St;if(z==="blur"){if(!w.focused||!yt.relatedTarget)return;w.focused=!1}else if(z==="focus"){if(w.focused)return;w.focused=!0}ut&&((St=this.linkService.eventBus)==null||St.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:dt,value:ut(yt)}}))})}_setEventListeners(M,w,z,dt){var ut,yt,St;for(const[Lt,Ft]of z)(Ft==="Action"||(ut=this.data.actions)!=null&&ut[Ft])&&((Ft==="Focus"||Ft==="Blur")&&(w||(w={focused:!1})),this._setEventListener(M,w,Lt,Ft,dt),Ft==="Focus"&&!((yt=this.data.actions)!=null&&yt.Blur)?this._setEventListener(M,w,"blur","Blur",null):Ft==="Blur"&&!((St=this.data.actions)!=null&&St.Focus)&&this._setEventListener(M,w,"focus","Focus",null))}_setBackgroundColor(M){const w=this.data.backgroundColor||null;M.style.backgroundColor=w===null?"transparent":n.Util.makeHexColor(w[0],w[1],w[2])}_setTextStyle(M){const w=["left","center","right"],{fontColor:z}=this.data.defaultAppearanceData,dt=this.data.defaultAppearanceData.fontSize||f,ut=M.style;let yt;const St=2,Lt=Ft=>Math.round(10*Ft)/10;if(this.data.multiLine){const Ft=Math.abs(this.data.rect[3]-this.data.rect[1]-St),Pt=Math.round(Ft/(n.LINE_FACTOR*dt))||1,Wt=Ft/Pt;yt=Math.min(dt,Lt(Wt/n.LINE_FACTOR))}else{const Ft=Math.abs(this.data.rect[3]-this.data.rect[1]-St);yt=Math.min(dt,Lt(Ft/n.LINE_FACTOR))}ut.fontSize=`calc(${yt}px * var(--scale-factor))`,ut.color=n.Util.makeHexColor(z[0],z[1],z[2]),this.data.textAlignment!==null&&(ut.textAlign=w[this.data.textAlignment])}_setRequired(M,w){w?M.setAttribute("required",!0):M.removeAttribute("required"),M.setAttribute("aria-required",w)}}class b extends u{constructor(M){const w=M.renderForms||!M.data.hasAppearance&&!!M.data.fieldValue;super(M,{isRenderable:w})}setPropertyOnSiblings(M,w,z,dt){const ut=this.annotationStorage;for(const yt of this._getElementsByName(M.name,M.id))yt.domElement&&(yt.domElement[w]=z),ut.setValue(yt.id,{[dt]:z})}render(){var dt,ut;const M=this.annotationStorage,w=this.data.id;this.container.classList.add("textWidgetAnnotation");let z=null;if(this.renderForms){const yt=M.getValue(w,{value:this.data.fieldValue});let St=yt.value||"";const Lt=M.getValue(w,{charLimit:this.data.maxLen}).charLimit;Lt&&St.length>Lt&&(St=St.slice(0,Lt));let Ft=yt.formattedValue||((dt=this.data.textContent)==null?void 0:dt.join(`
`))||null;Ft&&this.data.comb&&(Ft=Ft.replaceAll(/\s+/g,""));const Pt={userValue:St,formattedValue:Ft,lastCommittedValue:null,commitKey:1,focused:!1};this.data.multiLine?(z=document.createElement("textarea"),z.textContent=Ft??St,this.data.doNotScroll&&(z.style.overflowY="hidden")):(z=document.createElement("input"),z.type="text",z.setAttribute("value",Ft??St),this.data.doNotScroll&&(z.style.overflowX="hidden")),this.data.hasOwnCanvas&&(z.hidden=!0),D.add(z),z.setAttribute("data-element-id",w),z.disabled=this.data.readOnly,z.name=this.data.fieldName,z.tabIndex=x,this._setRequired(z,this.data.required),Lt&&(z.maxLength=Lt),z.addEventListener("input",Mt=>{M.setValue(w,{value:Mt.target.value}),this.setPropertyOnSiblings(z,"value",Mt.target.value,"value"),Pt.formattedValue=null}),z.addEventListener("resetform",Mt=>{const It=this.data.defaultFieldValue??"";z.value=Pt.userValue=It,Pt.formattedValue=null});let Wt=Mt=>{const{formattedValue:It}=Pt;It!=null&&(Mt.target.value=It),Mt.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){z.addEventListener("focus",It=>{if(Pt.focused)return;const{target:Bt}=It;Pt.userValue&&(Bt.value=Pt.userValue),Pt.lastCommittedValue=Bt.value,Pt.commitKey=1,Pt.focused=!0}),z.addEventListener("updatefromsandbox",It=>{this.showElementAndHideCanvas(It.target);const Bt={value(Gt){Pt.userValue=Gt.detail.value??"",M.setValue(w,{value:Pt.userValue.toString()}),Gt.target.value=Pt.userValue},formattedValue(Gt){const{formattedValue:Xt}=Gt.detail;Pt.formattedValue=Xt,Xt!=null&&Gt.target!==document.activeElement&&(Gt.target.value=Xt),M.setValue(w,{formattedValue:Xt})},selRange(Gt){Gt.target.setSelectionRange(...Gt.detail.selRange)},charLimit:Gt=>{var Jt;const{charLimit:Xt}=Gt.detail,{target:Yt}=Gt;if(Xt===0){Yt.removeAttribute("maxLength");return}Yt.setAttribute("maxLength",Xt);let Qt=Pt.userValue;!Qt||Qt.length<=Xt||(Qt=Qt.slice(0,Xt),Yt.value=Pt.userValue=Qt,M.setValue(w,{value:Qt}),(Jt=this.linkService.eventBus)==null||Jt.dispatch("dispatcheventinsandbox",{source:this,detail:{id:w,name:"Keystroke",value:Qt,willCommit:!0,commitKey:1,selStart:Yt.selectionStart,selEnd:Yt.selectionEnd}}))}};this._dispatchEventFromSandbox(Bt,It)}),z.addEventListener("keydown",It=>{var Xt;Pt.commitKey=1;let Bt=-1;if(It.key==="Escape"?Bt=0:It.key==="Enter"&&!this.data.multiLine?Bt=2:It.key==="Tab"&&(Pt.commitKey=3),Bt===-1)return;const{value:Gt}=It.target;Pt.lastCommittedValue!==Gt&&(Pt.lastCommittedValue=Gt,Pt.userValue=Gt,(Xt=this.linkService.eventBus)==null||Xt.dispatch("dispatcheventinsandbox",{source:this,detail:{id:w,name:"Keystroke",value:Gt,willCommit:!0,commitKey:Bt,selStart:It.target.selectionStart,selEnd:It.target.selectionEnd}}))});const Mt=Wt;Wt=null,z.addEventListener("blur",It=>{var Gt;if(!Pt.focused||!It.relatedTarget)return;Pt.focused=!1;const{value:Bt}=It.target;Pt.userValue=Bt,Pt.lastCommittedValue!==Bt&&((Gt=this.linkService.eventBus)==null||Gt.dispatch("dispatcheventinsandbox",{source:this,detail:{id:w,name:"Keystroke",value:Bt,willCommit:!0,commitKey:Pt.commitKey,selStart:It.target.selectionStart,selEnd:It.target.selectionEnd}})),Mt(It)}),(ut=this.data.actions)!=null&&ut.Keystroke&&z.addEventListener("beforeinput",It=>{var ne;Pt.lastCommittedValue=null;const{data:Bt,target:Gt}=It,{value:Xt,selectionStart:Yt,selectionEnd:Qt}=Gt;let Jt=Yt,te=Qt;switch(It.inputType){case"deleteWordBackward":{const se=Xt.substring(0,Yt).match(/\w*[^\w]*$/);se&&(Jt-=se[0].length);break}case"deleteWordForward":{const se=Xt.substring(Yt).match(/^[^\w]*\w*/);se&&(te+=se[0].length);break}case"deleteContentBackward":Yt===Qt&&(Jt-=1);break;case"deleteContentForward":Yt===Qt&&(te+=1);break}It.preventDefault(),(ne=this.linkService.eventBus)==null||ne.dispatch("dispatcheventinsandbox",{source:this,detail:{id:w,name:"Keystroke",value:Xt,change:Bt||"",willCommit:!1,selStart:Jt,selEnd:te}})}),this._setEventListeners(z,Pt,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],It=>It.target.value)}if(Wt&&z.addEventListener("blur",Wt),this.data.comb){const It=(this.data.rect[2]-this.data.rect[0])/Lt;z.classList.add("comb"),z.style.letterSpacing=`calc(${It}px * var(--scale-factor) - 1ch)`}}else z=document.createElement("div"),z.textContent=this.data.fieldValue,z.style.verticalAlign="middle",z.style.display="table-cell";return this._setTextStyle(z),this._setBackgroundColor(z),this._setDefaultPropertiesFromJS(z),this.container.append(z),this.container}}class C extends u{constructor(M){super(M,{isRenderable:!!M.data.hasOwnCanvas})}}class _ extends u{constructor(M){super(M,{isRenderable:M.renderForms})}render(){const M=this.annotationStorage,w=this.data,z=w.id;let dt=M.getValue(z,{value:w.exportValue===w.fieldValue}).value;typeof dt=="string"&&(dt=dt!=="Off",M.setValue(z,{value:dt})),this.container.classList.add("buttonWidgetAnnotation","checkBox");const ut=document.createElement("input");return D.add(ut),ut.setAttribute("data-element-id",z),ut.disabled=w.readOnly,this._setRequired(ut,this.data.required),ut.type="checkbox",ut.name=w.fieldName,dt&&ut.setAttribute("checked",!0),ut.setAttribute("exportValue",w.exportValue),ut.tabIndex=x,ut.addEventListener("change",yt=>{const{name:St,checked:Lt}=yt.target;for(const Ft of this._getElementsByName(St,z)){const Pt=Lt&&Ft.exportValue===w.exportValue;Ft.domElement&&(Ft.domElement.checked=Pt),M.setValue(Ft.id,{value:Pt})}M.setValue(z,{value:Lt})}),ut.addEventListener("resetform",yt=>{const St=w.defaultFieldValue||"Off";yt.target.checked=St===w.exportValue}),this.enableScripting&&this.hasJSActions&&(ut.addEventListener("updatefromsandbox",yt=>{const St={value(Lt){Lt.target.checked=Lt.detail.value!=="Off",M.setValue(z,{value:Lt.target.checked})}};this._dispatchEventFromSandbox(St,yt)}),this._setEventListeners(ut,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],yt=>yt.target.checked)),this._setBackgroundColor(ut),this._setDefaultPropertiesFromJS(ut),this.container.append(ut),this.container}}class i extends u{constructor(M){super(M,{isRenderable:M.renderForms})}render(){this.container.classList.add("buttonWidgetAnnotation","radioButton");const M=this.annotationStorage,w=this.data,z=w.id;let dt=M.getValue(z,{value:w.fieldValue===w.buttonValue}).value;typeof dt=="string"&&(dt=dt!==w.buttonValue,M.setValue(z,{value:dt}));const ut=document.createElement("input");if(D.add(ut),ut.setAttribute("data-element-id",z),ut.disabled=w.readOnly,this._setRequired(ut,this.data.required),ut.type="radio",ut.name=w.fieldName,dt&&ut.setAttribute("checked",!0),ut.tabIndex=x,ut.addEventListener("change",yt=>{const{name:St,checked:Lt}=yt.target;for(const Ft of this._getElementsByName(St,z))M.setValue(Ft.id,{value:!1});M.setValue(z,{value:Lt})}),ut.addEventListener("resetform",yt=>{const St=w.defaultFieldValue;yt.target.checked=St!=null&&St===w.buttonValue}),this.enableScripting&&this.hasJSActions){const yt=w.buttonValue;ut.addEventListener("updatefromsandbox",St=>{const Lt={value:Ft=>{const Pt=yt===Ft.detail.value;for(const Wt of this._getElementsByName(Ft.target.name)){const Mt=Pt&&Wt.id===z;Wt.domElement&&(Wt.domElement.checked=Mt),M.setValue(Wt.id,{value:Mt})}}};this._dispatchEventFromSandbox(Lt,St)}),this._setEventListeners(ut,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],St=>St.target.checked)}return this._setBackgroundColor(ut),this._setDefaultPropertiesFromJS(ut),this.container.append(ut),this.container}}class c extends E{constructor(M){super(M,{ignoreBorder:M.data.hasAppearance})}render(){const M=super.render();M.classList.add("buttonWidgetAnnotation","pushButton"),this.data.alternativeText&&(M.title=this.data.alternativeText);const w=M.lastChild;return this.enableScripting&&this.hasJSActions&&w&&(this._setDefaultPropertiesFromJS(w),w.addEventListener("updatefromsandbox",z=>{this._dispatchEventFromSandbox({},z)})),M}}class o extends u{constructor(M){super(M,{isRenderable:M.renderForms})}render(){this.container.classList.add("choiceWidgetAnnotation");const M=this.annotationStorage,w=this.data.id,z=M.getValue(w,{value:this.data.fieldValue}),dt=document.createElement("select");D.add(dt),dt.setAttribute("data-element-id",w),dt.disabled=this.data.readOnly,this._setRequired(dt,this.data.required),dt.name=this.data.fieldName,dt.tabIndex=x;let ut=this.data.combo&&this.data.options.length>0;this.data.combo||(dt.size=this.data.options.length,this.data.multiSelect&&(dt.multiple=!0)),dt.addEventListener("resetform",Pt=>{const Wt=this.data.defaultFieldValue;for(const Mt of dt.options)Mt.selected=Mt.value===Wt});for(const Pt of this.data.options){const Wt=document.createElement("option");Wt.textContent=Pt.displayValue,Wt.value=Pt.exportValue,z.value.includes(Pt.exportValue)&&(Wt.setAttribute("selected",!0),ut=!1),dt.append(Wt)}let yt=null;if(ut){const Pt=document.createElement("option");Pt.value=" ",Pt.setAttribute("hidden",!0),Pt.setAttribute("selected",!0),dt.prepend(Pt),yt=()=>{Pt.remove(),dt.removeEventListener("input",yt),yt=null},dt.addEventListener("input",yt)}const St=Pt=>{const Wt=Pt?"value":"textContent",{options:Mt,multiple:It}=dt;return It?Array.prototype.filter.call(Mt,Bt=>Bt.selected).map(Bt=>Bt[Wt]):Mt.selectedIndex===-1?null:Mt[Mt.selectedIndex][Wt]};let Lt=St(!1);const Ft=Pt=>{const Wt=Pt.target.options;return Array.prototype.map.call(Wt,Mt=>({displayValue:Mt.textContent,exportValue:Mt.value}))};return this.enableScripting&&this.hasJSActions?(dt.addEventListener("updatefromsandbox",Pt=>{const Wt={value(Mt){yt==null||yt();const It=Mt.detail.value,Bt=new Set(Array.isArray(It)?It:[It]);for(const Gt of dt.options)Gt.selected=Bt.has(Gt.value);M.setValue(w,{value:St(!0)}),Lt=St(!1)},multipleSelection(Mt){dt.multiple=!0},remove(Mt){const It=dt.options,Bt=Mt.detail.remove;It[Bt].selected=!1,dt.remove(Bt),It.length>0&&Array.prototype.findIndex.call(It,Xt=>Xt.selected)===-1&&(It[0].selected=!0),M.setValue(w,{value:St(!0),items:Ft(Mt)}),Lt=St(!1)},clear(Mt){for(;dt.length!==0;)dt.remove(0);M.setValue(w,{value:null,items:[]}),Lt=St(!1)},insert(Mt){const{index:It,displayValue:Bt,exportValue:Gt}=Mt.detail.insert,Xt=dt.children[It],Yt=document.createElement("option");Yt.textContent=Bt,Yt.value=Gt,Xt?Xt.before(Yt):dt.append(Yt),M.setValue(w,{value:St(!0),items:Ft(Mt)}),Lt=St(!1)},items(Mt){const{items:It}=Mt.detail;for(;dt.length!==0;)dt.remove(0);for(const Bt of It){const{displayValue:Gt,exportValue:Xt}=Bt,Yt=document.createElement("option");Yt.textContent=Gt,Yt.value=Xt,dt.append(Yt)}dt.options.length>0&&(dt.options[0].selected=!0),M.setValue(w,{value:St(!0),items:Ft(Mt)}),Lt=St(!1)},indices(Mt){const It=new Set(Mt.detail.indices);for(const Bt of Mt.target.options)Bt.selected=It.has(Bt.index);M.setValue(w,{value:St(!0)}),Lt=St(!1)},editable(Mt){Mt.target.disabled=!Mt.detail.editable}};this._dispatchEventFromSandbox(Wt,Pt)}),dt.addEventListener("input",Pt=>{var Mt;const Wt=St(!0);M.setValue(w,{value:Wt}),Pt.preventDefault(),(Mt=this.linkService.eventBus)==null||Mt.dispatch("dispatcheventinsandbox",{source:this,detail:{id:w,name:"Keystroke",value:Lt,changeEx:Wt,willCommit:!1,commitKey:1,keyDown:!1}})}),this._setEventListeners(dt,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],Pt=>Pt.target.value)):dt.addEventListener("input",function(Pt){M.setValue(w,{value:St(!0)})}),this.data.combo&&this._setTextStyle(dt),this._setBackgroundColor(dt),this._setDefaultPropertiesFromJS(dt),this.container.append(dt),this.container}}class l extends m{constructor(M){const{data:w,elements:z}=M;super(M,{isRenderable:m._hasPopupData(w)}),this.elements=z}render(){this.container.classList.add("popupAnnotation");const M=new h({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),w=[];for(const z of this.elements)z.popup=M,w.push(z.data.id),z.addHighlightArea();return this.container.setAttribute("aria-controls",w.map(z=>`${n.AnnotationPrefix}${z}`).join(",")),this.container}}class h{constructor({container:M,color:w,elements:z,titleObj:dt,modificationDate:ut,contentsObj:yt,richText:St,parent:Lt,rect:Ft,parentRect:Pt,open:Wt}){rt(this,xt);rt(this,nt,null);rt(this,ct,K(this,xt,Mn).bind(this));rt(this,ft,K(this,xt,nn).bind(this));rt(this,bt,K(this,xt,en).bind(this));rt(this,mt,K(this,xt,we).bind(this));rt(this,_t,null);rt(this,J,null);rt(this,Q,null);rt(this,A,null);rt(this,j,null);rt(this,Y,null);rt(this,st,!1);rt(this,pt,null);rt(this,Et,null);rt(this,At,null);rt(this,tt,null);rt(this,wt,!1);var It;ot(this,J,M),ot(this,tt,dt),ot(this,Q,yt),ot(this,At,St),ot(this,j,Lt),ot(this,_t,w),ot(this,Et,Ft),ot(this,Y,Pt),ot(this,A,z);const Mt=d.PDFDateString.toDateObject(ut);Mt&&ot(this,nt,Lt.l10n.get("annotation_date_string",{date:Mt.toLocaleDateString(),time:Mt.toLocaleTimeString()})),this.trigger=z.flatMap(Bt=>Bt.getElementsToTriggerPopup());for(const Bt of this.trigger)Bt.addEventListener("click",t(this,mt)),Bt.addEventListener("mouseenter",t(this,bt)),Bt.addEventListener("mouseleave",t(this,ft)),Bt.classList.add("popupTriggerArea");for(const Bt of z)(It=Bt.container)==null||It.addEventListener("keydown",t(this,ct));t(this,J).hidden=!0,Wt&&K(this,xt,we).call(this)}render(){if(t(this,pt))return;const{page:{view:M},viewport:{rawDims:{pageWidth:w,pageHeight:z,pageX:dt,pageY:ut}}}=t(this,j),yt=ot(this,pt,document.createElement("div"));if(yt.className="popup",t(this,_t)){const Jt=yt.style.outlineColor=n.Util.makeHexColor(...t(this,_t));CSS.supports("background-color","color-mix(in srgb, red 30%, white)")?yt.style.backgroundColor=`color-mix(in srgb, ${Jt} 30%, white)`:yt.style.backgroundColor=n.Util.makeHexColor(...t(this,_t).map(ne=>Math.floor(.7*(255-ne)+ne)))}const St=document.createElement("span");St.className="header";const Lt=document.createElement("h1");if(St.append(Lt),{dir:Lt.dir,str:Lt.textContent}=t(this,tt),yt.append(St),t(this,nt)){const Jt=document.createElement("span");Jt.classList.add("popupDate"),t(this,nt).then(te=>{Jt.textContent=te}),St.append(Jt)}const Ft=t(this,Q),Pt=t(this,At);if(Pt!=null&&Pt.str&&(!(Ft!=null&&Ft.str)||Ft.str===Pt.str))P.XfaLayer.render({xfaHtml:Pt.html,intent:"richText",div:yt}),yt.lastChild.classList.add("richText","popupContent");else{const Jt=this._formatContents(Ft);yt.append(Jt)}let Wt=!!t(this,Y),Mt=Wt?t(this,Y):t(this,Et);for(const Jt of t(this,A))if(!Mt||n.Util.intersect(Jt.data.rect,Mt)!==null){Mt=Jt.data.rect,Wt=!0;break}const It=n.Util.normalizeRect([Mt[0],M[3]-Mt[1]+M[1],Mt[2],M[3]-Mt[3]+M[1]]),Gt=Wt?Mt[2]-Mt[0]+5:0,Xt=It[0]+Gt,Yt=It[1],{style:Qt}=t(this,J);Qt.left=`${100*(Xt-dt)/w}%`,Qt.top=`${100*(Yt-ut)/z}%`,t(this,J).append(yt)}_formatContents({str:M,dir:w}){const z=document.createElement("p");z.classList.add("popupContent"),z.dir=w;const dt=M.split(/(?:\r\n?|\n)/);for(let ut=0,yt=dt.length;ut<yt;++ut){const St=dt[ut];z.append(document.createTextNode(St)),ut<yt-1&&z.append(document.createElement("br"))}return z}forceHide(){ot(this,wt,this.isVisible),t(this,wt)&&(t(this,J).hidden=!0)}maybeShow(){t(this,wt)&&(ot(this,wt,!1),t(this,J).hidden=!1)}get isVisible(){return t(this,J).hidden===!1}}nt=new WeakMap,ct=new WeakMap,ft=new WeakMap,bt=new WeakMap,mt=new WeakMap,_t=new WeakMap,J=new WeakMap,Q=new WeakMap,A=new WeakMap,j=new WeakMap,Y=new WeakMap,st=new WeakMap,pt=new WeakMap,Et=new WeakMap,At=new WeakMap,tt=new WeakMap,wt=new WeakMap,xt=new WeakSet,Mn=function(M){M.altKey||M.shiftKey||M.ctrlKey||M.metaKey||(M.key==="Enter"||M.key==="Escape"&&t(this,st))&&K(this,xt,we).call(this)},we=function(){ot(this,st,!t(this,st)),t(this,st)?(K(this,xt,en).call(this),t(this,J).addEventListener("click",t(this,mt)),t(this,J).addEventListener("keydown",t(this,ct))):(K(this,xt,nn).call(this),t(this,J).removeEventListener("click",t(this,mt)),t(this,J).removeEventListener("keydown",t(this,ct)))},en=function(){t(this,pt)||this.render(),this.isVisible?t(this,st)&&t(this,J).classList.add("focused"):(t(this,J).hidden=!1,t(this,J).style.zIndex=parseInt(t(this,J).style.zIndex)+1e3)},nn=function(){t(this,J).classList.remove("focused"),!(t(this,st)||!this.isVisible)&&(t(this,J).hidden=!0,t(this,J).style.zIndex=parseInt(t(this,J).style.zIndex)-1e3)};class F extends m{constructor(M){super(M,{isRenderable:!0,ignoreBorder:!0}),this.textContent=M.data.textContent,this.textPosition=M.data.textPosition,this.annotationEditorType=n.AnnotationEditorType.FREETEXT}render(){if(this.container.classList.add("freeTextAnnotation"),this.textContent){const M=document.createElement("div");M.classList.add("annotationTextContent"),M.setAttribute("role","comment");for(const w of this.textContent){const z=document.createElement("span");z.textContent=w,M.append(z)}this.container.append(M)}return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}e.FreeTextAnnotationElement=F;class r extends m{constructor(w){super(w,{isRenderable:!0,ignoreBorder:!0});rt(this,Ot,null)}render(){this.container.classList.add("lineAnnotation");const w=this.data,{width:z,height:dt}=I(w.rect),ut=this.svgFactory.create(z,dt,!0),yt=ot(this,Ot,this.svgFactory.createElement("svg:line"));return yt.setAttribute("x1",w.rect[2]-w.lineCoordinates[0]),yt.setAttribute("y1",w.rect[3]-w.lineCoordinates[1]),yt.setAttribute("x2",w.rect[2]-w.lineCoordinates[2]),yt.setAttribute("y2",w.rect[3]-w.lineCoordinates[3]),yt.setAttribute("stroke-width",w.borderStyle.width||1),yt.setAttribute("stroke","transparent"),yt.setAttribute("fill","transparent"),ut.append(yt),this.container.append(ut),!w.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return t(this,Ot)}addHighlightArea(){this.container.classList.add("highlightArea")}}Ot=new WeakMap;class v extends m{constructor(w){super(w,{isRenderable:!0,ignoreBorder:!0});rt(this,Ht,null)}render(){this.container.classList.add("squareAnnotation");const w=this.data,{width:z,height:dt}=I(w.rect),ut=this.svgFactory.create(z,dt,!0),yt=w.borderStyle.width,St=ot(this,Ht,this.svgFactory.createElement("svg:rect"));return St.setAttribute("x",yt/2),St.setAttribute("y",yt/2),St.setAttribute("width",z-yt),St.setAttribute("height",dt-yt),St.setAttribute("stroke-width",yt||1),St.setAttribute("stroke","transparent"),St.setAttribute("fill","transparent"),ut.append(St),this.container.append(ut),!w.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return t(this,Ht)}addHighlightArea(){this.container.classList.add("highlightArea")}}Ht=new WeakMap;class k extends m{constructor(w){super(w,{isRenderable:!0,ignoreBorder:!0});rt(this,qt,null)}render(){this.container.classList.add("circleAnnotation");const w=this.data,{width:z,height:dt}=I(w.rect),ut=this.svgFactory.create(z,dt,!0),yt=w.borderStyle.width,St=ot(this,qt,this.svgFactory.createElement("svg:ellipse"));return St.setAttribute("cx",z/2),St.setAttribute("cy",dt/2),St.setAttribute("rx",z/2-yt/2),St.setAttribute("ry",dt/2-yt/2),St.setAttribute("stroke-width",yt||1),St.setAttribute("stroke","transparent"),St.setAttribute("fill","transparent"),ut.append(St),this.container.append(ut),!w.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return t(this,qt)}addHighlightArea(){this.container.classList.add("highlightArea")}}qt=new WeakMap;class Z extends m{constructor(w){super(w,{isRenderable:!0,ignoreBorder:!0});rt(this,vt,null);this.containerClassName="polylineAnnotation",this.svgElementName="svg:polyline"}render(){this.container.classList.add(this.containerClassName);const w=this.data,{width:z,height:dt}=I(w.rect),ut=this.svgFactory.create(z,dt,!0);let yt=[];for(const Lt of w.vertices){const Ft=Lt.x-w.rect[0],Pt=w.rect[3]-Lt.y;yt.push(Ft+","+Pt)}yt=yt.join(" ");const St=ot(this,vt,this.svgFactory.createElement(this.svgElementName));return St.setAttribute("points",yt),St.setAttribute("stroke-width",w.borderStyle.width||1),St.setAttribute("stroke","transparent"),St.setAttribute("fill","transparent"),ut.append(St),this.container.append(ut),!w.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return t(this,vt)}addHighlightArea(){this.container.classList.add("highlightArea")}}vt=new WeakMap;class $ extends Z{constructor(M){super(M),this.containerClassName="polygonAnnotation",this.svgElementName="svg:polygon"}}class q extends m{constructor(M){super(M,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("caretAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}class G extends m{constructor(w){super(w,{isRenderable:!0,ignoreBorder:!0});rt(this,at,[]);this.containerClassName="inkAnnotation",this.svgElementName="svg:polyline",this.annotationEditorType=n.AnnotationEditorType.INK}render(){this.container.classList.add(this.containerClassName);const w=this.data,{width:z,height:dt}=I(w.rect),ut=this.svgFactory.create(z,dt,!0);for(const yt of w.inkLists){let St=[];for(const Ft of yt){const Pt=Ft.x-w.rect[0],Wt=w.rect[3]-Ft.y;St.push(`${Pt},${Wt}`)}St=St.join(" ");const Lt=this.svgFactory.createElement(this.svgElementName);t(this,at).push(Lt),Lt.setAttribute("points",St),Lt.setAttribute("stroke-width",w.borderStyle.width||1),Lt.setAttribute("stroke","transparent"),Lt.setAttribute("fill","transparent"),!w.popupRef&&this.hasPopupData&&this._createPopup(),ut.append(Lt)}return this.container.append(ut),this.container}getElementsToTriggerPopup(){return t(this,at)}addHighlightArea(){this.container.classList.add("highlightArea")}}at=new WeakMap,e.InkAnnotationElement=G;class it extends m{constructor(M){super(M,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("highlightAnnotation"),this.container}}class R extends m{constructor(M){super(M,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("underlineAnnotation"),this.container}}class V extends m{constructor(M){super(M,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("squigglyAnnotation"),this.container}}class et extends m{constructor(M){super(M,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("strikeoutAnnotation"),this.container}}class S extends m{constructor(M){super(M,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("stampAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}e.StampAnnotationElement=S;class s extends m{constructor(w){var ut;super(w,{isRenderable:!0});rt(this,Rt);rt(this,lt,null);const{filename:z,content:dt}=this.data.file;this.filename=(0,d.getFilenameFromUrl)(z,!0),this.content=dt,(ut=this.linkService.eventBus)==null||ut.dispatch("fileattachmentannotation",{source:this,filename:z,content:dt})}render(){this.container.classList.add("fileAttachmentAnnotation");const{container:w,data:z}=this;let dt;z.hasAppearance||z.fillAlpha===0?dt=document.createElement("div"):(dt=document.createElement("img"),dt.src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(z.name)?"paperclip":"pushpin"}.svg`,z.fillAlpha&&z.fillAlpha<1&&(dt.style=`filter: opacity(${Math.round(z.fillAlpha*100)}%);`)),dt.addEventListener("dblclick",K(this,Rt,sn).bind(this)),ot(this,lt,dt);const{isMac:ut}=n.FeatureTest.platform;return w.addEventListener("keydown",yt=>{yt.key==="Enter"&&(ut?yt.metaKey:yt.ctrlKey)&&K(this,Rt,sn).call(this)}),!z.popupRef&&this.hasPopupData?this._createPopup():dt.classList.add("popupTriggerArea"),w.append(dt),w}getElementsToTriggerPopup(){return t(this,lt)}addHighlightArea(){this.container.classList.add("highlightArea")}}lt=new WeakMap,Rt=new WeakSet,sn=function(){var w;(w=this.downloadManager)==null||w.openOrDownloadData(this.container,this.content,this.filename)};class a{constructor({div:M,accessibilityManager:w,annotationCanvasMap:z,l10n:dt,page:ut,viewport:yt}){rt(this,Tt);rt(this,$t,null);rt(this,U,null);rt(this,gt,new Map);this.div=M,ot(this,$t,w),ot(this,U,z),this.l10n=dt,this.page=ut,this.viewport=yt,this.zIndex=0,this.l10n||(this.l10n=ht.NullL10n)}async render(M){const{annotations:w}=M,z=this.div;(0,d.setLayerDimensions)(z,this.viewport);const dt=new Map,ut={data:null,layer:z,linkService:M.linkService,downloadManager:M.downloadManager,imageResourcesPath:M.imageResourcesPath||"",renderForms:M.renderForms!==!1,svgFactory:new d.DOMSVGFactory,annotationStorage:M.annotationStorage||new H.AnnotationStorage,enableScripting:M.enableScripting===!0,hasJSActions:M.hasJSActions,fieldObjects:M.fieldObjects,parent:this,elements:null};for(const yt of w){if(yt.noHTML)continue;const St=yt.annotationType===n.AnnotationType.POPUP;if(St){const Pt=dt.get(yt.id);if(!Pt)continue;ut.elements=Pt}else{const{width:Pt,height:Wt}=I(yt.rect);if(Pt<=0||Wt<=0)continue}ut.data=yt;const Lt=y.create(ut);if(!Lt.isRenderable)continue;if(!St&&yt.popupRef){const Pt=dt.get(yt.popupRef);Pt?Pt.push(Lt):dt.set(yt.popupRef,[Lt])}Lt.annotationEditorType>0&&t(this,gt).set(Lt.data.id,Lt);const Ft=Lt.render();yt.hidden&&(Ft.style.visibility="hidden"),K(this,Tt,Dn).call(this,Ft,yt.id)}K(this,Tt,rn).call(this),await this.l10n.translate(z)}update({viewport:M}){const w=this.div;this.viewport=M,(0,d.setLayerDimensions)(w,{rotation:M.rotation}),K(this,Tt,rn).call(this),w.hidden=!1}getEditableAnnotations(){return Array.from(t(this,gt).values())}getEditableAnnotation(M){return t(this,gt).get(M)}}$t=new WeakMap,U=new WeakMap,gt=new WeakMap,Tt=new WeakSet,Dn=function(M,w){var dt;const z=M.firstChild||M;z.id=`${n.AnnotationPrefix}${w}`,this.div.append(M),(dt=t(this,$t))==null||dt.moveElementInDOM(this.div,M,z,!1)},rn=function(){if(!t(this,U))return;const M=this.div;for(const[w,z]of t(this,U)){const dt=M.querySelector(`[data-annotation-id="${w}"]`);if(!dt)continue;const{firstChild:ut}=dt;ut?ut.nodeName==="CANVAS"?ut.replaceWith(z):ut.before(z):dt.append(z)}t(this,U).clear()},e.AnnotationLayer=a},(T,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ColorConverters=void 0;function B(H){return Math.floor(Math.max(0,Math.min(1,H))*255).toString(16).padStart(2,"0")}function n(H){return Math.max(0,Math.min(255,255*H))}class d{static CMYK_G([W,ht,P,x]){return["G",1-Math.min(1,.3*W+.59*P+.11*ht+x)]}static G_CMYK([W]){return["CMYK",0,0,0,1-W]}static G_RGB([W]){return["RGB",W,W,W]}static G_rgb([W]){return W=n(W),[W,W,W]}static G_HTML([W]){const ht=B(W);return`#${ht}${ht}${ht}`}static RGB_G([W,ht,P]){return["G",.3*W+.59*ht+.11*P]}static RGB_rgb(W){return W.map(n)}static RGB_HTML(W){return`#${W.map(B).join("")}`}static T_HTML(){return"#00000000"}static T_rgb(){return[null]}static CMYK_RGB([W,ht,P,x]){return["RGB",1-Math.min(1,W+x),1-Math.min(1,P+x),1-Math.min(1,ht+x)]}static CMYK_rgb([W,ht,P,x]){return[n(1-Math.min(1,W+x)),n(1-Math.min(1,P+x)),n(1-Math.min(1,ht+x))]}static CMYK_HTML(W){const ht=this.CMYK_RGB(W).slice(1);return this.RGB_HTML(ht)}static RGB_CMYK([W,ht,P]){const x=1-W,f=1-ht,D=1-P,I=Math.min(x,f,D);return["CMYK",x,f,D,I]}}e.ColorConverters=d},(T,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.NullL10n=void 0,e.getL10nFallback=n;const B={of_pages:"of {{pagesCount}}",page_of_pages:"({{pageNumber}} of {{pagesCount}})",document_properties_kb:"{{size_kb}} KB ({{size_b}} bytes)",document_properties_mb:"{{size_mb}} MB ({{size_b}} bytes)",document_properties_date_string:"{{date}}, {{time}}",document_properties_page_size_unit_inches:"in",document_properties_page_size_unit_millimeters:"mm",document_properties_page_size_orientation_portrait:"portrait",document_properties_page_size_orientation_landscape:"landscape",document_properties_page_size_name_a3:"A3",document_properties_page_size_name_a4:"A4",document_properties_page_size_name_letter:"Letter",document_properties_page_size_name_legal:"Legal",document_properties_page_size_dimension_string:"{{width}} × {{height}} {{unit}} ({{orientation}})",document_properties_page_size_dimension_name_string:"{{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})",document_properties_linearized_yes:"Yes",document_properties_linearized_no:"No",additional_layers:"Additional Layers",page_landmark:"Page {{page}}",thumb_page_title:"Page {{page}}",thumb_page_canvas:"Thumbnail of Page {{page}}",find_reached_top:"Reached top of document, continued from bottom",find_reached_bottom:"Reached end of document, continued from top","find_match_count[one]":"{{current}} of {{total}} match","find_match_count[other]":"{{current}} of {{total}} matches","find_match_count_limit[one]":"More than {{limit}} match","find_match_count_limit[other]":"More than {{limit}} matches",find_not_found:"Phrase not found",page_scale_width:"Page Width",page_scale_fit:"Page Fit",page_scale_auto:"Automatic Zoom",page_scale_actual:"Actual Size",page_scale_percent:"{{scale}}%",loading_error:"An error occurred while loading the PDF.",invalid_file_error:"Invalid or corrupted PDF file.",missing_file_error:"Missing PDF file.",unexpected_response_error:"Unexpected server response.",rendering_error:"An error occurred while rendering the page.",annotation_date_string:"{{date}}, {{time}}",printing_not_supported:"Warning: Printing is not fully supported by this browser.",printing_not_ready:"Warning: The PDF is not fully loaded for printing.",web_fonts_disabled:"Web fonts are disabled: unable to use embedded PDF fonts.",free_text2_default_content:"Start typing…",editor_free_text2_aria_label:"Text Editor",editor_ink2_aria_label:"Draw Editor",editor_ink_canvas_aria_label:"User-created image",editor_alt_text_button_label:"Alt text",editor_alt_text_edit_button_label:"Edit alt text",editor_alt_text_decorative_tooltip:"Marked as decorative"};B.print_progress_percent="{{progress}}%";function n(W,ht){switch(W){case"find_match_count":W=`find_match_count[${ht.total===1?"one":"other"}]`;break;case"find_match_count_limit":W=`find_match_count_limit[${ht.limit===1?"one":"other"}]`;break}return B[W]||""}function d(W,ht){return ht?W.replaceAll(/\{\{\s*(\w+)\s*\}\}/g,(P,x)=>x in ht?ht[x]:"{{"+x+"}}"):W}const H={async getLanguage(){return"en-us"},async getDirection(){return"ltr"},async get(W,ht=null,P=n(W,ht)){return d(P,ht)},async translate(W){}};e.NullL10n=H},(T,e,B)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.XfaLayer=void 0;var n=B(25);class d{static setupStorage(W,ht,P,x,f){const D=x.getValue(ht,{value:null});switch(P.name){case"textarea":if(D.value!==null&&(W.textContent=D.value),f==="print")break;W.addEventListener("input",I=>{x.setValue(ht,{value:I.target.value})});break;case"input":if(P.attributes.type==="radio"||P.attributes.type==="checkbox"){if(D.value===P.attributes.xfaOn?W.setAttribute("checked",!0):D.value===P.attributes.xfaOff&&W.removeAttribute("checked"),f==="print")break;W.addEventListener("change",I=>{x.setValue(ht,{value:I.target.checked?I.target.getAttribute("xfaOn"):I.target.getAttribute("xfaOff")})})}else{if(D.value!==null&&W.setAttribute("value",D.value),f==="print")break;W.addEventListener("input",I=>{x.setValue(ht,{value:I.target.value})})}break;case"select":if(D.value!==null){W.setAttribute("value",D.value);for(const I of P.children)I.attributes.value===D.value?I.attributes.selected=!0:I.attributes.hasOwnProperty("selected")&&delete I.attributes.selected}W.addEventListener("input",I=>{const y=I.target.options,m=y.selectedIndex===-1?"":y[y.selectedIndex].value;x.setValue(ht,{value:m})});break}}static setAttributes({html:W,element:ht,storage:P=null,intent:x,linkService:f}){const{attributes:D}=ht,I=W instanceof HTMLAnchorElement;D.type==="radio"&&(D.name=`${D.name}-${x}`);for(const[y,m]of Object.entries(D))if(m!=null)switch(y){case"class":m.length&&W.setAttribute(y,m.join(" "));break;case"dataId":break;case"id":W.setAttribute("data-element-id",m);break;case"style":Object.assign(W.style,m);break;case"textContent":W.textContent=m;break;default:(!I||y!=="href"&&y!=="newWindow")&&W.setAttribute(y,m)}I&&f.addLinkAttributes(W,D.href,D.newWindow),P&&D.dataId&&this.setupStorage(W,D.dataId,ht,P)}static render(W){var E;const ht=W.annotationStorage,P=W.linkService,x=W.xfaHtml,f=W.intent||"display",D=document.createElement(x.name);x.attributes&&this.setAttributes({html:D,element:x,intent:f,linkService:P});const I=[[x,-1,D]],y=W.div;if(y.append(D),W.viewport){const p=`matrix(${W.viewport.transform.join(",")})`;y.style.transform=p}f!=="richText"&&y.setAttribute("class","xfaLayer xfaFont");const m=[];for(;I.length>0;){const[p,u,b]=I.at(-1);if(u+1===p.children.length){I.pop();continue}const C=p.children[++I.at(-1)[1]];if(C===null)continue;const{name:_}=C;if(_==="#text"){const c=document.createTextNode(C.value);m.push(c),b.append(c);continue}const i=(E=C==null?void 0:C.attributes)!=null&&E.xmlns?document.createElementNS(C.attributes.xmlns,_):document.createElement(_);if(b.append(i),C.attributes&&this.setAttributes({html:i,element:C,storage:ht,intent:f,linkService:P}),C.children&&C.children.length>0)I.push([C,-1,i]);else if(C.value){const c=document.createTextNode(C.value);n.XfaText.shouldBuildText(_)&&m.push(c),i.append(c)}}for(const p of y.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))p.setAttribute("readOnly",!0);return{textDivs:m}}static update(W){const ht=`matrix(${W.viewport.transform.join(",")})`;W.div.style.transform=ht,W.div.hidden=!1}}e.XfaLayer=d},(T,e,B)=>{var x,f,D,I,y,m,E,p,u,b,C,_,i,c,o,Ln,On,In,Nn,an,jn,on,Bn,Un,Wn,Hn,Gn,ee,ln,Te,Pe,le,cn,Ce,O,$n,hn,Vn,zn,dn,Re,ce;Object.defineProperty(e,"__esModule",{value:!0}),e.InkEditor=void 0;var n=B(1),d=B(4),H=B(29),W=B(6),ht=B(5);const _t=class _t extends d.AnnotationEditor{constructor(A){super({...A,name:"inkEditor"});rt(this,o);rt(this,x,0);rt(this,f,0);rt(this,D,this.canvasPointermove.bind(this));rt(this,I,this.canvasPointerleave.bind(this));rt(this,y,this.canvasPointerup.bind(this));rt(this,m,this.canvasPointerdown.bind(this));rt(this,E,new Path2D);rt(this,p,!1);rt(this,u,!1);rt(this,b,!1);rt(this,C,null);rt(this,_,0);rt(this,i,0);rt(this,c,null);this.color=A.color||null,this.thickness=A.thickness||null,this.opacity=A.opacity||null,this.paths=[],this.bezierPath2D=[],this.allRawPaths=[],this.currentPath=[],this.scaleFactor=1,this.translationX=this.translationY=0,this.x=0,this.y=0,this._willKeepAspectRatio=!0}static initialize(A){d.AnnotationEditor.initialize(A,{strings:["editor_ink_canvas_aria_label","editor_ink2_aria_label"]})}static updateDefaultParams(A,j){switch(A){case n.AnnotationEditorParamsType.INK_THICKNESS:_t._defaultThickness=j;break;case n.AnnotationEditorParamsType.INK_COLOR:_t._defaultColor=j;break;case n.AnnotationEditorParamsType.INK_OPACITY:_t._defaultOpacity=j/100;break}}updateParams(A,j){switch(A){case n.AnnotationEditorParamsType.INK_THICKNESS:K(this,o,Ln).call(this,j);break;case n.AnnotationEditorParamsType.INK_COLOR:K(this,o,On).call(this,j);break;case n.AnnotationEditorParamsType.INK_OPACITY:K(this,o,In).call(this,j);break}}static get defaultPropertiesToUpdate(){return[[n.AnnotationEditorParamsType.INK_THICKNESS,_t._defaultThickness],[n.AnnotationEditorParamsType.INK_COLOR,_t._defaultColor||d.AnnotationEditor._defaultLineColor],[n.AnnotationEditorParamsType.INK_OPACITY,Math.round(_t._defaultOpacity*100)]]}get propertiesToUpdate(){return[[n.AnnotationEditorParamsType.INK_THICKNESS,this.thickness||_t._defaultThickness],[n.AnnotationEditorParamsType.INK_COLOR,this.color||_t._defaultColor||d.AnnotationEditor._defaultLineColor],[n.AnnotationEditorParamsType.INK_OPACITY,Math.round(100*(this.opacity??_t._defaultOpacity))]]}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(this.canvas||(K(this,o,Te).call(this),K(this,o,Pe).call(this)),this.isAttachedToDOM||(this.parent.add(this),K(this,o,le).call(this)),K(this,o,ce).call(this)))}remove(){this.canvas!==null&&(this.isEmpty()||this.commit(),this.canvas.width=this.canvas.height=0,this.canvas.remove(),this.canvas=null,t(this,C).disconnect(),ot(this,C,null),super.remove())}setParent(A){!this.parent&&A?this._uiManager.removeShouldRescale(this):this.parent&&A===null&&this._uiManager.addShouldRescale(this),super.setParent(A)}onScaleChanging(){const[A,j]=this.parentDimensions,Y=this.width*A,st=this.height*j;this.setDimensions(Y,st)}enableEditMode(){t(this,p)||this.canvas===null||(super.enableEditMode(),this._isDraggable=!1,this.canvas.addEventListener("pointerdown",t(this,m)))}disableEditMode(){!this.isInEditMode()||this.canvas===null||(super.disableEditMode(),this._isDraggable=!this.isEmpty(),this.div.classList.remove("editing"),this.canvas.removeEventListener("pointerdown",t(this,m)))}onceAdded(){this._isDraggable=!this.isEmpty()}isEmpty(){return this.paths.length===0||this.paths.length===1&&this.paths[0].length===0}commit(){t(this,p)||(super.commit(),this.isEditing=!1,this.disableEditMode(),this.setInForeground(),ot(this,p,!0),this.div.classList.add("disabled"),K(this,o,ce).call(this,!0),this.makeResizable(),this.parent.addInkEditorIfNeeded(!0),this.moveInDOM(),this.div.focus({preventScroll:!0}))}focusin(A){this._focusEventsAllowed&&(super.focusin(A),this.enableEditMode())}canvasPointerdown(A){A.button!==0||!this.isInEditMode()||t(this,p)||(this.setInForeground(),A.preventDefault(),A.type!=="mouse"&&this.div.focus(),K(this,o,jn).call(this,A.offsetX,A.offsetY))}canvasPointermove(A){A.preventDefault(),K(this,o,on).call(this,A.offsetX,A.offsetY)}canvasPointerup(A){A.preventDefault(),K(this,o,ln).call(this,A)}canvasPointerleave(A){K(this,o,ln).call(this,A)}get isResizable(){return!this.isEmpty()&&t(this,p)}render(){if(this.div)return this.div;let A,j;this.width&&(A=this.x,j=this.y),super.render(),d.AnnotationEditor._l10nPromise.get("editor_ink2_aria_label").then(At=>{var tt;return(tt=this.div)==null?void 0:tt.setAttribute("aria-label",At)});const[Y,st,pt,Et]=K(this,o,Nn).call(this);if(this.setAt(Y,st,0,0),this.setDims(pt,Et),K(this,o,Te).call(this),this.width){const[At,tt]=this.parentDimensions;this.setAspectRatio(this.width*At,this.height*tt),this.setAt(A*At,j*tt,this.width*At,this.height*tt),ot(this,b,!0),K(this,o,le).call(this),this.setDims(this.width*At,this.height*tt),K(this,o,ee).call(this),this.div.classList.add("disabled")}else this.div.classList.add("editing"),this.enableEditMode();return K(this,o,Pe).call(this),this.div}setDimensions(A,j){const Y=Math.round(A),st=Math.round(j);if(t(this,_)===Y&&t(this,i)===st)return;ot(this,_,Y),ot(this,i,st),this.canvas.style.visibility="hidden";const[pt,Et]=this.parentDimensions;this.width=A/pt,this.height=j/Et,this.fixAndSetPosition(),t(this,p)&&K(this,o,cn).call(this,A,j),K(this,o,le).call(this),K(this,o,ee).call(this),this.canvas.style.visibility="visible",this.fixDims()}static deserialize(A,j,Y){var Ot,Ht,qt;if(A instanceof H.InkAnnotationElement)return null;const st=super.deserialize(A,j,Y);st.thickness=A.thickness,st.color=n.Util.makeHexColor(...A.color),st.opacity=A.opacity;const[pt,Et]=st.pageDimensions,At=st.width*pt,tt=st.height*Et,wt=st.parentScale,xt=A.thickness/2;ot(st,p,!0),ot(st,_,Math.round(At)),ot(st,i,Math.round(tt));const{paths:Ut,rect:jt,rotation:Vt}=A;for(let{bezier:vt}of Ut){vt=K(Ot=_t,O,Vn).call(Ot,vt,jt,Vt);const at=[];st.paths.push(at);let lt=wt*(vt[0]-xt),Rt=wt*(vt[1]-xt);for(let $t=2,U=vt.length;$t<U;$t+=6){const gt=wt*(vt[$t]-xt),Tt=wt*(vt[$t+1]-xt),Dt=wt*(vt[$t+2]-xt),Nt=wt*(vt[$t+3]-xt),Ct=wt*(vt[$t+4]-xt),M=wt*(vt[$t+5]-xt);at.push([[lt,Rt],[gt,Tt],[Dt,Nt],[Ct,M]]),lt=Ct,Rt=M}const zt=K(this,O,$n).call(this,at);st.bezierPath2D.push(zt)}const kt=K(Ht=st,o,dn).call(Ht);return ot(st,f,Math.max(d.AnnotationEditor.MIN_SIZE,kt[2]-kt[0])),ot(st,x,Math.max(d.AnnotationEditor.MIN_SIZE,kt[3]-kt[1])),K(qt=st,o,cn).call(qt,At,tt),st}serialize(){if(this.isEmpty())return null;const A=this.getRect(0,0),j=d.AnnotationEditor._colorManager.convert(this.ctx.strokeStyle);return{annotationType:n.AnnotationEditorType.INK,color:j,thickness:this.thickness,opacity:this.opacity,paths:K(this,o,zn).call(this,this.scaleFactor/this.parentScale,this.translationX,this.translationY,A),pageIndex:this.pageIndex,rect:A,rotation:this.rotation,structTreeParentId:this._structTreeParentId}}};x=new WeakMap,f=new WeakMap,D=new WeakMap,I=new WeakMap,y=new WeakMap,m=new WeakMap,E=new WeakMap,p=new WeakMap,u=new WeakMap,b=new WeakMap,C=new WeakMap,_=new WeakMap,i=new WeakMap,c=new WeakMap,o=new WeakSet,Ln=function(A){const j=this.thickness;this.addCommands({cmd:()=>{this.thickness=A,K(this,o,ce).call(this)},undo:()=>{this.thickness=j,K(this,o,ce).call(this)},mustExec:!0,type:n.AnnotationEditorParamsType.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0})},On=function(A){const j=this.color;this.addCommands({cmd:()=>{this.color=A,K(this,o,ee).call(this)},undo:()=>{this.color=j,K(this,o,ee).call(this)},mustExec:!0,type:n.AnnotationEditorParamsType.INK_COLOR,overwriteIfSameType:!0,keepUndo:!0})},In=function(A){A/=100;const j=this.opacity;this.addCommands({cmd:()=>{this.opacity=A,K(this,o,ee).call(this)},undo:()=>{this.opacity=j,K(this,o,ee).call(this)},mustExec:!0,type:n.AnnotationEditorParamsType.INK_OPACITY,overwriteIfSameType:!0,keepUndo:!0})},Nn=function(){const{parentRotation:A,parentDimensions:[j,Y]}=this;switch(A){case 90:return[0,Y,Y,j];case 180:return[j,Y,j,Y];case 270:return[j,0,Y,j];default:return[0,0,j,Y]}},an=function(){const{ctx:A,color:j,opacity:Y,thickness:st,parentScale:pt,scaleFactor:Et}=this;A.lineWidth=st*pt/Et,A.lineCap="round",A.lineJoin="round",A.miterLimit=10,A.strokeStyle=`${j}${(0,ht.opacityToHex)(Y)}`},jn=function(A,j){this.canvas.addEventListener("contextmenu",W.noContextMenu),this.canvas.addEventListener("pointerleave",t(this,I)),this.canvas.addEventListener("pointermove",t(this,D)),this.canvas.addEventListener("pointerup",t(this,y)),this.canvas.removeEventListener("pointerdown",t(this,m)),this.isEditing=!0,t(this,b)||(ot(this,b,!0),K(this,o,le).call(this),this.thickness||(this.thickness=_t._defaultThickness),this.color||(this.color=_t._defaultColor||d.AnnotationEditor._defaultLineColor),this.opacity??(this.opacity=_t._defaultOpacity)),this.currentPath.push([A,j]),ot(this,u,!1),K(this,o,an).call(this),ot(this,c,()=>{K(this,o,Wn).call(this),t(this,c)&&window.requestAnimationFrame(t(this,c))}),window.requestAnimationFrame(t(this,c))},on=function(A,j){const[Y,st]=this.currentPath.at(-1);if(this.currentPath.length>1&&A===Y&&j===st)return;const pt=this.currentPath;let Et=t(this,E);if(pt.push([A,j]),ot(this,u,!0),pt.length<=2){Et.moveTo(...pt[0]),Et.lineTo(A,j);return}pt.length===3&&(ot(this,E,Et=new Path2D),Et.moveTo(...pt[0])),K(this,o,Hn).call(this,Et,...pt.at(-3),...pt.at(-2),A,j)},Bn=function(){if(this.currentPath.length===0)return;const A=this.currentPath.at(-1);t(this,E).lineTo(...A)},Un=function(A,j){ot(this,c,null),A=Math.min(Math.max(A,0),this.canvas.width),j=Math.min(Math.max(j,0),this.canvas.height),K(this,o,on).call(this,A,j),K(this,o,Bn).call(this);let Y;if(this.currentPath.length!==1)Y=K(this,o,Gn).call(this);else{const tt=[A,j];Y=[[tt,tt.slice(),tt.slice(),tt]]}const st=t(this,E),pt=this.currentPath;this.currentPath=[],ot(this,E,new Path2D);const Et=()=>{this.allRawPaths.push(pt),this.paths.push(Y),this.bezierPath2D.push(st),this.rebuild()},At=()=>{this.allRawPaths.pop(),this.paths.pop(),this.bezierPath2D.pop(),this.paths.length===0?this.remove():(this.canvas||(K(this,o,Te).call(this),K(this,o,Pe).call(this)),K(this,o,ce).call(this))};this.addCommands({cmd:Et,undo:At,mustExec:!0})},Wn=function(){if(!t(this,u))return;ot(this,u,!1);const A=Math.ceil(this.thickness*this.parentScale),j=this.currentPath.slice(-3),Y=j.map(Et=>Et[0]),st=j.map(Et=>Et[1]);Math.min(...Y)-A,Math.max(...Y)+A,Math.min(...st)-A,Math.max(...st)+A;const{ctx:pt}=this;pt.save(),pt.clearRect(0,0,this.canvas.width,this.canvas.height);for(const Et of this.bezierPath2D)pt.stroke(Et);pt.stroke(t(this,E)),pt.restore()},Hn=function(A,j,Y,st,pt,Et,At){const tt=(j+st)/2,wt=(Y+pt)/2,xt=(st+Et)/2,Ut=(pt+At)/2;A.bezierCurveTo(tt+2*(st-tt)/3,wt+2*(pt-wt)/3,xt+2*(st-xt)/3,Ut+2*(pt-Ut)/3,xt,Ut)},Gn=function(){const A=this.currentPath;if(A.length<=2)return[[A[0],A[0],A.at(-1),A.at(-1)]];const j=[];let Y,[st,pt]=A[0];for(Y=1;Y<A.length-2;Y++){const[jt,Vt]=A[Y],[kt,Ot]=A[Y+1],Ht=(jt+kt)/2,qt=(Vt+Ot)/2,vt=[st+2*(jt-st)/3,pt+2*(Vt-pt)/3],at=[Ht+2*(jt-Ht)/3,qt+2*(Vt-qt)/3];j.push([[st,pt],vt,at,[Ht,qt]]),[st,pt]=[Ht,qt]}const[Et,At]=A[Y],[tt,wt]=A[Y+1],xt=[st+2*(Et-st)/3,pt+2*(At-pt)/3],Ut=[tt+2*(Et-tt)/3,wt+2*(At-wt)/3];return j.push([[st,pt],xt,Ut,[tt,wt]]),j},ee=function(){if(this.isEmpty()){K(this,o,Ce).call(this);return}K(this,o,an).call(this);const{canvas:A,ctx:j}=this;j.setTransform(1,0,0,1,0,0),j.clearRect(0,0,A.width,A.height),K(this,o,Ce).call(this);for(const Y of this.bezierPath2D)j.stroke(Y)},ln=function(A){this.canvas.removeEventListener("pointerleave",t(this,I)),this.canvas.removeEventListener("pointermove",t(this,D)),this.canvas.removeEventListener("pointerup",t(this,y)),this.canvas.addEventListener("pointerdown",t(this,m)),setTimeout(()=>{this.canvas.removeEventListener("contextmenu",W.noContextMenu)},10),K(this,o,Un).call(this,A.offsetX,A.offsetY),this.addToAnnotationStorage(),this.setInBackground()},Te=function(){this.canvas=document.createElement("canvas"),this.canvas.width=this.canvas.height=0,this.canvas.className="inkEditorCanvas",d.AnnotationEditor._l10nPromise.get("editor_ink_canvas_aria_label").then(A=>{var j;return(j=this.canvas)==null?void 0:j.setAttribute("aria-label",A)}),this.div.append(this.canvas),this.ctx=this.canvas.getContext("2d")},Pe=function(){ot(this,C,new ResizeObserver(A=>{const j=A[0].contentRect;j.width&&j.height&&this.setDimensions(j.width,j.height)})),t(this,C).observe(this.div)},le=function(){if(!t(this,b))return;const[A,j]=this.parentDimensions;this.canvas.width=Math.ceil(this.width*A),this.canvas.height=Math.ceil(this.height*j),K(this,o,Ce).call(this)},cn=function(A,j){const Y=K(this,o,Re).call(this),st=(A-Y)/t(this,f),pt=(j-Y)/t(this,x);this.scaleFactor=Math.min(st,pt)},Ce=function(){const A=K(this,o,Re).call(this)/2;this.ctx.setTransform(this.scaleFactor,0,0,this.scaleFactor,this.translationX*this.scaleFactor+A,this.translationY*this.scaleFactor+A)},O=new WeakSet,$n=function(A){const j=new Path2D;for(let Y=0,st=A.length;Y<st;Y++){const[pt,Et,At,tt]=A[Y];Y===0&&j.moveTo(...pt),j.bezierCurveTo(Et[0],Et[1],At[0],At[1],tt[0],tt[1])}return j},hn=function(A,j,Y){const[st,pt,Et,At]=j;switch(Y){case 0:for(let tt=0,wt=A.length;tt<wt;tt+=2)A[tt]+=st,A[tt+1]=At-A[tt+1];break;case 90:for(let tt=0,wt=A.length;tt<wt;tt+=2){const xt=A[tt];A[tt]=A[tt+1]+st,A[tt+1]=xt+pt}break;case 180:for(let tt=0,wt=A.length;tt<wt;tt+=2)A[tt]=Et-A[tt],A[tt+1]+=pt;break;case 270:for(let tt=0,wt=A.length;tt<wt;tt+=2){const xt=A[tt];A[tt]=Et-A[tt+1],A[tt+1]=At-xt}break;default:throw new Error("Invalid rotation")}return A},Vn=function(A,j,Y){const[st,pt,Et,At]=j;switch(Y){case 0:for(let tt=0,wt=A.length;tt<wt;tt+=2)A[tt]-=st,A[tt+1]=At-A[tt+1];break;case 90:for(let tt=0,wt=A.length;tt<wt;tt+=2){const xt=A[tt];A[tt]=A[tt+1]-pt,A[tt+1]=xt-st}break;case 180:for(let tt=0,wt=A.length;tt<wt;tt+=2)A[tt]=Et-A[tt],A[tt+1]-=pt;break;case 270:for(let tt=0,wt=A.length;tt<wt;tt+=2){const xt=A[tt];A[tt]=At-A[tt+1],A[tt+1]=Et-xt}break;default:throw new Error("Invalid rotation")}return A},zn=function(A,j,Y,st){var wt,xt;const pt=[],Et=this.thickness/2,At=A*j+Et,tt=A*Y+Et;for(const Ut of this.paths){const jt=[],Vt=[];for(let kt=0,Ot=Ut.length;kt<Ot;kt++){const[Ht,qt,vt,at]=Ut[kt],lt=A*Ht[0]+At,Rt=A*Ht[1]+tt,zt=A*qt[0]+At,$t=A*qt[1]+tt,U=A*vt[0]+At,gt=A*vt[1]+tt,Tt=A*at[0]+At,Dt=A*at[1]+tt;kt===0&&(jt.push(lt,Rt),Vt.push(lt,Rt)),jt.push(zt,$t,U,gt,Tt,Dt),Vt.push(zt,$t),kt===Ot-1&&Vt.push(Tt,Dt)}pt.push({bezier:K(wt=_t,O,hn).call(wt,jt,st,this.rotation),points:K(xt=_t,O,hn).call(xt,Vt,st,this.rotation)})}return pt},dn=function(){let A=1/0,j=-1/0,Y=1/0,st=-1/0;for(const pt of this.paths)for(const[Et,At,tt,wt]of pt){const xt=n.Util.bezierBoundingBox(...Et,...At,...tt,...wt);A=Math.min(A,xt[0]),Y=Math.min(Y,xt[1]),j=Math.max(j,xt[2]),st=Math.max(st,xt[3])}return[A,Y,j,st]},Re=function(){return t(this,p)?Math.ceil(this.thickness*this.parentScale):0},ce=function(A=!1){if(this.isEmpty())return;if(!t(this,p)){K(this,o,ee).call(this);return}const j=K(this,o,dn).call(this),Y=K(this,o,Re).call(this);ot(this,f,Math.max(d.AnnotationEditor.MIN_SIZE,j[2]-j[0])),ot(this,x,Math.max(d.AnnotationEditor.MIN_SIZE,j[3]-j[1]));const st=Math.ceil(Y+t(this,f)*this.scaleFactor),pt=Math.ceil(Y+t(this,x)*this.scaleFactor),[Et,At]=this.parentDimensions;this.width=st/Et,this.height=pt/At,this.setAspectRatio(st,pt);const tt=this.translationX,wt=this.translationY;this.translationX=-j[0],this.translationY=-j[1],K(this,o,le).call(this),K(this,o,ee).call(this),ot(this,_,st),ot(this,i,pt),this.setDims(st,pt);const xt=A?Y/this.scaleFactor/2:0;this.translate(tt-this.translationX-xt,wt-this.translationY-xt)},rt(_t,O),Kt(_t,"_defaultColor",null),Kt(_t,"_defaultOpacity",1),Kt(_t,"_defaultThickness",1),Kt(_t,"_type","ink");let P=_t;e.InkEditor=P},(T,e,B)=>{var P,x,f,D,I,y,m,E,p,u,b,me,be,ke,un,qn,Xn,fn,Fe,Yn;Object.defineProperty(e,"__esModule",{value:!0}),e.StampEditor=void 0;var n=B(1),d=B(4),H=B(6),W=B(29);const v=class v extends d.AnnotationEditor{constructor($){super({...$,name:"stampEditor"});rt(this,b);rt(this,P,null);rt(this,x,null);rt(this,f,null);rt(this,D,null);rt(this,I,null);rt(this,y,null);rt(this,m,null);rt(this,E,null);rt(this,p,!1);rt(this,u,!1);ot(this,D,$.bitmapUrl),ot(this,I,$.bitmapFile)}static initialize($){d.AnnotationEditor.initialize($)}static get supportedTypes(){const $=["apng","avif","bmp","gif","jpeg","png","svg+xml","webp","x-icon"];return(0,n.shadow)(this,"supportedTypes",$.map(q=>`image/${q}`))}static get supportedTypesStr(){return(0,n.shadow)(this,"supportedTypesStr",this.supportedTypes.join(","))}static isHandlingMimeForPasting($){return this.supportedTypes.includes($)}static paste($,q){q.pasteEditor(n.AnnotationEditorType.STAMP,{bitmapFile:$.getAsFile()})}remove(){var $,q;t(this,x)&&(ot(this,P,null),this._uiManager.imageManager.deleteId(t(this,x)),($=t(this,y))==null||$.remove(),ot(this,y,null),(q=t(this,m))==null||q.disconnect(),ot(this,m,null)),super.remove()}rebuild(){if(!this.parent){t(this,x)&&K(this,b,ke).call(this);return}super.rebuild(),this.div!==null&&(t(this,x)&&K(this,b,ke).call(this),this.isAttachedToDOM||this.parent.add(this))}onceAdded(){this._isDraggable=!0,this.div.focus()}isEmpty(){return!(t(this,f)||t(this,P)||t(this,D)||t(this,I))}get isResizable(){return!0}render(){if(this.div)return this.div;let $,q;if(this.width&&($=this.x,q=this.y),super.render(),this.div.hidden=!0,t(this,P)?K(this,b,un).call(this):K(this,b,ke).call(this),this.width){const[G,it]=this.parentDimensions;this.setAt($*G,q*it,this.width*G,this.height*it)}return this.div}static deserialize($,q,G){if($ instanceof W.StampAnnotationElement)return null;const it=super.deserialize($,q,G),{rect:R,bitmapUrl:V,bitmapId:et,isSvg:S,accessibilityData:s}=$;et&&G.imageManager.isValidId(et)?ot(it,x,et):ot(it,D,V),ot(it,p,S);const[a,g]=it.pageDimensions;return it.width=(R[2]-R[0])/a,it.height=(R[3]-R[1])/g,s&&(it.altTextData=s),it}serialize($=!1,q=null){if(this.isEmpty())return null;const G={annotationType:n.AnnotationEditorType.STAMP,bitmapId:t(this,x),pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:t(this,p),structTreeParentId:this._structTreeParentId};if($)return G.bitmapUrl=K(this,b,Fe).call(this,!0),G.accessibilityData=this.altTextData,G;const{decorative:it,altText:R}=this.altTextData;if(!it&&R&&(G.accessibilityData={type:"Figure",alt:R}),q===null)return G;q.stamps||(q.stamps=new Map);const V=t(this,p)?(G.rect[2]-G.rect[0])*(G.rect[3]-G.rect[1]):null;if(!q.stamps.has(t(this,x)))q.stamps.set(t(this,x),{area:V,serialized:G}),G.bitmap=K(this,b,Fe).call(this,!1);else if(t(this,p)){const et=q.stamps.get(t(this,x));V>et.area&&(et.area=V,et.serialized.bitmap.close(),et.serialized.bitmap=K(this,b,Fe).call(this,!1))}return G}};P=new WeakMap,x=new WeakMap,f=new WeakMap,D=new WeakMap,I=new WeakMap,y=new WeakMap,m=new WeakMap,E=new WeakMap,p=new WeakMap,u=new WeakMap,b=new WeakSet,me=function($,q=!1){if(!$){this.remove();return}ot(this,P,$.bitmap),q||(ot(this,x,$.id),ot(this,p,$.isSvg)),K(this,b,un).call(this)},be=function(){ot(this,f,null),this._uiManager.enableWaiting(!1),t(this,y)&&this.div.focus()},ke=function(){if(t(this,x)){this._uiManager.enableWaiting(!0),this._uiManager.imageManager.getFromId(t(this,x)).then(q=>K(this,b,me).call(this,q,!0)).finally(()=>K(this,b,be).call(this));return}if(t(this,D)){const q=t(this,D);ot(this,D,null),this._uiManager.enableWaiting(!0),ot(this,f,this._uiManager.imageManager.getFromUrl(q).then(G=>K(this,b,me).call(this,G)).finally(()=>K(this,b,be).call(this)));return}if(t(this,I)){const q=t(this,I);ot(this,I,null),this._uiManager.enableWaiting(!0),ot(this,f,this._uiManager.imageManager.getFromFile(q).then(G=>K(this,b,me).call(this,G)).finally(()=>K(this,b,be).call(this)));return}const $=document.createElement("input");$.type="file",$.accept=v.supportedTypesStr,ot(this,f,new Promise(q=>{$.addEventListener("change",async()=>{if(!$.files||$.files.length===0)this.remove();else{this._uiManager.enableWaiting(!0);const G=await this._uiManager.imageManager.getFromFile($.files[0]);K(this,b,me).call(this,G)}q()}),$.addEventListener("cancel",()=>{this.remove(),q()})}).finally(()=>K(this,b,be).call(this))),$.click()},un=function(){const{div:$}=this;let{width:q,height:G}=t(this,P);const[it,R]=this.pageDimensions,V=.75;if(this.width)q=this.width*it,G=this.height*R;else if(q>V*it||G>V*R){const a=Math.min(V*it/q,V*R/G);q*=a,G*=a}const[et,S]=this.parentDimensions;this.setDims(q*et/it,G*S/R),this._uiManager.enableWaiting(!1);const s=ot(this,y,document.createElement("canvas"));$.append(s),$.hidden=!1,K(this,b,fn).call(this,q,G),K(this,b,Yn).call(this),t(this,u)||(this.parent.addUndoableEditor(this),ot(this,u,!0)),this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",subtype:this.editorType,data:{action:"inserted_image"}}}),this.addAltTextButton()},qn=function($,q){var V;const[G,it]=this.parentDimensions;this.width=$/G,this.height=q/it,this.setDims($,q),(V=this._initialOptions)!=null&&V.isCentered?this.center():this.fixAndSetPosition(),this._initialOptions=null,t(this,E)!==null&&clearTimeout(t(this,E)),ot(this,E,setTimeout(()=>{ot(this,E,null),K(this,b,fn).call(this,$,q)},200))},Xn=function($,q){const{width:G,height:it}=t(this,P);let R=G,V=it,et=t(this,P);for(;R>2*$||V>2*q;){const S=R,s=V;R>2*$&&(R=R>=16384?Math.floor(R/2)-1:Math.ceil(R/2)),V>2*q&&(V=V>=16384?Math.floor(V/2)-1:Math.ceil(V/2));const a=new OffscreenCanvas(R,V);a.getContext("2d").drawImage(et,0,0,S,s,0,0,R,V),et=a.transferToImageBitmap()}return et},fn=function($,q){$=Math.ceil($),q=Math.ceil(q);const G=t(this,y);if(!G||G.width===$&&G.height===q)return;G.width=$,G.height=q;const it=t(this,p)?t(this,P):K(this,b,Xn).call(this,$,q),R=G.getContext("2d");R.filter=this._uiManager.hcmFilter,R.drawImage(it,0,0,it.width,it.height,0,0,$,q)},Fe=function($){if($){if(t(this,p)){const it=this._uiManager.imageManager.getSvgUrl(t(this,x));if(it)return it}const q=document.createElement("canvas");return{width:q.width,height:q.height}=t(this,P),q.getContext("2d").drawImage(t(this,P),0,0),q.toDataURL()}if(t(this,p)){const[q,G]=this.pageDimensions,it=Math.round(this.width*q*H.PixelsPerInch.PDF_TO_CSS_UNITS),R=Math.round(this.height*G*H.PixelsPerInch.PDF_TO_CSS_UNITS),V=new OffscreenCanvas(it,R);return V.getContext("2d").drawImage(t(this,P),0,0,t(this,P).width,t(this,P).height,0,0,it,R),V.transferToImageBitmap()}return structuredClone(t(this,P))},Yn=function(){ot(this,m,new ResizeObserver($=>{const q=$[0].contentRect;q.width&&q.height&&K(this,b,qn).call(this,q.width,q.height)})),t(this,m).observe(this.div)},Kt(v,"_type","stamp");let ht=v;e.StampEditor=ht}],__webpack_module_cache__={};function __w_pdfjs_require__(T){var e=__webpack_module_cache__[T];if(e!==void 0)return e.exports;var B=__webpack_module_cache__[T]={exports:{}};return __webpack_modules__[T](B,B.exports,__w_pdfjs_require__),B.exports}var __webpack_exports__={};return(()=>{var T=__webpack_exports__;Object.defineProperty(T,"__esModule",{value:!0}),Object.defineProperty(T,"AbortException",{enumerable:!0,get:function(){return e.AbortException}}),Object.defineProperty(T,"AnnotationEditorLayer",{enumerable:!0,get:function(){return H.AnnotationEditorLayer}}),Object.defineProperty(T,"AnnotationEditorParamsType",{enumerable:!0,get:function(){return e.AnnotationEditorParamsType}}),Object.defineProperty(T,"AnnotationEditorType",{enumerable:!0,get:function(){return e.AnnotationEditorType}}),Object.defineProperty(T,"AnnotationEditorUIManager",{enumerable:!0,get:function(){return W.AnnotationEditorUIManager}}),Object.defineProperty(T,"AnnotationLayer",{enumerable:!0,get:function(){return ht.AnnotationLayer}}),Object.defineProperty(T,"AnnotationMode",{enumerable:!0,get:function(){return e.AnnotationMode}}),Object.defineProperty(T,"CMapCompressionType",{enumerable:!0,get:function(){return e.CMapCompressionType}}),Object.defineProperty(T,"DOMSVGFactory",{enumerable:!0,get:function(){return n.DOMSVGFactory}}),Object.defineProperty(T,"FeatureTest",{enumerable:!0,get:function(){return e.FeatureTest}}),Object.defineProperty(T,"GlobalWorkerOptions",{enumerable:!0,get:function(){return P.GlobalWorkerOptions}}),Object.defineProperty(T,"ImageKind",{enumerable:!0,get:function(){return e.ImageKind}}),Object.defineProperty(T,"InvalidPDFException",{enumerable:!0,get:function(){return e.InvalidPDFException}}),Object.defineProperty(T,"MissingPDFException",{enumerable:!0,get:function(){return e.MissingPDFException}}),Object.defineProperty(T,"OPS",{enumerable:!0,get:function(){return e.OPS}}),Object.defineProperty(T,"PDFDataRangeTransport",{enumerable:!0,get:function(){return B.PDFDataRangeTransport}}),Object.defineProperty(T,"PDFDateString",{enumerable:!0,get:function(){return n.PDFDateString}}),Object.defineProperty(T,"PDFWorker",{enumerable:!0,get:function(){return B.PDFWorker}}),Object.defineProperty(T,"PasswordResponses",{enumerable:!0,get:function(){return e.PasswordResponses}}),Object.defineProperty(T,"PermissionFlag",{enumerable:!0,get:function(){return e.PermissionFlag}}),Object.defineProperty(T,"PixelsPerInch",{enumerable:!0,get:function(){return n.PixelsPerInch}}),Object.defineProperty(T,"PromiseCapability",{enumerable:!0,get:function(){return e.PromiseCapability}}),Object.defineProperty(T,"RenderingCancelledException",{enumerable:!0,get:function(){return n.RenderingCancelledException}}),Object.defineProperty(T,"SVGGraphics",{enumerable:!0,get:function(){return B.SVGGraphics}}),Object.defineProperty(T,"UnexpectedResponseException",{enumerable:!0,get:function(){return e.UnexpectedResponseException}}),Object.defineProperty(T,"Util",{enumerable:!0,get:function(){return e.Util}}),Object.defineProperty(T,"VerbosityLevel",{enumerable:!0,get:function(){return e.VerbosityLevel}}),Object.defineProperty(T,"XfaLayer",{enumerable:!0,get:function(){return x.XfaLayer}}),Object.defineProperty(T,"build",{enumerable:!0,get:function(){return B.build}}),Object.defineProperty(T,"createValidAbsoluteUrl",{enumerable:!0,get:function(){return e.createValidAbsoluteUrl}}),Object.defineProperty(T,"getDocument",{enumerable:!0,get:function(){return B.getDocument}}),Object.defineProperty(T,"getFilenameFromUrl",{enumerable:!0,get:function(){return n.getFilenameFromUrl}}),Object.defineProperty(T,"getPdfFilenameFromUrl",{enumerable:!0,get:function(){return n.getPdfFilenameFromUrl}}),Object.defineProperty(T,"getXfaPageViewport",{enumerable:!0,get:function(){return n.getXfaPageViewport}}),Object.defineProperty(T,"isDataScheme",{enumerable:!0,get:function(){return n.isDataScheme}}),Object.defineProperty(T,"isPdfFile",{enumerable:!0,get:function(){return n.isPdfFile}}),Object.defineProperty(T,"loadScript",{enumerable:!0,get:function(){return n.loadScript}}),Object.defineProperty(T,"noContextMenu",{enumerable:!0,get:function(){return n.noContextMenu}}),Object.defineProperty(T,"normalizeUnicode",{enumerable:!0,get:function(){return e.normalizeUnicode}}),Object.defineProperty(T,"renderTextLayer",{enumerable:!0,get:function(){return d.renderTextLayer}}),Object.defineProperty(T,"setLayerDimensions",{enumerable:!0,get:function(){return n.setLayerDimensions}}),Object.defineProperty(T,"shadow",{enumerable:!0,get:function(){return e.shadow}}),Object.defineProperty(T,"updateTextLayer",{enumerable:!0,get:function(){return d.updateTextLayer}}),Object.defineProperty(T,"version",{enumerable:!0,get:function(){return B.version}});var e=__w_pdfjs_require__(1),B=__w_pdfjs_require__(2),n=__w_pdfjs_require__(6),d=__w_pdfjs_require__(26),H=__w_pdfjs_require__(27),W=__w_pdfjs_require__(5),ht=__w_pdfjs_require__(29),P=__w_pdfjs_require__(14),x=__w_pdfjs_require__(32)})(),__webpack_exports__})())}(pdf$1)),pdf$1.exports}var pdfExports=requirePdf();const pdf=getDefaultExportFromCjs(pdfExports),pdfjsModule=_mergeNamespaces({__proto__:null,default:pdf},[pdfExports]),pdfjs="default"in pdfjsModule?pdf:pdfjsModule;var __spreadArray=function(T,e,B){if(B||arguments.length===2)for(var n=0,d=e.length,H;n<d;n++)(H||!(n in e))&&(H||(H=Array.prototype.slice.call(e,0,n)),H[n]=e[n]);return T.concat(H||Array.prototype.slice.call(e))},clipboardEvents=["onCopy","onCut","onPaste"],compositionEvents=["onCompositionEnd","onCompositionStart","onCompositionUpdate"],focusEvents=["onFocus","onBlur"],formEvents=["onInput","onInvalid","onReset","onSubmit"],imageEvents=["onLoad","onError"],keyboardEvents=["onKeyDown","onKeyPress","onKeyUp"],mediaEvents=["onAbort","onCanPlay","onCanPlayThrough","onDurationChange","onEmptied","onEncrypted","onEnded","onError","onLoadedData","onLoadedMetadata","onLoadStart","onPause","onPlay","onPlaying","onProgress","onRateChange","onSeeked","onSeeking","onStalled","onSuspend","onTimeUpdate","onVolumeChange","onWaiting"],mouseEvents=["onClick","onContextMenu","onDoubleClick","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp"],dragEvents=["onDrag","onDragEnd","onDragEnter","onDragExit","onDragLeave","onDragOver","onDragStart","onDrop"],selectionEvents=["onSelect"],touchEvents=["onTouchCancel","onTouchEnd","onTouchMove","onTouchStart"],pointerEvents=["onPointerDown","onPointerMove","onPointerUp","onPointerCancel","onGotPointerCapture","onLostPointerCapture","onPointerEnter","onPointerLeave","onPointerOver","onPointerOut"],uiEvents=["onScroll"],wheelEvents=["onWheel"],animationEvents=["onAnimationStart","onAnimationEnd","onAnimationIteration"],transitionEvents=["onTransitionEnd"],otherEvents=["onToggle"],changeEvents=["onChange"],allEvents=__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray([],clipboardEvents,!0),compositionEvents,!0),focusEvents,!0),formEvents,!0),imageEvents,!0),keyboardEvents,!0),mediaEvents,!0),mouseEvents,!0),dragEvents,!0),selectionEvents,!0),touchEvents,!0),pointerEvents,!0),uiEvents,!0),wheelEvents,!0),animationEvents,!0),transitionEvents,!0),changeEvents,!0),otherEvents,!0);function makeEventProps(T,e){var B={};return allEvents.forEach(function(n){var d=T[n];d&&(e?B[n]=function(H){return d(H,e(n))}:B[n]=d)}),B}function makeCancellablePromise(T){var e=!1,B=new Promise(function(n,d){T.then(function(H){return!e&&n(H)}).catch(function(H){return!e&&d(H)})});return{promise:B,cancel:function(){e=!0}}}var prefix="Invariant failed";function invariant(T,e){if(!T)throw new Error(prefix)}var warning_1,hasRequiredWarning;function requireWarning(){if(hasRequiredWarning)return warning_1;hasRequiredWarning=1;var T=function(){};return warning_1=T,warning_1}var warningExports=requireWarning();const warning=getDefaultExportFromCjs(warningExports);var has=Object.prototype.hasOwnProperty;function find(T,e,B){for(B of T.keys())if(dequal(B,e))return B}function dequal(T,e){var B,n,d;if(T===e)return!0;if(T&&e&&(B=T.constructor)===e.constructor){if(B===Date)return T.getTime()===e.getTime();if(B===RegExp)return T.toString()===e.toString();if(B===Array){if((n=T.length)===e.length)for(;n--&&dequal(T[n],e[n]););return n===-1}if(B===Set){if(T.size!==e.size)return!1;for(n of T)if(d=n,d&&typeof d=="object"&&(d=find(e,d),!d)||!e.has(d))return!1;return!0}if(B===Map){if(T.size!==e.size)return!1;for(n of T)if(d=n[0],d&&typeof d=="object"&&(d=find(e,d),!d)||!dequal(n[1],e.get(d)))return!1;return!0}if(B===ArrayBuffer)T=new Uint8Array(T),e=new Uint8Array(e);else if(B===DataView){if((n=T.byteLength)===e.byteLength)for(;n--&&T.getInt8(n)===e.getInt8(n););return n===-1}if(ArrayBuffer.isView(T)){if((n=T.byteLength)===e.byteLength)for(;n--&&T[n]===e[n];);return n===-1}if(!B||typeof T=="object"){n=0;for(B in T)if(has.call(T,B)&&++n&&!has.call(e,B)||!(B in e)||!dequal(T[B],e[B]))return!1;return Object.keys(e).length===n}}return T!==T&&e!==e}const DocumentContext=reactExports.createContext(null);function Message({children:T,type:e}){return React.createElement("div",{className:`react-pdf__message react-pdf__message--${e}`},T)}const DEFAULT_LINK_REL="noopener noreferrer nofollow";class LinkService{constructor(){this.externalLinkEnabled=!0,this.externalLinkRel=void 0,this.externalLinkTarget=void 0,this.isInPresentationMode=!1,this.pdfDocument=void 0,this.pdfViewer=void 0}setDocument(e){this.pdfDocument=e}setViewer(e){this.pdfViewer=e}setExternalLinkRel(e){this.externalLinkRel=e}setExternalLinkTarget(e){this.externalLinkTarget=e}setHistory(){}get pagesCount(){return this.pdfDocument?this.pdfDocument.numPages:0}get page(){return invariant(this.pdfViewer),this.pdfViewer.currentPageNumber||0}set page(e){invariant(this.pdfViewer),this.pdfViewer.currentPageNumber=e}get rotation(){return 0}set rotation(e){}goToDestination(e){return new Promise(B=>{invariant(this.pdfDocument),invariant(e),typeof e=="string"?this.pdfDocument.getDestination(e).then(B):Array.isArray(e)?B(e):e.then(B)}).then(B=>{invariant(Array.isArray(B));const n=B[0];new Promise(d=>{invariant(this.pdfDocument),n instanceof Object?this.pdfDocument.getPageIndex(n).then(H=>{d(H)}).catch(()=>{invariant(!1)}):typeof n=="number"?d(n):invariant(!1)}).then(d=>{const H=d+1;invariant(this.pdfViewer),invariant(H>=1&&H<=this.pagesCount),this.pdfViewer.scrollPageIntoView({dest:B,pageIndex:d,pageNumber:H})})})}navigateTo(e){this.goToDestination(e)}goToPage(e){const B=e-1;invariant(this.pdfViewer),invariant(e>=1&&e<=this.pagesCount),this.pdfViewer.scrollPageIntoView({pageIndex:B,pageNumber:e})}addLinkAttributes(e,B,n){e.href=B,e.rel=this.externalLinkRel||DEFAULT_LINK_REL,e.target=n?"_blank":this.externalLinkTarget||""}getDestinationHash(){return"#"}getAnchorUrl(){return"#"}setHash(){}executeNamedAction(){}cachePageRef(){}isPageVisible(){return!0}isPageCached(){return!0}executeSetOCGState(){}}const PasswordResponses={NEED_PASSWORD:1,INCORRECT_PASSWORD:2},isBrowser=typeof document<"u",isLocalFileSystem=isBrowser&&window.location.protocol==="file:";function isDefined(T){return typeof T<"u"}function isProvided(T){return isDefined(T)&&T!==null}function isString(T){return typeof T=="string"}function isArrayBuffer(T){return T instanceof ArrayBuffer}function isBlob(T){return invariant(isBrowser),T instanceof Blob}function isDataURI(T){return isString(T)&&/^data:/.test(T)}function dataURItoByteString(T){invariant(isDataURI(T));const[e="",B=""]=T.split(",");return e.split(";").indexOf("base64")!==-1?atob(B):unescape(B)}function getDevicePixelRatio(){return isBrowser&&window.devicePixelRatio||1}const allowFileAccessFromFilesTip="On Chromium based browsers, you can use --allow-file-access-from-files flag for debugging purposes.";function displayCORSWarning(){warning(!isLocalFileSystem,`Loading PDF as base64 strings/URLs may not work on protocols other than HTTP/HTTPS. ${allowFileAccessFromFilesTip}`)}function displayWorkerWarning(){warning(!isLocalFileSystem,`Loading PDF.js worker may not work on protocols other than HTTP/HTTPS. ${allowFileAccessFromFilesTip}`)}function cancelRunningTask(T){T&&T.cancel&&T.cancel()}function makePageCallback(T,e){return Object.defineProperty(T,"width",{get(){return this.view[2]*e},configurable:!0}),Object.defineProperty(T,"height",{get(){return this.view[3]*e},configurable:!0}),Object.defineProperty(T,"originalWidth",{get(){return this.view[2]},configurable:!0}),Object.defineProperty(T,"originalHeight",{get(){return this.view[3]},configurable:!0}),T}function isCancelException(T){return T.name==="RenderingCancelledException"}function loadFromFile(T){return new Promise((e,B)=>{const n=new FileReader;n.onload=()=>{if(!n.result)return B(new Error("Error while reading a file."));e(n.result)},n.onerror=d=>{if(!d.target)return B(new Error("Error while reading a file."));const{error:H}=d.target;if(!H)return B(new Error("Error while reading a file."));switch(H.code){case H.NOT_FOUND_ERR:return B(new Error("Error while reading a file: File not found."));case H.SECURITY_ERR:return B(new Error("Error while reading a file: Security error."));case H.ABORT_ERR:return B(new Error("Error while reading a file: Aborted."));default:return B(new Error("Error while reading a file."))}},n.readAsArrayBuffer(T)})}function reducer(T,e){switch(e.type){case"RESOLVE":return{value:e.value,error:void 0};case"REJECT":return{value:!1,error:e.error};case"RESET":return{value:void 0,error:void 0};default:return T}}function useResolver(){return reactExports.useReducer(reducer,{value:void 0,error:void 0})}const{PDFDataRangeTransport:PDFDataRangeTransport$1}=pdfjs,eventProps=(()=>{const T={};return allEvents.forEach(e=>{T[e]=PropTypes.func}),T})(),isTypedArray=PropTypes.oneOfType([PropTypes.instanceOf(Int8Array),PropTypes.instanceOf(Uint8Array),PropTypes.instanceOf(Uint8ClampedArray),PropTypes.instanceOf(Int16Array),PropTypes.instanceOf(Uint16Array),PropTypes.instanceOf(Int32Array),PropTypes.instanceOf(Uint32Array),PropTypes.instanceOf(Float32Array),PropTypes.instanceOf(Float64Array)]),fileTypes=[PropTypes.string,PropTypes.instanceOf(ArrayBuffer),PropTypes.shape({data:PropTypes.oneOfType([PropTypes.string,PropTypes.instanceOf(ArrayBuffer),PropTypes.arrayOf(PropTypes.number.isRequired),isTypedArray]).isRequired}),PropTypes.shape({range:PropTypes.instanceOf(PDFDataRangeTransport$1).isRequired}),PropTypes.shape({url:PropTypes.string.isRequired})];typeof Blob<"u"&&fileTypes.push(PropTypes.instanceOf(Blob));const isClassName=PropTypes.oneOfType([PropTypes.string,PropTypes.arrayOf(PropTypes.string)]),isFile=PropTypes.oneOfType(fileTypes);PropTypes.instanceOf(LinkService);PropTypes.oneOf(["_self","_blank","_parent","_top"]);PropTypes.shape({commonObjs:PropTypes.shape({}).isRequired,getAnnotations:PropTypes.func.isRequired,getTextContent:PropTypes.func.isRequired,getViewport:PropTypes.func.isRequired,render:PropTypes.func.isRequired});const isPageIndex=function T(e,B,n){const{[B]:d,pageNumber:H,pdf:W}=e;if(!isDefined(W))return null;if(isDefined(d)){if(typeof d!="number")return new Error(`\`${B}\` of type \`${typeof d}\` supplied to \`${n}\`, expected \`number\`.`);if(d<0)return new Error(`Expected \`${B}\` to be greater or equal to 0.`);const{numPages:ht}=W;if(d+1>ht)return new Error(`Expected \`${B}\` to be less or equal to ${ht-1}.`)}else if(!isDefined(H))return new Error(`\`${B}\` not supplied. Either pageIndex or pageNumber must be supplied to \`${n}\`.`);return null},isPageNumber=function T(e,B,n){const{[B]:d,pageIndex:H,pdf:W}=e;if(!isDefined(W))return null;if(isDefined(d)){if(typeof d!="number")return new Error(`\`${B}\` of type \`${typeof d}\` supplied to \`${n}\`, expected \`number\`.`);if(d<1)return new Error(`Expected \`${B}\` to be greater or equal to 1.`);const{numPages:ht}=W;if(d>ht)return new Error(`Expected \`${B}\` to be less or equal to ${ht}.`)}else if(!isDefined(H))return new Error(`\`${B}\` not supplied. Either pageIndex or pageNumber must be supplied to \`${n}\`.`);return null},isPdf=PropTypes.oneOfType([PropTypes.any,PropTypes.oneOf([!1])]),isRef=PropTypes.oneOfType([PropTypes.func,PropTypes.exact({current:PropTypes.any})]),isRenderMode=PropTypes.oneOf(["canvas","custom","none","svg"]),isRotate=PropTypes.oneOf([0,90,180,270]);var __awaiter=function(T,e,B,n){function d(H){return H instanceof B?H:new B(function(W){W(H)})}return new(B||(B=Promise))(function(H,W){function ht(f){try{x(n.next(f))}catch(D){W(D)}}function P(f){try{x(n.throw(f))}catch(D){W(D)}}function x(f){f.done?H(f.value):d(f.value).then(ht,P)}x((n=n.apply(T,e||[])).next())})},__rest$1=function(T,e){var B={};for(var n in T)Object.prototype.hasOwnProperty.call(T,n)&&e.indexOf(n)<0&&(B[n]=T[n]);if(T!=null&&typeof Object.getOwnPropertySymbols=="function")for(var d=0,n=Object.getOwnPropertySymbols(T);d<n.length;d++)e.indexOf(n[d])<0&&Object.prototype.propertyIsEnumerable.call(T,n[d])&&(B[n[d]]=T[n[d]]);return B};const{PDFDataRangeTransport}=pdfjs,defaultOnPassword=(T,e)=>{switch(e){case PasswordResponses.NEED_PASSWORD:{const B=prompt("Enter the password to open this PDF file.");T(B);break}case PasswordResponses.INCORRECT_PASSWORD:{const B=prompt("Invalid password. Please try again.");T(B);break}}};function isParameterObject(T){return typeof T=="object"&&T!==null&&("data"in T||"range"in T||"url"in T)}const Document=reactExports.forwardRef(function T(e,B){var{children:n,className:d,error:H="Failed to load PDF file.",externalLinkRel:W,externalLinkTarget:ht,file:P,inputRef:x,imageResourcesPath:f,loading:D="Loading PDF…",noData:I="No PDF file specified.",onItemClick:y,onLoadError:m,onLoadProgress:E,onLoadSuccess:p,onPassword:u=defaultOnPassword,onSourceError:b,onSourceSuccess:C,options:_,renderMode:i,rotate:c}=e,o=__rest$1(e,["children","className","error","externalLinkRel","externalLinkTarget","file","inputRef","imageResourcesPath","loading","noData","onItemClick","onLoadError","onLoadProgress","onLoadSuccess","onPassword","onSourceError","onSourceSuccess","options","renderMode","rotate"]);const[l,h]=useResolver(),{value:F,error:r}=l,[v,k]=useResolver(),{value:Z,error:$}=v,q=reactExports.useRef(new LinkService),G=reactExports.useRef([]),it=reactExports.useRef(),R=reactExports.useRef();reactExports.useEffect(()=>{P&&P!==it.current&&isParameterObject(P)&&(warning(!dequal(P,it.current),`File prop passed to <Document /> changed, but it's equal to previous one. This might result in unnecessary reloads. Consider memoizing the value passed to "file" prop.`),it.current=P)},[P]),reactExports.useEffect(()=>{_&&_!==R.current&&(warning(!dequal(_,R.current),`Options prop passed to <Document /> changed, but it's equal to previous one. This might result in unnecessary reloads. Consider memoizing the value passed to "options" prop.`),R.current=_)},[_]);const V=reactExports.useRef({scrollPageIntoView:J=>{const{dest:Q,pageNumber:A,pageIndex:j=A-1}=J;if(y){y({dest:Q,pageIndex:j,pageNumber:A});return}const Y=G.current[j];if(Y){Y.scrollIntoView();return}warning(!1,`An internal link leading to page ${A} was clicked, but neither <Document> was provided with onItemClick nor it was able to find the page within itself. Either provide onItemClick to <Document> and handle navigating by yourself or ensure that all pages are rendered within <Document>.`)}});reactExports.useImperativeHandle(B,()=>({linkService:q,pages:G,viewer:V}),[]);function et(){C&&C()}function S(){r&&(warning(!1,r.toString()),b&&b(r))}function s(){h({type:"RESET"})}reactExports.useEffect(s,[P,h]);const a=reactExports.useCallback(()=>__awaiter(this,void 0,void 0,function*(){if(!P)return null;if(typeof P=="string")return isDataURI(P)?{data:dataURItoByteString(P)}:(displayCORSWarning(),{url:P});if(P instanceof PDFDataRangeTransport)return{range:P};if(isArrayBuffer(P))return{data:P};if(isBrowser&&isBlob(P))return{data:yield loadFromFile(P)};if(invariant(typeof P=="object"),invariant(isParameterObject(P)),"url"in P&&typeof P.url=="string"){if(isDataURI(P.url)){const{url:J}=P,Q=__rest$1(P,["url"]),A=dataURItoByteString(J);return Object.assign({data:A},Q)}displayCORSWarning()}return P}),[P]);reactExports.useEffect(()=>{const J=makeCancellablePromise(a());return J.promise.then(Q=>{h({type:"RESOLVE",value:Q})}).catch(Q=>{h({type:"REJECT",error:Q})}),()=>{cancelRunningTask(J)}},[a,h]),reactExports.useEffect(()=>{if(!(typeof F>"u")){if(F===!1){S();return}et()}},[F]);function g(){Z&&(p&&p(Z),G.current=new Array(Z.numPages),q.current.setDocument(Z))}function L(){$&&(warning(!1,$.toString()),m&&m($))}function O(){k({type:"RESET"})}reactExports.useEffect(O,[k,F]);function N(){if(!F)return;const J=Object.assign(Object.assign({},_),{isEvalSupported:!1}),Q=Object.assign(Object.assign({},F),J),A=pdfjs.getDocument(Q);E&&(A.onProgress=E),u&&(A.onPassword=u);const j=A;return j.promise.then(Y=>{k({type:"RESOLVE",value:Y})}).catch(Y=>{j.destroyed||k({type:"REJECT",error:Y})}),()=>{j.destroy()}}reactExports.useEffect(N,[_,k,F]),reactExports.useEffect(()=>{if(!(typeof Z>"u")){if(Z===!1){L();return}g()}},[Z]);function X(){q.current.setViewer(V.current),q.current.setExternalLinkRel(W),q.current.setExternalLinkTarget(ht)}reactExports.useEffect(X,[W,ht]);function nt(J,Q){G.current[J]=Q}function ct(J){delete G.current[J]}const ft=reactExports.useMemo(()=>({imageResourcesPath:f,linkService:q.current,onItemClick:y,pdf:Z,registerPage:nt,renderMode:i,rotate:c,unregisterPage:ct}),[f,y,Z,i,c]),bt=reactExports.useMemo(()=>makeEventProps(o,()=>Z),[o,Z]);function mt(){return React.createElement(DocumentContext.Provider,{value:ft},n)}function _t(){return P?Z==null?React.createElement(Message,{type:"loading"},typeof D=="function"?D():D):Z===!1?React.createElement(Message,{type:"error"},typeof H=="function"?H():H):mt():React.createElement(Message,{type:"no-data"},typeof I=="function"?I():I)}return React.createElement("div",Object.assign({className:clsx("react-pdf__Document",d),ref:x,style:{"--scale-factor":"1"}},bt),_t())}),isFunctionOrNode$1=PropTypes.oneOfType([PropTypes.func,PropTypes.node]);Document.propTypes=Object.assign(Object.assign({},eventProps),{children:PropTypes.node,className:isClassName,error:isFunctionOrNode$1,externalLinkRel:PropTypes.string,externalLinkTarget:PropTypes.oneOf(["_self","_blank","_parent","_top"]),file:isFile,imageResourcesPath:PropTypes.string,inputRef:isRef,loading:isFunctionOrNode$1,noData:isFunctionOrNode$1,onItemClick:PropTypes.func,onLoadError:PropTypes.func,onLoadProgress:PropTypes.func,onLoadSuccess:PropTypes.func,onPassword:PropTypes.func,onSourceError:PropTypes.func,onSourceSuccess:PropTypes.func,options:PropTypes.shape({canvasFactory:PropTypes.any,canvasMaxAreaInBytes:PropTypes.number,cMapPacked:PropTypes.bool,CMapReaderFactory:PropTypes.any,cMapUrl:PropTypes.string,disableAutoFetch:PropTypes.bool,disableFontFace:PropTypes.bool,disableRange:PropTypes.bool,disableStream:PropTypes.bool,docBaseUrl:PropTypes.string,enableXfa:PropTypes.bool,filterFactory:PropTypes.any,fontExtraProperties:PropTypes.bool,httpHeaders:PropTypes.object,isEvalSupported:PropTypes.bool,isOffscreenCanvasSupported:PropTypes.bool,length:PropTypes.number,maxImageSize:PropTypes.number,ownerDocument:PropTypes.any,password:PropTypes.string,pdfBug:PropTypes.bool,rangeChunkSize:PropTypes.number,StandardFontDataFactory:PropTypes.any,standardFontDataUrl:PropTypes.string,stopAtErrors:PropTypes.bool,useSystemFonts:PropTypes.bool,useWorkerFetch:PropTypes.bool,verbosity:PropTypes.number,withCredentials:PropTypes.bool,worker:PropTypes.any}),rotate:PropTypes.number});function useDocumentContext(){return reactExports.useContext(DocumentContext)}function mergeRefs(){for(var T=[],e=0;e<arguments.length;e++)T[e]=arguments[e];var B=T.filter(Boolean);if(B.length<=1){var n=B[0];return n||null}return function(H){B.forEach(function(W){typeof W=="function"?W(H):W&&(W.current=H)})}}const PageContext=reactExports.createContext(null),PDF_ROLE_TO_HTML_ROLE={Document:null,DocumentFragment:null,Part:"group",Sect:"group",Div:"group",Aside:"note",NonStruct:"none",P:null,H:"heading",Title:null,FENote:"note",Sub:"group",Lbl:null,Span:null,Em:null,Strong:null,Link:"link",Annot:"note",Form:"form",Ruby:null,RB:null,RT:null,RP:null,Warichu:null,WT:null,WP:null,L:"list",LI:"listitem",LBody:null,Table:"table",TR:"row",TH:"columnheader",TD:"cell",THead:"columnheader",TBody:null,TFoot:null,Caption:null,Figure:"figure",Formula:null,Artifact:null},HEADING_PATTERN=/^H(\d+)$/;function isPdfRole(T){return T in PDF_ROLE_TO_HTML_ROLE}function isStructTreeNode(T){return"children"in T}function isStructTreeNodeWithOnlyContentChild(T){return isStructTreeNode(T)?T.children.length===1&&0 in T.children&&"id"in T.children[0]:!1}function getRoleAttributes(T){const e={};if(isStructTreeNode(T)){const{role:B}=T,n=B.match(HEADING_PATTERN);if(n)e.role="heading",e["aria-level"]=Number(n[1]);else if(isPdfRole(B)){const d=PDF_ROLE_TO_HTML_ROLE[B];d&&(e.role=d)}}return e}function getBaseAttributes(T){const e={};if(isStructTreeNode(T)){if(T.alt!==void 0&&(e["aria-label"]=T.alt),T.lang!==void 0&&(e.lang=T.lang),isStructTreeNodeWithOnlyContentChild(T)){const[B]=T.children;if(B){const n=getBaseAttributes(B);return Object.assign(Object.assign({},e),n)}}}else"id"in T&&(e["aria-owns"]=T.id);return e}function getAttributes(T){return T?Object.assign(Object.assign({},getRoleAttributes(T)),getBaseAttributes(T)):null}function StructTreeItem({className:T,node:e}){const B=reactExports.useMemo(()=>getAttributes(e),[e]),n=reactExports.useMemo(()=>!isStructTreeNode(e)||isStructTreeNodeWithOnlyContentChild(e)?null:e.children.map((d,H)=>React.createElement(StructTreeItem,{key:H,node:d})),[e]);return React.createElement("span",Object.assign({className:T},B),n)}function usePageContext(){return reactExports.useContext(PageContext)}function StructTree(){const T=usePageContext();invariant(T);const{onGetStructTreeError:e,onGetStructTreeSuccess:B}=T,[n,d]=useResolver(),{value:H,error:W}=n,{customTextRenderer:ht,page:P}=T;function x(){H&&B&&B(H)}function f(){W&&(warning(!1,W.toString()),e&&e(W))}function D(){d({type:"RESET"})}reactExports.useEffect(D,[d,P]);function I(){if(ht||!P)return;const y=makeCancellablePromise(P.getStructTree()),m=y;return y.promise.then(E=>{d({type:"RESOLVE",value:E})}).catch(E=>{d({type:"REJECT",error:E})}),()=>cancelRunningTask(m)}return reactExports.useEffect(I,[ht,P,d]),reactExports.useEffect(()=>{if(H!==void 0){if(H===!1){f();return}x()}},[H]),H?React.createElement(StructTreeItem,{className:"react-pdf__Page__structTree structTree",node:H}):null}const ANNOTATION_MODE=pdfjs.AnnotationMode;function PageCanvas(T){const e=usePageContext();invariant(e);const B=Object.assign(Object.assign({},e),T),{_className:n,canvasBackground:d,devicePixelRatio:H=getDevicePixelRatio(),onRenderError:W,onRenderSuccess:ht,page:P,renderForms:x,renderTextLayer:f,rotate:D,scale:I}=B,{canvasRef:y}=T;invariant(P);const m=reactExports.useRef(null);function E(){P&&ht&&ht(makePageCallback(P,I))}function p(i){isCancelException(i)||(warning(!1,i.toString()),W&&W(i))}const u=reactExports.useMemo(()=>P.getViewport({scale:I*H,rotation:D}),[H,P,D,I]),b=reactExports.useMemo(()=>P.getViewport({scale:I,rotation:D}),[P,D,I]);function C(){if(!P)return;P.cleanup();const{current:i}=m;if(!i)return;i.width=u.width,i.height=u.height,i.style.width=`${Math.floor(b.width)}px`,i.style.height=`${Math.floor(b.height)}px`,i.style.visibility="hidden";const c={annotationMode:x?ANNOTATION_MODE.ENABLE_FORMS:ANNOTATION_MODE.ENABLE,canvasContext:i.getContext("2d",{alpha:!1}),viewport:u};d&&(c.background=d);const o=P.render(c),l=o;return o.promise.then(()=>{i.style.visibility="",E()}).catch(p),()=>cancelRunningTask(l)}reactExports.useEffect(C,[d,m,H,P,x,u,b]);const _=reactExports.useCallback(()=>{const{current:i}=m;i&&(i.width=0,i.height=0)},[m]);return reactExports.useEffect(()=>_,[_]),React.createElement("canvas",{className:`${n}__canvas`,dir:"ltr",ref:mergeRefs(y,m),style:{display:"block",userSelect:"none"}},f?React.createElement(StructTree,null):null)}function PageSVG(){const T=usePageContext();invariant(T);const{_className:e,onRenderSuccess:B,onRenderError:n,page:d,rotate:H,scale:W}=T;invariant(d);const[ht,P]=useResolver(),{value:x,error:f}=ht;function D(){d&&B&&B(makePageCallback(d,W))}function I(){f&&(isCancelException(f)||(warning(!1,f.toString()),n&&n(f)))}const y=reactExports.useMemo(()=>d.getViewport({scale:W,rotation:H}),[d,H,W]);function m(){P({type:"RESET"})}reactExports.useEffect(m,[d,P,y]);function E(){if(!d)return;const C=makeCancellablePromise(d.getOperatorList());return C.promise.then(_=>{new pdfjs.SVGGraphics(d.commonObjs,d.objs).getSVG(_,y).then(c=>{if(!(c instanceof SVGElement))throw new Error("getSVG returned unexpected result.");P({type:"RESOLVE",value:c})}).catch(c=>{P({type:"REJECT",error:c})})}).catch(_=>{P({type:"REJECT",error:_})}),()=>cancelRunningTask(C)}reactExports.useEffect(E,[d,P,y]),reactExports.useEffect(()=>{if(x!==void 0){if(x===!1){I();return}D()}},[x]);function p(C){if(!C||!x)return;C.firstElementChild||C.appendChild(x);const{width:_,height:i}=y;x.setAttribute("width",`${_}`),x.setAttribute("height",`${i}`)}const{width:u,height:b}=y;return React.createElement("div",{className:`${e}__svg`,ref:C=>p(C),style:{display:"block",backgroundColor:"white",overflow:"hidden",width:u,height:b,userSelect:"none"}})}function isTextItem(T){return"str"in T}function TextLayer(){const T=usePageContext();invariant(T);const{customTextRenderer:e,onGetTextError:B,onGetTextSuccess:n,onRenderTextLayerError:d,onRenderTextLayerSuccess:H,page:W,pageIndex:ht,pageNumber:P,rotate:x,scale:f}=T;invariant(W);const[D,I]=useResolver(),{value:y,error:m}=D,E=reactExports.useRef(null),p=reactExports.useRef();warning(parseInt(window.getComputedStyle(document.body).getPropertyValue("--react-pdf-text-layer"),10)===1,"TextLayer styles not found. Read more: https://github.com/wojtekmaj/react-pdf#support-for-text-layer");function u(){y&&n&&n(y)}function b(){m&&(warning(!1,m.toString()),B&&B(m))}function C(){I({type:"RESET"})}reactExports.useEffect(C,[W,I]);function _(){if(!W)return;const r=makeCancellablePromise(W.getTextContent()),v=r;return r.promise.then(k=>{I({type:"RESOLVE",value:k})}).catch(k=>{I({type:"REJECT",error:k})}),()=>cancelRunningTask(v)}reactExports.useEffect(_,[W,I]),reactExports.useEffect(()=>{if(y!==void 0){if(y===!1){b();return}u()}},[y]);const i=reactExports.useCallback(()=>{H&&H()},[H]),c=reactExports.useCallback(r=>{warning(!1,r.toString()),d&&d(r)},[d]);function o(){const r=p.current;r&&r.classList.add("active")}function l(){const r=p.current;r&&r.classList.remove("active")}const h=reactExports.useMemo(()=>W.getViewport({scale:f,rotation:x}),[W,x,f]);function F(){if(!W||!y)return;const{current:r}=E;if(!r)return;r.innerHTML="";const v=W.streamTextContent({includeMarkedContent:!0}),k={container:r,textContentSource:v,viewport:h},Z=pdfjs.renderTextLayer(k),$=Z;return Z.promise.then(()=>{const q=document.createElement("div");q.className="endOfContent",r.append(q),p.current=q;const G=r.querySelectorAll('[role="presentation"]');if(e){let it=0;y.items.forEach((R,V)=>{if(!isTextItem(R))return;const et=G[it];if(!et)return;const S=e(Object.assign({pageIndex:ht,pageNumber:P,itemIndex:V},R));et.innerHTML=S,it+=R.str&&R.hasEOL?2:1})}i()}).catch(c),()=>cancelRunningTask($)}return reactExports.useLayoutEffect(F,[e,c,i,W,ht,P,y,h]),React.createElement("div",{className:clsx("react-pdf__Page__textContent","textLayer"),onMouseUp:l,onMouseDown:o,ref:E})}function AnnotationLayer(){const T=useDocumentContext(),e=usePageContext();invariant(e);const B=Object.assign(Object.assign({},T),e),{imageResourcesPath:n,linkService:d,onGetAnnotationsError:H,onGetAnnotationsSuccess:W,onRenderAnnotationLayerError:ht,onRenderAnnotationLayerSuccess:P,page:x,pdf:f,renderForms:D,rotate:I,scale:y=1}=B;invariant(f),invariant(x),invariant(d);const[m,E]=useResolver(),{value:p,error:u}=m,b=reactExports.useRef(null);warning(parseInt(window.getComputedStyle(document.body).getPropertyValue("--react-pdf-annotation-layer"),10)===1,"AnnotationLayer styles not found. Read more: https://github.com/wojtekmaj/react-pdf#support-for-annotations");function C(){p&&W&&W(p)}function _(){u&&(warning(!1,u.toString()),H&&H(u))}function i(){E({type:"RESET"})}reactExports.useEffect(i,[E,x]);function c(){if(!x)return;const r=makeCancellablePromise(x.getAnnotations()),v=r;return r.promise.then(k=>{E({type:"RESOLVE",value:k})}).catch(k=>{E({type:"REJECT",error:k})}),()=>{cancelRunningTask(v)}}reactExports.useEffect(c,[E,x,D]),reactExports.useEffect(()=>{if(p!==void 0){if(p===!1){_();return}C()}},[p]);function o(){P&&P()}function l(r){warning(!1,`${r}`),ht&&ht(r)}const h=reactExports.useMemo(()=>x.getViewport({scale:y,rotation:I}),[x,I,y]);function F(){if(!f||!x||!d||!p)return;const{current:r}=b;if(!r)return;const v=h.clone({dontFlip:!0}),k={accessibilityManager:null,annotationCanvasMap:null,div:r,l10n:null,page:x,viewport:v},Z={annotations:p,annotationStorage:f.annotationStorage,div:r,downloadManager:null,imageResourcesPath:n,linkService:d,page:x,renderForms:D,viewport:v};r.innerHTML="";try{new pdfjs.AnnotationLayer(k).render(Z),o()}catch($){l($)}return()=>{}}return reactExports.useEffect(F,[p,n,d,x,D,h]),React.createElement("div",{className:clsx("react-pdf__Page__annotations","annotationLayer"),ref:b})}var __rest=function(T,e){var B={};for(var n in T)Object.prototype.hasOwnProperty.call(T,n)&&e.indexOf(n)<0&&(B[n]=T[n]);if(T!=null&&typeof Object.getOwnPropertySymbols=="function")for(var d=0,n=Object.getOwnPropertySymbols(T);d<n.length;d++)e.indexOf(n[d])<0&&Object.prototype.propertyIsEnumerable.call(T,n[d])&&(B[n[d]]=T[n[d]]);return B};const defaultScale=1,Page=function T(e){const B=useDocumentContext(),n=Object.assign(Object.assign({},B),e),{_className:d="react-pdf__Page",_enableRegisterUnregisterPage:H=!0,canvasBackground:W,canvasRef:ht,children:P,className:x,customRenderer:f,customTextRenderer:D,devicePixelRatio:I,error:y="Failed to load the page.",height:m,inputRef:E,loading:p="Loading page…",noData:u="No page specified.",onGetAnnotationsError:b,onGetAnnotationsSuccess:C,onGetStructTreeError:_,onGetStructTreeSuccess:i,onGetTextError:c,onGetTextSuccess:o,onLoadError:l,onLoadSuccess:h,onRenderAnnotationLayerError:F,onRenderAnnotationLayerSuccess:r,onRenderError:v,onRenderSuccess:k,onRenderTextLayerError:Z,onRenderTextLayerSuccess:$,pageIndex:q,pageNumber:G,pdf:it,registerPage:R,renderAnnotationLayer:V=!0,renderForms:et=!1,renderMode:S="canvas",renderTextLayer:s=!0,rotate:a,scale:g=defaultScale,unregisterPage:L,width:O}=n,N=__rest(n,["_className","_enableRegisterUnregisterPage","canvasBackground","canvasRef","children","className","customRenderer","customTextRenderer","devicePixelRatio","error","height","inputRef","loading","noData","onGetAnnotationsError","onGetAnnotationsSuccess","onGetStructTreeError","onGetStructTreeSuccess","onGetTextError","onGetTextSuccess","onLoadError","onLoadSuccess","onRenderAnnotationLayerError","onRenderAnnotationLayerSuccess","onRenderError","onRenderSuccess","onRenderTextLayerError","onRenderTextLayerSuccess","pageIndex","pageNumber","pdf","registerPage","renderAnnotationLayer","renderForms","renderMode","renderTextLayer","rotate","scale","unregisterPage","width"]),[X,nt]=useResolver(),{value:ct,error:ft}=X,bt=reactExports.useRef(null);invariant(it);const mt=isProvided(G)?G-1:q??null,_t=G??(isProvided(q)?q+1:null),J=a??(ct?ct.rotate:null),Q=reactExports.useMemo(()=>{if(!ct)return null;let Ot=1;const Ht=g??defaultScale;if(O||m){const qt=ct.getViewport({scale:1,rotation:J});O?Ot=O/qt.width:m&&(Ot=m/qt.height)}return Ht*Ot},[m,ct,J,g,O]);function A(){return()=>{isProvided(mt)&&H&&L&&L(mt)}}reactExports.useEffect(A,[H,it,mt,L]);function j(){if(h){if(!ct||!Q)return;h(makePageCallback(ct,Q))}if(H&&R){if(!isProvided(mt)||!bt.current)return;R(mt,bt.current)}}function Y(){ft&&(warning(!1,ft.toString()),l&&l(ft))}function st(){nt({type:"RESET"})}reactExports.useEffect(st,[nt,it,mt]);function pt(){if(!it||!_t)return;const Ot=makeCancellablePromise(it.getPage(_t)),Ht=Ot;return Ot.promise.then(qt=>{nt({type:"RESOLVE",value:qt})}).catch(qt=>{nt({type:"REJECT",error:qt})}),()=>cancelRunningTask(Ht)}reactExports.useEffect(pt,[nt,it,mt,_t,R]),reactExports.useEffect(()=>{if(ct!==void 0){if(ct===!1){Y();return}j()}},[ct,Q]);const Et=reactExports.useMemo(()=>ct&&isProvided(mt)&&_t&&isProvided(J)&&isProvided(Q)?{_className:d,canvasBackground:W,customTextRenderer:D,devicePixelRatio:I,onGetAnnotationsError:b,onGetAnnotationsSuccess:C,onGetStructTreeError:_,onGetStructTreeSuccess:i,onGetTextError:c,onGetTextSuccess:o,onRenderAnnotationLayerError:F,onRenderAnnotationLayerSuccess:r,onRenderError:v,onRenderSuccess:k,onRenderTextLayerError:Z,onRenderTextLayerSuccess:$,page:ct,pageIndex:mt,pageNumber:_t,renderForms:et,renderTextLayer:s,rotate:J,scale:Q}:null,[d,W,D,I,b,C,_,i,c,o,F,r,v,k,Z,$,ct,mt,_t,et,s,J,Q]),At=reactExports.useMemo(()=>makeEventProps(N,()=>ct&&(Q?makePageCallback(ct,Q):void 0)),[N,ct,Q]),tt=`${mt}@${Q}/${J}`,wt=`${mt}/${J}`;function xt(){switch(S){case"custom":return invariant(f),React.createElement(f,{key:`${tt}_custom`});case"none":return null;case"svg":return React.createElement(PageSVG,{key:`${wt}_svg`});case"canvas":default:return React.createElement(PageCanvas,{key:`${tt}_canvas`,canvasRef:ht})}}function Ut(){return s?React.createElement(TextLayer,{key:`${tt}_text`}):null}function jt(){return V?React.createElement(AnnotationLayer,{key:`${tt}_annotations`}):null}function Vt(){return React.createElement(PageContext.Provider,{value:Et},xt(),Ut(),jt(),P)}function kt(){return _t?it===null||ct===void 0||ct===null?React.createElement(Message,{type:"loading"},typeof p=="function"?p():p):it===!1||ct===!1?React.createElement(Message,{type:"error"},typeof y=="function"?y():y):Vt():React.createElement(Message,{type:"no-data"},typeof u=="function"?u():u)}return React.createElement("div",Object.assign({className:clsx(d,x),"data-page-number":_t,ref:mergeRefs(E,bt),style:{"--scale-factor":`${Q}`,backgroundColor:W||"white",position:"relative",minWidth:"min-content",minHeight:"min-content"}},At),kt())},isFunctionOrNode=PropTypes.oneOfType([PropTypes.func,PropTypes.node]);Page.propTypes=Object.assign(Object.assign({},eventProps),{canvasBackground:PropTypes.string,canvasRef:isRef,children:PropTypes.node,className:isClassName,customRenderer:PropTypes.func,customTextRenderer:PropTypes.func,devicePixelRatio:PropTypes.number,error:isFunctionOrNode,height:PropTypes.number,imageResourcesPath:PropTypes.string,inputRef:isRef,loading:isFunctionOrNode,noData:isFunctionOrNode,onGetTextError:PropTypes.func,onGetTextSuccess:PropTypes.func,onLoadError:PropTypes.func,onLoadSuccess:PropTypes.func,onRenderError:PropTypes.func,onRenderSuccess:PropTypes.func,onRenderTextLayerError:PropTypes.func,onRenderTextLayerSuccess:PropTypes.func,pageIndex:isPageIndex,pageNumber:isPageNumber,pdf:isPdf,renderAnnotationLayer:PropTypes.bool,renderForms:PropTypes.bool,renderMode:isRenderMode,renderTextLayer:PropTypes.bool,rotate:isRotate,scale:PropTypes.number,width:PropTypes.number});displayWorkerWarning();pdfjs.GlobalWorkerOptions.workerSrc="pdf.worker.js";pdfjs.GlobalWorkerOptions.workerSrc=`//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;const UniversalPDFViewer=({fileUrl:T,fileName:e="",title:B="PDF Document",className:n="",height:d="100%",showDownload:H=!1,onDownload:W=null,showNativeOptions:ht=!1})=>{const[P,x]=reactExports.useState(!0),[f,D]=reactExports.useState(!1),[I,y]=reactExports.useState(null),[m,E]=reactExports.useState(null),[p,u]=reactExports.useState(1);reactExports.useEffect(()=>{const c=()=>{window.innerWidth<768?y(window.innerWidth-32):y(Math.min(800,window.innerWidth-64))};return c(),window.addEventListener("resize",c),()=>{window.removeEventListener("resize",c)}},[]);const b=({numPages:c})=>{x(!1),D(!1),E(c)},C=c=>{console.error("PDF load error:",c),x(!1),D(!0)},_=()=>{u(c=>Math.max(c-1,1))},i=()=>{u(c=>Math.min(c+1,m||1))};return jsxRuntimeExports.jsxs("div",{className:`universal-pdf-viewer ${n}`,style:{height:d},children:[m>1&&jsxRuntimeExports.jsxs("div",{className:"universal-pdf-viewer__controls",children:[jsxRuntimeExports.jsx("button",{className:"universal-pdf-viewer__nav-btn",onClick:_,disabled:p<=1,children:jsxRuntimeExports.jsx(FaChevronLeft,{})}),jsxRuntimeExports.jsxs("span",{className:"universal-pdf-viewer__page-info",children:["Page ",p," of ",m]}),jsxRuntimeExports.jsx("button",{className:"universal-pdf-viewer__nav-btn",onClick:i,disabled:p>=m,children:jsxRuntimeExports.jsx(FaChevronRight,{})})]}),jsxRuntimeExports.jsxs("div",{className:"universal-pdf-viewer__document",children:[P&&jsxRuntimeExports.jsxs("div",{className:"universal-pdf-viewer__loading",children:[jsxRuntimeExports.jsx("div",{className:"universal-pdf-viewer__spinner"}),jsxRuntimeExports.jsx("p",{children:"Loading PDF..."})]}),f?jsxRuntimeExports.jsxs("div",{className:"universal-pdf-viewer__error",children:[jsxRuntimeExports.jsx(FaExclamationTriangle,{className:"universal-pdf-viewer__warning-icon"}),jsxRuntimeExports.jsx("h3",{children:"PDF Preview Not Available"}),jsxRuntimeExports.jsx("p",{children:"Unable to load the PDF preview"})]}):jsxRuntimeExports.jsx(Document,{file:T,onLoadSuccess:b,onLoadError:C,loading:"",error:"",children:jsxRuntimeExports.jsx(Page,{pageNumber:p,width:I,renderTextLayer:!0,renderAnnotationLayer:!0})})]})]})},WordDocumentViewer=({fileUrl:T,fileName:e="",title:B="Word Document",className:n="",height:d="400px",showDownload:H=!1,onDownload:W=null})=>{const[ht,P]=reactExports.useState(!0),[x,f]=reactExports.useState(!1),[D,I]=reactExports.useState(null),[y,m]=reactExports.useState(""),E=()=>e||(T?T.split("/").pop():"document.docx");reactExports.useEffect(()=>{(async()=>{if(!T){f(!0),m("No file URL provided"),P(!1);return}try{P(!0),f(!1);const b=E();if(console.log(`[WordViewer] Loading preview for: ${b}`),T.endsWith(".json")){console.log(`[WordViewer] Loading JSON preview from: ${T}`);const C=await fetch(T);if(!C.ok)throw new Error(`Failed to load preview: ${C.status}`);const _=await C.json();if(_.success)I(_);else throw new Error(_.error||"Invalid preview data")}else{const C=await fetch(`${API_BASE_URL}/document-preview/convert`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify({fileUrl:T,fileName:b})});if(!C.ok)throw new Error(`Preview generation failed: ${C.status}`);const _=await C.json();if(!_.success)throw new Error(_.message||"Failed to generate preview");I(_.data)}console.log("[WordViewer] Preview loaded successfully"),P(!1)}catch(b){console.error("[WordViewer] Preview loading failed:",b),f(!0),m(b.message),P(!1)}})()},[T,e]);const p=()=>{console.warn("Download functionality has been disabled for security purposes")};return ht?jsxRuntimeExports.jsxs("div",{className:`word-document-viewer ${n}`,style:{height:d},children:[jsxRuntimeExports.jsx("div",{className:"word-document-viewer__header",children:jsxRuntimeExports.jsxs("div",{className:"word-document-viewer__info",children:[jsxRuntimeExports.jsx("span",{className:"word-document-viewer__title",children:B}),jsxRuntimeExports.jsx("span",{className:"word-document-viewer__type",children:"Microsoft Word Document"})]})}),jsxRuntimeExports.jsxs("div",{className:"word-document-viewer__loading",children:[jsxRuntimeExports.jsx(FaSync,{className:"spinning"}),jsxRuntimeExports.jsx("p",{children:"Converting Word document..."}),jsxRuntimeExports.jsx("p",{className:"word-document-viewer__loading-info",children:"This may take a moment for large documents"})]})]}):x?jsxRuntimeExports.jsxs("div",{className:`word-document-viewer ${n}`,style:{height:d},children:[jsxRuntimeExports.jsx("div",{className:"word-document-viewer__header",children:jsxRuntimeExports.jsxs("div",{className:"word-document-viewer__info",children:[jsxRuntimeExports.jsx("span",{className:"word-document-viewer__title",children:B}),jsxRuntimeExports.jsx("span",{className:"word-document-viewer__type",children:"Microsoft Word Document"})]})}),jsxRuntimeExports.jsxs("div",{className:"word-document-viewer__error",children:[jsxRuntimeExports.jsx(FaExclamationTriangle,{}),jsxRuntimeExports.jsx("h3",{children:"Preview Not Available"}),jsxRuntimeExports.jsx("p",{children:"Unable to generate preview for this Word document."}),jsxRuntimeExports.jsx("p",{className:"word-document-viewer__error-details",children:y}),H&&jsxRuntimeExports.jsxs("button",{className:"word-document-viewer__download-button",onClick:p,children:[jsxRuntimeExports.jsx(FaDownload,{}),"Download Word Document"]})]})]}):jsxRuntimeExports.jsx("div",{className:`word-document-viewer ${n}`,style:{height:d},children:jsxRuntimeExports.jsx("div",{className:"word-document-viewer__content",children:D!=null&&D.html?jsxRuntimeExports.jsx("div",{className:"word-document-viewer__html-content",dangerouslySetInnerHTML:{__html:D.html}}):jsxRuntimeExports.jsxs("div",{className:"word-document-viewer__no-content",children:[jsxRuntimeExports.jsx(FaFileWord,{}),jsxRuntimeExports.jsx("p",{children:"Document content could not be displayed"}),jsxRuntimeExports.jsx("p",{children:"Please download the file to view the full content"})]})})})},ExcelDocumentViewer=({fileUrl:T,fileName:e="",title:B="Excel Spreadsheet",className:n="",height:d="400px",showDownload:H=!1,onDownload:W=null})=>{const[ht,P]=reactExports.useState(!0),[x,f]=reactExports.useState(!1),[D,I]=reactExports.useState(null),[y,m]=reactExports.useState(""),[E,p]=reactExports.useState(0),u=()=>e||(T?T.split("/").pop():"document.xlsx");reactExports.useEffect(()=>{(async()=>{if(!T){f(!0),m("No file URL provided"),P(!1);return}try{P(!0),f(!1);const h=u();if(console.log(`[ExcelViewer] Loading preview for: ${h}`),T.endsWith(".json")){console.log(`[ExcelViewer] Loading JSON preview from: ${T}`);const F=await fetch(T);if(!F.ok)throw new Error(`Failed to load preview: ${F.status}`);const r=await F.json();if(r.success)I(r);else throw new Error(r.error||"Invalid preview data")}else{const F=await fetch(`${API_BASE_URL}/document-preview/convert`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify({fileUrl:T,fileName:h})});if(!F.ok)throw new Error(`Preview generation failed: ${F.status}`);const r=await F.json();if(!r.success)throw new Error(r.message||"Failed to generate preview");I(r.data)}console.log("[ExcelViewer] Preview loaded successfully"),P(!1)}catch(h){console.error("[ExcelViewer] Preview loading failed:",h),f(!0),m(h.message),P(!1)}})()},[T,e]);const b=()=>{console.warn("Download functionality has been disabled for security purposes")},C=()=>D!=null&&D.sheets?Object.keys(D.sheets):[],_=()=>{const l=C();if(l.length===0)return null;const h=l[E]||l[0];return D.sheets[h]},i=(l,h,F)=>{const r=l||"",v=h===0;return jsxRuntimeExports.jsx("td",{className:`excel-table__cell ${v?"excel-table__header-cell":""}`,title:r,children:r},F)};if(ht)return jsxRuntimeExports.jsxs("div",{className:`excel-document-viewer ${n}`,style:{height:d},children:[jsxRuntimeExports.jsx("div",{className:"excel-document-viewer__header",children:jsxRuntimeExports.jsxs("div",{className:"excel-document-viewer__info",children:[jsxRuntimeExports.jsx("span",{className:"excel-document-viewer__title",children:B}),jsxRuntimeExports.jsx("span",{className:"excel-document-viewer__type",children:"Microsoft Excel Spreadsheet"})]})}),jsxRuntimeExports.jsxs("div",{className:"excel-document-viewer__loading",children:[jsxRuntimeExports.jsx(FaSync,{className:"spinning"}),jsxRuntimeExports.jsx("p",{children:"Converting Excel spreadsheet..."}),jsxRuntimeExports.jsx("p",{className:"excel-document-viewer__loading-info",children:"Processing sheets and data..."})]})]});if(x)return jsxRuntimeExports.jsxs("div",{className:`excel-document-viewer ${n}`,style:{height:d},children:[jsxRuntimeExports.jsx("div",{className:"excel-document-viewer__header",children:jsxRuntimeExports.jsxs("div",{className:"excel-document-viewer__info",children:[jsxRuntimeExports.jsx("span",{className:"excel-document-viewer__title",children:B}),jsxRuntimeExports.jsx("span",{className:"excel-document-viewer__type",children:"Microsoft Excel Spreadsheet"})]})}),jsxRuntimeExports.jsxs("div",{className:"excel-document-viewer__error",children:[jsxRuntimeExports.jsx(FaExclamationTriangle,{}),jsxRuntimeExports.jsx("h3",{children:"Preview Not Available"}),jsxRuntimeExports.jsx("p",{children:"Unable to generate preview for this Excel spreadsheet."}),jsxRuntimeExports.jsx("p",{className:"excel-document-viewer__error-details",children:y}),H&&jsxRuntimeExports.jsxs("button",{className:"excel-document-viewer__download-button",onClick:b,children:[jsxRuntimeExports.jsx(FaDownload,{}),"Download Excel Spreadsheet"]})]})]});const c=C(),o=_();return jsxRuntimeExports.jsxs("div",{className:`excel-document-viewer ${n}`,style:{height:d},children:[c.length>1&&jsxRuntimeExports.jsx("div",{className:"excel-document-viewer__sheet-tabs",children:c.map((l,h)=>jsxRuntimeExports.jsxs("button",{className:`excel-sheet-tab ${h===E?"excel-sheet-tab--active":""}`,onClick:()=>p(h),title:l,children:[jsxRuntimeExports.jsx(FaTable,{}),l]},h))}),jsxRuntimeExports.jsx("div",{className:"excel-document-viewer__content",children:o&&o.data.length>0?jsxRuntimeExports.jsx("div",{className:"excel-table-container",children:jsxRuntimeExports.jsx("table",{className:"excel-table",children:jsxRuntimeExports.jsx("tbody",{children:o.data.map((l,h)=>jsxRuntimeExports.jsx("tr",{className:"excel-table__row",children:l.map((F,r)=>i(F,h,r))},h))})})}):jsxRuntimeExports.jsxs("div",{className:"excel-document-viewer__no-content",children:[jsxRuntimeExports.jsx(FaFileExcel,{}),jsxRuntimeExports.jsx("p",{children:"No data found in this sheet"}),jsxRuntimeExports.jsx("p",{children:"The sheet may be empty or contain only formatting"})]})})]})},OfficeDocumentViewer=({fileUrl:T,fileName:e="",title:B="Document",documentType:n="word",className:d="",height:H="400px",showDownload:W=!1,onDownload:ht=null})=>{switch(n){case"word":return jsxRuntimeExports.jsx(WordDocumentViewer,{fileUrl:T,fileName:e,title:B,className:d,height:H,showDownload:W,onDownload:ht});case"excel":return jsxRuntimeExports.jsx(ExcelDocumentViewer,{fileUrl:T,fileName:e,title:B,className:d,height:H,showDownload:W,onDownload:ht});default:return jsxRuntimeExports.jsx("div",{className:`office-document-viewer ${d}`,style:{height:H},children:jsxRuntimeExports.jsxs("div",{className:"office-document-viewer__error",children:[jsxRuntimeExports.jsx(FaExclamationTriangle,{}),jsxRuntimeExports.jsx("h3",{children:"Unsupported Document Type"}),jsxRuntimeExports.jsxs("p",{children:['Document type "',n,'" is not supported for preview.']})]})})}},DocumentViewer=({fileUrl:T,fileName:e="",title:B="Document",className:n="",height:d="400px",showDownload:H=!1,onDownload:W=null})=>{const[ht,P]=reactExports.useState("unknown"),[x,f]=reactExports.useState(!0),[D,I]=reactExports.useState(!1),[y,m]=reactExports.useState(!1);reactExports.useEffect(()=>{const i=navigator.userAgent,c=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(i)||window.innerWidth<=768;m(c)},[]);const E=reactExports.useCallback(i=>{if(!i)return"";const c=i.lastIndexOf(".");return c===-1?"":i.substring(c).toLowerCase()},[]),p=reactExports.useCallback(async(i,c=null)=>{var h,F,r,v,k;const o=E(i);if(c&&c.endsWith(".json")){try{const $=await fetch(c);if($.ok){const q=await $.json();if(q.sheets||(h=q.data)!=null&&h.sheets||(F=q.data)!=null&&F.content&&typeof q.data.content=="object")return"excel";if(q.html||(r=q.data)!=null&&r.html||q.text||(v=q.data)!=null&&v.text)return"word";if((k=q.metadata)!=null&&k.documentType)return q.metadata.documentType}}catch($){console.warn("[DocumentViewer] Could not analyze JSON preview:",$)}const Z=c.split("/").pop().toLowerCase();if(Z.includes("xls")||Z.includes("excel")||Z.includes("spreadsheet")||Z.includes("sheet"))return"excel";if(Z.includes("doc")||Z.includes("word"))return"word"}const l=i.toLowerCase();return l.includes("excel")||l.includes("spreadsheet")||l.includes("worksheet")||l.includes("xls")||l.includes("budget")||l.includes("data")||l.includes("report")||l.includes("analysis")||[".xls",".xlsx",".xlsm",".xlt",".xltx",".xltm",".csv"].includes(o)?"excel":[".doc",".docx",".docm",".dot",".dotx",".dotm"].includes(o)?"word":o===".pdf"?"pdf":[".txt",".rtf"].includes(o)?"text":[".odt",".ods",".odp",".odg",".odf"].includes(o)?"opendocument":[".pages",".numbers",".key"].includes(o)?"iwork":[".epub",".mobi",".azw",".azw3"].includes(o)?"ebook":"unknown"},[E]);reactExports.useEffect(()=>{(async()=>{if(!e&&!T){f(!1);return}try{f(!0);const c=await p(e,T);P(c)}catch(c){console.error("[DocumentViewer] Document type detection failed:",c),I(!0)}finally{f(!1)}})()},[e,T,p]);const u=reactExports.useCallback(i=>{switch(i){case"word":return jsxRuntimeExports.jsx(FaFileWord,{className:"document-viewer__icon document-viewer__icon--word"});case"excel":return jsxRuntimeExports.jsx(FaFileExcel,{className:"document-viewer__icon document-viewer__icon--excel"});case"pdf":return jsxRuntimeExports.jsx(FaFilePdf,{className:"document-viewer__icon document-viewer__icon--pdf"});case"text":case"opendocument":case"iwork":case"ebook":return jsxRuntimeExports.jsx(FaFileAlt,{className:"document-viewer__icon document-viewer__icon--text"});default:return jsxRuntimeExports.jsx(FaFile,{className:"document-viewer__icon document-viewer__icon--default"})}},[]),b=reactExports.useCallback(i=>{switch(i){case"word":return"Microsoft Word Document";case"excel":return"Microsoft Excel Spreadsheet";case"pdf":return"PDF Document";case"text":return"Text Document";case"opendocument":return"OpenDocument Format";case"iwork":return"Apple iWork Document";case"ebook":return"E-book";default:return"Document"}},[]),C=reactExports.useCallback(()=>{if(!T)return;const i=document.createElement("a");i.href=T,i.target="_blank",i.rel="noopener noreferrer",e&&(i.download=e),document.body.appendChild(i),i.click(),document.body.removeChild(i)},[T,e]),_=()=>{switch(ht){case"pdf":return jsxRuntimeExports.jsx(UniversalPDFViewer,{fileUrl:T,fileName:e,title:B,className:n,height:d,showDownload:H,onDownload:W,showNativeOptions:!1});case"word":case"excel":return jsxRuntimeExports.jsx(OfficeDocumentViewer,{fileUrl:T,fileName:e,title:B,className:n,height:d,showDownload:H,onDownload:W});default:return jsxRuntimeExports.jsxs("div",{className:"document-viewer__unsupported",children:[jsxRuntimeExports.jsx("div",{className:"document-viewer__icon-wrapper",children:u(ht)}),jsxRuntimeExports.jsx("h3",{children:b(ht)}),jsxRuntimeExports.jsx("p",{children:"Preview not available for this document type"}),(H||W)&&jsxRuntimeExports.jsxs("button",{className:"document-viewer__download-btn",onClick:C,children:[jsxRuntimeExports.jsx(FaDownload,{}),"Download"]})]})}};return jsxRuntimeExports.jsx("div",{className:`document-viewer ${n}`,style:{height:d},children:x?jsxRuntimeExports.jsxs("div",{className:"document-viewer__loading",children:[jsxRuntimeExports.jsx("div",{className:"document-viewer__spinner"}),jsxRuntimeExports.jsx("p",{children:"Loading document..."})]}):D?jsxRuntimeExports.jsxs("div",{className:"document-viewer__error",children:[jsxRuntimeExports.jsx(FaExclamationTriangle,{className:"document-viewer__error-icon"}),jsxRuntimeExports.jsx("h3",{children:"Error Loading Document"}),jsxRuntimeExports.jsx("p",{children:"Unable to load the document preview"}),(H||W)&&jsxRuntimeExports.jsxs("button",{className:"document-viewer__download-btn",onClick:C,children:[jsxRuntimeExports.jsx(FaDownload,{}),"Download"]})]}):_()})};export{DocumentViewer as D};

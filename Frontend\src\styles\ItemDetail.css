/* ItemDetail Component Styles */
.ItemDetail {
  padding: var(--heading5) 0;
  background-color: var(--white);
}

.ItemDetail__container {
  padding: 0 var(--basefont);
}

.ItemDetail__content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--heading4);
  margin-bottom: var(--heading3);
  border-bottom: 1px solid var(--light-gray);
  padding-bottom: 3rem;
}

/* Main Content Styles */
.ItemDetail__mainContent {
  display: flex;
  flex-direction: column;
  gap: var(--heading5);
  max-width: 100%;
  overflow: auto;
}
.ItemDetail__pendingButton {
  background: linear-gradient(to bottom, var(--btn-color), #ec1d3b);
  color: var(--white);
  padding: 14px 28px;
  border-radius: var(--border-radius-medium);
  font-weight: 600;
  font-size: var(--basefont);
  text-align: center;
  box-shadow: 0 4px 8px rgba(238, 52, 37, 0.3);
  border: none !important;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  transition: all 0.3s ease;
}
.ItemDetail__title {
  font-size: var(--heading4);
  font-weight: 600;
  color: var(--secondary-color);
  line-height: 1.3;
  margin: 0;
}

.ItemDetail__coach {
  font-size: var(--basefont);
  color: var(--btn-color);
  margin: 0;
}

.ItemDetail__imageContainer {
  width: 100%;
  border-radius: var(--border-radius-large);
  overflow: hidden;
}

/* Content Preview Styles */
.ItemDetail__contentPreview {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--light-gray);
}

.ItemDetail__previewTitle {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);
  margin: 0;
  padding: var(--basefont);
  background-color: var(--bg-gray);
  border-bottom: 1px solid var(--light-gray);
}

/* Video Preview Styles */
.ItemDetail__videoWrapper {
  position: relative;
  width: 100%;
  height: 400px;
  background-color: var(--black);
}

.ItemDetail__videoPreview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ItemDetail__videoOverlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: var(--heading5) var(--basefont) var(--basefont);
}

.ItemDetail__videoTitle {
  color: var(--white);
  font-size: var(--heading6);
  font-weight: 600;
  margin: 0;
  text-align: center;
}

/* Document Preview Styles */
.ItemDetail__documentWrapper {
  position: relative;
  width: 100%;
  min-height: 400px;
  overflow: visible;
}

.ItemDetail__documentPreview {
  position: relative;
  width: 100%;
  height: 400px;
  overflow: visible;
}

.ItemDetail__pdfPreview {
  width: 100%;
  height: 100%;
  border: none;
  background-color: var(--white);
  -webkit-overflow-scrolling: touch;
  pointer-events: auto;
  touch-action: auto;
}

.ItemDetail__pdfFallback {
  position: absolute;
  bottom: var(--basefont);
  right: var(--basefont);
  background-color: rgba(0, 0, 0, 0.8);
  color: var(--white);
  padding: var(--smallfont) var(--basefont);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
}

/* Preview Button Styles */
.ItemDetail__preview-controls {
  display: flex;
  justify-content: center;
  padding: var(--basefont);
  border-top: 1px solid var(--light-gray);
  background: var(--white);
}

.ItemDetail__preview-btn {
  background: var(--btn-color);
  color: var(--white);
  border: none;
  padding: var(--smallfont) var(--basefont);
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: var(--smallfont);
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
}

.ItemDetail__preview-btn:hover {
  background: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.ItemDetail__pdfLink {
  color: var(--btn-color);
  text-decoration: none;
  margin-left: var(--extrasmallfont);
}

.ItemDetail__pdfLink:hover {
  text-decoration: underline;
}

/* No Preview Styles */
.ItemDetail__noPreview {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  background-color: var(--bg-gray);
  border: 2px dashed var(--light-gray);
  border-radius: var(--border-radius);
}

.ItemDetail__noPreview p {
  font-size: var(--basefont);
  color: var(--dark-gray);
  text-align: center;
  margin: 0;
  font-style: italic;
}

.ItemDetail__image {
  width: 100%;
  height: auto;
  max-height: 400px;
  object-fit: cover;
  display: block;
}

/* Tabs Styles */
.ItemDetail__tabs {
  display: flex;
  flex-direction: column;

  max-width: 100%;
}

.ItemDetail__tabButtons {
  display: flex;
  gap: 10px;
  border-bottom: 1px solid var(--light-gray);
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
}

.ItemDetail__tabButtons::-webkit-scrollbar {
  display: none;
}

.ItemDetail__tabButton {
  background: none;
  border: none;
  padding: var(--basefont) var(--heading5);
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--dark-gray);
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
  position: relative;
  white-space: nowrap;
}

.ItemDetail__tabButton:hover {
  color: var(--btn-color);
}

.ItemDetail__tabButton--active {
  color: var(--btn-color);
  border-bottom-color: var(--btn-color);
}

.ItemDetail__tabContent {
  padding: var(--heading6);
  max-width: 100%;
  overflow: auto;
}

.ItemDetail__tabPanel {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.ItemDetail__description {
  font-size: var(--basefont);
  color: var(--text-color);
  line-height: 1.6;
  margin: 0;
}
.seemoreContainer {
  display: flex;
  align-items: center;

  border-bottom: 1px solid red;
  width: fit-content;
}
.seemoreContainer svg {
  color: red;
}
.ItemDetail__seeMore {
  color: var(--btn-color);
  font-size: var(--basefont);
  font-weight: 500;
  text-decoration: none;
  align-self: flex-start;
  transition: color 0.3s ease;
  appearance: none;
  border: none;
  background-color: transparent;
  display: flex;
}

/* .ItemDetail__seeMore:hover {
  color: var(--primary-color);
} */

/* FAQ Accordion Styles */
.ItemDetail .faq-accordion {
  display: flex;
  flex-direction: column;
  gap: 0;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.ItemDetail .faq-item {
  border-bottom: 1px solid #e5e7eb;
}

.ItemDetail .faq-item:last-child {
  border-bottom: none;
}

.ItemDetail .faq-question {
  width: 100%;
  background: none;
  border: none;
  padding: 20px 24px;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.5;
}

.ItemDetail .faq-question:hover {
  background-color: #f9fafb;
}

.ItemDetail .faq-question-text {
  color: black;
  flex: 1;
  margin-right: 16px;
}
.ItemDetail .faq-question--active {
  background-color: #f9fafb;
}

.ItemDetail .faq-question--active .faq-question-text {
  color: #ef4444;
  font-weight: 600;
}

.ItemDetail .faq-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  color: black;
  font-size: 20px;
  transition: all 0.3s ease;
}

.ItemDetail .faq-question--active .faq-icon {
  color: #ef4444;
}

.ItemDetail .faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  background-color: #ffffff;
}

.ItemDetail .faq-answer--open {
  max-height: 200px;
  border-top: 1px solid #e5e7eb;
}

.ItemDetail .faq-answer-content {
  padding: 20px 24px;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.6;
}

/* Sidebar Styles */
.ItemDetail__sidebar {
  display: flex;
  flex-direction: column;
  gap: var(--heading5);
  /* height: fit-content; */
  /* position: sticky; */
  top: var(--heading5);
}

.ItemDetail__priceBox {
  background-color: var(--primary-light-color);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: var(--heading5);
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
  box-shadow: var(--box-shadow-light);
}

.ItemDetail__price {
  font-size: var(--heading6);
  font-weight: 700;
  color: var(--secondary-color);
  text-align: center;
  margin-bottom: var(--basefont);
  display: flex;
  gap: 10px;
  justify-content: center;
  gap: 5px;
}
.ItemDetail__price p {
  margin-bottom: 0px;
}
/* .ItemDetail__buyButton {
  width: 100%;
} */

.ItemDetail__Request_Custom_Training {
  width: 100%;
  padding: 14px 28px;
  border-radius: var(--border-radius-medium);
  background-color: transparent;
  color: #ee3425;
  border: 1px solid #ee3425;

  border-radius: var(--border-radius-medium);
  font-size: var(--smallfont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.ItemDetail__contentIncludes,
.ItemDetail__contentInfo {
  background-color: var(--white);
  max-width: 100%;
  overflow: auto;
  padding: var(--heading5);
}

.ItemDetail__sidebarTitle {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);
  margin: 0 0 var(--basefont) 0;
  border-bottom: 1px solid var(--light-gray);
  padding-bottom: var(--smallfont);
}

.ItemDetail__includesList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

/* Strategic Content Styles */
.ItemDetail__strategicContent {
  font-size: var(--smallfont);
  color: var(--text-color);
  line-height: 1.6;
}

.ItemDetail__strategicContent ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.ItemDetail__strategicContent li {
  position: relative;
  padding-left: var(--heading5);
  margin-bottom: var(--extrasmallfont);
}

.ItemDetail__strategicContent li::before {
  content: "•";
  color: var(--btn-color);
  font-weight: bold;
  position: absolute;
  left: 0;
  top: 0;
}

.ItemDetail__strategicContent p {
  margin: 0 0 var(--extrasmallfont) 0;
}

.ItemDetail__strategicContent strong {
  color: var(--secondary-color);
  font-weight: 600;
}

.ItemDetail__noContent {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-style: italic;
  margin: 0;
}

.ItemDetail__includesItem {
  font-size: var(--smallfont);
  color: var(--text-color);
  position: relative;
  padding-left: var(--heading5);
}

.ItemDetail__includesItem::before {
  content: "•";
  color: var(--btn-color);
  font-weight: bold;
  position: absolute;
  left: 0;
  top: 0;
}

.ItemDetail__infoList {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.ItemDetail__infoItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--smallfont) 0;
  border-bottom: 1px solid var(--bg-gray);
}

.ItemDetail__infoItem:last-child {
  border-bottom: none;
}

.ItemDetail__infoLabel {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-weight: 500;
}

.ItemDetail__infoValue {
  font-size: var(--smallfont);
  color: var(--text-color);
  font-weight: 600;
}

/* Related Section Styles */
.ItemDetail__relatedSection {
  padding: var(--heading3) 0;
}

.ItemDetail__relatedTitle {
  font-size: var(--heading3);
  font-weight: 600;
  color: var(--secondary-color);
  text-align: left;
  margin: 0 0 var(--basefont) 0;
}

.ItemDetail__learnMoreLink {
  display: block;
  text-align: center;
  color: var(--btn-color);
  font-size: var(--basefont);
  font-weight: 500;
  text-decoration: none;
  margin-bottom: var(--heading4);
  transition: color 0.3s ease;
  text-decoration: underline;
}

.ItemDetail__learnMoreLink:hover {
  color: var(--primary-color);
}

.ItemDetail__relatedGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(270px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.ItemDetail__relatedGrid .loading-message,
.ItemDetail__relatedGrid .no-content-message {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px;
  background: var(--light-gray);
  border-radius: var(--border-radius);
  color: var(--dark-gray);
  font-size: var(--basefont);
}

.ItemDetail__relatedGrid .loading-message {
  background: var(--white);
  border: 1px solid var(--light-gray);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .ItemDetail__content {
    grid-template-columns: 1fr;
    gap: var(--heading5);
  }

  .ItemDetail__sidebar {
    /* position: static; */

    display: grid;
    grid-template-columns: 1fr 1fr;
  }
  .ItemDetail__priceBox {
    display: flex;

    justify-content: space-between;
  }
  .ItemDetail__contentIncludes {
    order: 3;
    grid-column: 1 / -1;
  }
  .ItemDetail__price {
    display: flex;
    gap: 10px;
    align-items: center;

    margin-bottom: 0px;
  }
  .ItemDetail__relatedGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .ItemDetail__title {
    font-size: var(--heading5);
  }

  .ItemDetail__tabButton {
    padding: var(--smallfont) var(--basefont);
    font-size: var(--smallfont);
  }

  .ItemDetail__relatedGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .ItemDetail__price {
    font-size: var(--heading6);
  }

  /* Content Preview Responsive */
  .ItemDetail__videoWrapper {
    height: 250px;
  }

  .ItemDetail__documentPreview {
    height: 350px;
    /* Better tablet document preview */
    min-height: 350px;
    max-height: 70vh;
    overflow: visible;
    position: relative;
  }

  /* Ensure iframe elements work properly on tablet */
  .ItemDetail__documentPreview iframe,
  .ItemDetail__documentPreview .simple-pdf-viewer__iframe,
  .ItemDetail__documentPreview .office-document-viewer__iframe {
    pointer-events: auto !important;
    touch-action: pan-x pan-y zoom !important;
    -webkit-overflow-scrolling: touch !important;
    min-height: 350px;
  }

  .ItemDetail__previewTitle {
    font-size: var(--basefont);
    padding: var(--smallfont) var(--basefont);
  }

  .ItemDetail__videoTitle {
    font-size: var(--basefont);
  }
}
@media (max-width: 600px) {
  .ItemDetail__sidebar {
    display: flex;
    flex-direction: column;
    gap: var(--heading5);
    /* height: fit-content; */
    /* position: sticky; */
    top: var(--heading5);
  }
}
@media (max-width: 480px) {
  .ItemDetail__container {
    padding: 0 var(--smallfont);
  }

  .ItemDetail__content {
    gap: var(--basefont);
  }

  .ItemDetail__mainContent {
    gap: var(--basefont);
  }

  .ItemDetail__sidebar {
    gap: var(--basefont);
  }
  .ItemDetail__priceBox {
    display: grid;
    justify-content: center;
    width: 100%;
    align-items: center;
  }
  .ItemDetail__priceBox,
  .ItemDetail__contentIncludes,
  .ItemDetail__contentInfo {
    padding: var(--basefont);
  }

  .ItemDetail__relatedGrid {
    grid-template-columns: 1fr;
    gap: var(--basefont);
  }

  .ItemDetail__tabButtons {
    justify-content: center;
    min-width: fit-content;
  }

  .ItemDetail__tabButton {
    flex: 1;
    text-align: center;
    min-width: fit-content;
    padding: var(--basefont) 0px;
  }
  .ItemDetail__relatedTitle {
    font-size: var(--heading5);
  }
  .ItemDetail__relatedTitle h2 {
    text-align: left;
  }

  /* Content Preview Mobile */
  .ItemDetail__videoWrapper {
    height: 200px;
  }

  .ItemDetail__documentPreview {
    height: 300px;
    /* Better mobile document preview */
    min-height: 300px;
    max-height: 60vh;
    overflow: visible;
    position: relative;
  }

  /* Ensure iframe elements work properly on mobile */
  .ItemDetail__documentPreview iframe,
  .ItemDetail__documentPreview .simple-pdf-viewer__iframe,
  .ItemDetail__documentPreview .office-document-viewer__iframe {
    pointer-events: auto !important;
    touch-action: pan-x pan-y zoom !important;
    -webkit-overflow-scrolling: touch !important;
    min-height: 300px;
  }

  .ItemDetail__previewTitle {
    font-size: var(--smallfont);
    padding: var(--extrasmallfont) var(--smallfont);
  }

  /* Preview button responsive styles */
  .ItemDetail__preview-btn {
    padding: var(--extrasmallfont) var(--smallfont);
    font-size: var(--extrasmallfont);
    min-height: 44px;
    gap: var(--extrasmallfont);
  }

  .ItemDetail__preview-controls {
    padding: var(--smallfont);
  }

  .ItemDetail__videoOverlay {
    padding: var(--basefont) var(--smallfont) var(--smallfont);
  }

  .ItemDetail__videoTitle {
    font-size: var(--smallfont);
  }

  .ItemDetail__pdfFallback {
    bottom: var(--smallfont);
    right: var(--smallfont);
    padding: var(--extrasmallfont) var(--smallfont);
    font-size: var(--extrasmallfont);
  }

  /* FAQ Responsive Styles */
  .ItemDetail.faq-question {
    padding: 16px 20px;
    font-size: 14px;
  }

  .ItemDetail.faq-question-text {
    margin-right: 12px;
  }

  .ItemDetail.faq-icon {
    width: 20px;
    height: 20px;
    font-size: 18px;
  }

  .ItemDetail .faq-answer-content {
    padding: 16px 20px;
    font-size: 13px;
  }
}

.loading-message,
.no-content-message {
  grid-column: 1 / -1;
  text-align: center;
  padding: 2rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  color: #6c757d;
  font-size: 1rem;
}

.loading-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.content-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.content-tags .tag {
  background-color: var(--light-gray);
  color: var(--text-color);
  padding: 4px 12px;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.content-tags .tag:hover {
  background-color: var(--primary-light-color);
  color: var(--btn-color);
}

/* Reviews Panel Styles */

.reviews-summary {
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--light-gray);
  display: flex;
  justify-content: space-between;
}

.reviews-summary h3 {
  font-size: 1.5rem;
  color: var(--text-color);
}

.average-rating {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-direction: row-reverse;
}

.rating-stars {
  display: flex;
  gap: 0.25rem;
}

.star-filled {
  color: #ffc107;
}

.star-empty {
  color: #e0e0e0;
}

.rating-number {
  font-size: 1rem;
  font-weight: 500;
}

.total-reviews {
  color: var(--dark-gray);
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.review-item {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  display: grid;
  grid-template-columns: 1fr 1fr;
}

.review-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  justify-content: flex-start;
  flex-direction: row-reverse;
}

.ItemDetail .review-rating {
  display: flex;
  gap: 0.25rem;
  color: #ffc107;
}

.review-title {
  font-weight: 500;
  font-size: 1.1rem;
}

.review-date {
  color: var(--dark-gray);
  font-size: 0.9rem;
}

.review-text {
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

.review-author {
  color: var(--dark-gray);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.verified-badge {
  background: #28a745;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
}

.loading-reviews {
  text-align: center;
  padding: 2rem;
  color: var(--dark-gray);
}

.review-error {
  text-align: center;
  padding: 2rem;
  color: var(--error-color);
}

.no-reviews {
  text-align: center;
  padding: 2rem;
  color: var(--dark-gray);
  font-style: italic;
}

.ItemDetail__buyButton.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
@media (max-width: 768px) {
  .reviews-summary {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--light-gray);
    display: grid;
    justify-content: space-between;
  }
  .review-item {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  .reviews-summary h3 {
    margin-bottom: 10px;
  }
  .review-header {
    justify-content: flex-end;
  }
}
@media (max-width: 480px) {
  .reviews-summary h3 {
    font-size: 1rem;
  }
  .average-rating {
    display: grid;
    align-items: center;
    gap: 5px;
    grid-template-columns: 1fr 1fr;
  }
  .review-header {
    display: flex;
    align-items: flex-start;
    gap: 5px;
    justify-content: flex-start;
    flex-direction: column-reverse;
  }
}
@media (max-width: 300px) {
  .average-rating {
    display: grid;
    align-items: center;
    gap: 5px;
    grid-template-columns: 1fr;
  }
  /* .ItemDetail__tabContent{
      padding: 0px;
    } */
  .review-item {
    padding: 10px;
    border-radius: 8px;
    display: grid;
    grid-template-columns: 1fr;
  }
}

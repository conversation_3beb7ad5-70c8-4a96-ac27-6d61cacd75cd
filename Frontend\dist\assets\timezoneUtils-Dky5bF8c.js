const a=e=>{try{return Intl.DateTimeFormat(void 0,{timeZone:e}),!0}catch(r){return console.warn(`Invalid timezone: ${e}`,r),!1}},n=e=>{try{return Intl.DateTimeFormat().resolvedOptions().timeZone}catch(r){return console.warn("Could not get browser timezone, falling back to UTC",r),"UTC"}},s=e=>{if(!e)return null;try{const r=new Date(e);return isNaN(r.getTime())?(console.error("Invalid date provided to toUTC:",e),null):new Date(r.toISOString())}catch(r){return console.error("Error converting to UTC:",r),null}},c=e=>{if(!e)return null;try{const r=new Date(e);return isNaN(r.getTime())?(console.error("Invalid date provided to toLocal:",e),null):new Date(r)}catch(r){return console.error("Error converting to local:",r),null}},l=e=>{if(!e)return"";try{const r=new Date(e);return isNaN(r.getTime())?(console.error("Invalid date provided to formatForDateTimeLocal:",e),""):r.toLocaleDateString("en-CA")+"T"+r.toLocaleTimeString("en-US",{hour12:!1,hour:"2-digit",minute:"2-digit"})}catch(r){return console.error("Error formatting for datetime-local:",r),""}},m=(e,r={})=>{if(!e)return"N/A";try{const t=new Date(e);if(isNaN(t.getTime()))return console.error("Invalid date provided to formatWithTimezone:",e),"Invalid Date";const o={year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",timeZoneName:"short"},i=n();return t.toLocaleString("en-US",{...o,...r,timeZone:i})}catch(t){return console.error("Error formatting with timezone:",t),"Invalid Date"}},u=()=>{try{return n()}catch(e){return console.error("Error getting user timezone:",e),"UTC"}},d=()=>{try{const e=new Date().getTimezoneOffset(),r=Math.abs(Math.floor(e/60)),t=Math.abs(e%60);return`UTC${e<=0?"+":"-"}${String(r).padStart(2,"0")}:${String(t).padStart(2,"0")}`}catch(e){return console.error("Error getting timezone offset:",e),"UTC+00:00"}};export{l as a,s as b,d as c,m as f,u as g,c as t,a as v};

const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/jquery-CyyPaF4j.js","assets/index-BpICMq6M.js","assets/index-BicLFhOs.css","assets/jquery-BQXThELV.js","assets/summernote-lite-CsB-xLcU.js"])))=>i.map(i=>d[i]);
var T=Object.defineProperty;var _=(e,s,t)=>s in e?T(e,s,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[s]=t;var v=(e,s,t)=>_(e,typeof s!="symbol"?s+"":s,t);import{r as c,j as r,_ as I,c3 as D}from"./index-BpICMq6M.js";import{g as z,c as E,v as F}from"./timezoneUtils-Dky5bF8c.js";const A=({value:e="",onChange:s,placeholder:t="Enter text...",height:l=350,className:u="",disabled:i=!1,contentKey:m=""})=>{const p=c.useRef(null),o=c.useRef(null),[f,w]=c.useState(!1),[b,y]=c.useState(e),[h,g]=c.useState(!1),[x,j]=c.useState(!1);return c.useEffect(()=>((async()=>{try{const n=(await I(async()=>{const{default:a}=await import("./jquery-CyyPaF4j.js").then(N=>N.j);return{default:a}},__vite__mapDeps([0,1,2,3]))).default;window.$=n,window.jQuery=n,await I(()=>import("./summernote-lite-CsB-xLcU.js").then(a=>a.s),__vite__mapDeps([4,1,2,3])),p.current&&!o.current&&(n(p.current).summernote({height:l,placeholder:t,toolbar:[["style",["style"]],["font",["bold","underline","clear"]],["fontname",["fontname"]],["para",["ul","ol","paragraph"]],["table",["table"]],["insert",["link","picture","video"]],["view",["fullscreen","codeview"]]],callbacks:{onChange:function(a){g(!0),s==null||s(a),setTimeout(()=>g(!1),1e3)},onFocus:function(){g(!0)},onBlur:function(){setTimeout(()=>g(!1),100)}}}),o.current=n(p.current),w(!0),setTimeout(()=>{const a=b||e||"";a&&!x&&(n(p.current).summernote("code",a),j(!0)),y("")},100),i&&n(p.current).summernote("disable"))}catch(n){console.error("Failed to load Summernote:",n)}})(),()=>{if(o.current)try{o.current.summernote("destroy"),o.current=null}catch(n){console.error("Error destroying Summernote:",n)}w(!1),g(!1),j(!1)}),[m]),c.useEffect(()=>{if(e!=null&&!h)if(f&&o.current&&x){const d=o.current.summernote("code");d!==e&&!d.includes(e)&&!e.includes(d)&&o.current.summernote("code",e)}else f||y(e)},[e,f,h,x]),c.useEffect(()=>{if(f&&o.current&&e&&e.trim()!==""&&!h&&!x){const d=o.current.summernote("code");d!==e&&(d.trim()===""||d==="<p><br></p>")&&(o.current.summernote("code",e),j(!0))}},[f,e,h,x]),c.useEffect(()=>{if(f&&o.current&&!h&&!x){const d=setTimeout(()=>{const n=o.current.summernote("code"),a=e||b;a&&n!==a&&(n.trim()===""||n==="<p><br></p>")&&(o.current.summernote("code",a),j(!0))},1e3);return()=>clearTimeout(d)}},[f,e,b,h,x]),r.jsx("div",{className:`summernote-wrapper ${u}`,children:r.jsx("div",{ref:p})})},$=({progress:e=0,isVisible:s=!1,fileName:t="",uploadType:l="file"})=>s?r.jsx("div",{className:"upload-progress-overlay",children:r.jsxs("div",{className:"upload-progress-container",children:[r.jsxs("div",{className:"upload-progress-header",children:[r.jsxs("h4",{className:"upload-progress-title",children:["Uploading ",l,"..."]}),t&&r.jsx("p",{className:"upload-progress-filename",children:t})]}),r.jsxs("div",{className:"upload-progress-bar-container",children:[r.jsx("div",{className:"upload-progress-bar",children:r.jsx("div",{className:"upload-progress-fill",style:{width:`${e}%`}})}),r.jsxs("span",{className:"upload-progress-percentage",children:[Math.round(e),"%"]})]}),r.jsx("div",{className:"upload-progress-status",children:e<100?r.jsxs("p",{className:"upload-progress-message",children:["Please wait while your ",l," is being uploaded..."]}):r.jsx("p",{className:"upload-progress-message upload-progress-complete",children:"Upload completed! Processing..."})})]})}):null,S={Video:[".mp4",".mov",".avi",".webm"],Document:[".pdf",".doc",".docx"]},V={Video:["video/mp4","video/quicktime","video/x-msvideo","video/webm"],Document:["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document"]},k={Video:500*1024*1024,Document:50*1024*1024},L=e=>{if(!e)return"";const s=e.lastIndexOf(".");return s===-1?"":e.substring(s).toLowerCase()},B=(e,s)=>{if(!e)return{isValid:!1,message:"No file selected"};if(!s)return{isValid:!1,message:"Please select a content type first"};const t=L(e.name),l=S[s],u=V[s],i=k[s];if(e.size>i)return{isValid:!1,message:`File size exceeds the maximum limit of ${i/1048576}MB for ${s.toLowerCase()} files`};if(!l.includes(t)){const m=l.join(", ").toUpperCase();return{isValid:!1,message:`Invalid file format for ${s}. Supported formats: ${m}`}}return u.includes(e.type)?{isValid:!0,message:"File validation successful"}:{isValid:!1,message:`Invalid file type for ${s}. Please select a valid ${s.toLowerCase()} file.`}},C=e=>e?S[e].join(","):"",M=e=>!e,O=()=>{const e=z(),s=E();return r.jsxs("div",{className:"timezone-info",style:{fontSize:"0.8rem",color:"#666",marginTop:"4px"},children:["All times are in your local timezone: ",e," (",s,")"]})};class Y extends D.Component{constructor(t){super(t);v(this,"handleRetry",()=>{this.setState({hasError:!1,errorInfo:null})});this.state={hasError:!1,errorInfo:null,timezoneSupported:!0}}static getDerivedStateFromError(t){var u,i,m;return{hasError:!0,isTimezoneError:((u=t.message)==null?void 0:u.includes("timezone"))||((i=t.message)==null?void 0:i.includes("Intl"))||((m=t.message)==null?void 0:m.includes("locale"))}}componentDidCatch(t,l){var u;console.error("Timezone Error Boundary caught an error:",t,l);try{const i=z(),m=E(),p=F(i);console.log("Timezone Environment:",{userTimezone:i,timezoneOffset:m,timezoneSupported:p,browserSupport:{Intl:typeof Intl<"u",DateTimeFormat:typeof(Intl==null?void 0:Intl.DateTimeFormat)<"u",timeZone:typeof((u=Intl==null?void 0:Intl.DateTimeFormat())==null?void 0:u.resolvedOptions)<"u"}})}catch(i){console.error("Failed to log timezone environment:",i)}this.setState({errorInfo:l,timezoneSupported:this.checkTimezoneSupport()})}checkTimezoneSupport(){try{return typeof Intl>"u"||typeof Intl.DateTimeFormat>"u"?!1:(new Date().toLocaleString("en-US",{timeZone:"America/New_York"}),!0)}catch{return!1}}render(){return this.state.hasError?r.jsxs("div",{className:"timezone-error-boundary",children:[r.jsxs("div",{className:"error-card",children:[r.jsx("h2",{children:"🌍 Timezone Error"}),this.state.timezoneSupported?r.jsxs("div",{className:"timezone-error-details",children:[r.jsx("p",{children:"There was an issue with date/timezone handling."}),r.jsxs("details",{className:"error-details",children:[r.jsx("summary",{children:"Technical Details"}),r.jsxs("div",{className:"timezone-info",children:[r.jsxs("p",{children:[r.jsx("strong",{children:"Your Timezone:"})," ",z()]}),r.jsxs("p",{children:[r.jsx("strong",{children:"UTC Offset:"})," ",E()]}),r.jsxs("p",{children:[r.jsx("strong",{children:"Browser:"})," ",navigator.userAgent]})]}),this.state.errorInfo&&r.jsx("pre",{className:"error-stack",children:this.state.errorInfo.componentStack})]})]}):r.jsxs("div",{className:"browser-support-error",children:[r.jsx("p",{children:"Your browser doesn't fully support timezone functionality."}),r.jsxs("div",{className:"suggestions",children:[r.jsx("h4",{children:"Suggestions:"}),r.jsxs("ul",{children:[r.jsx("li",{children:"Update your browser to the latest version"}),r.jsx("li",{children:"Try using Chrome, Firefox, or Safari"}),r.jsx("li",{children:"Enable JavaScript if it's disabled"})]})]})]}),r.jsxs("div",{className:"error-actions",children:[r.jsx("button",{className:"btn btn-primary",onClick:this.handleRetry,children:"Try Again"}),r.jsx("button",{className:"btn btn-outline",onClick:()=>window.location.reload(),children:"Refresh Page"})]})]}),r.jsx("style",{jsx:!0,children:`
            .timezone-error-boundary {
              display: flex;
              justify-content: center;
              align-items: center;
              min-height: 300px;
              padding: 20px;
            }
            
            .error-card {
              background: #fff;
              border: 1px solid #ddd;
              border-radius: 8px;
              padding: 24px;
              max-width: 600px;
              box-shadow: 0 2px 10px rgba(0,0,0,0.1);
              text-align: center;
            }
            
            .error-card h2 {
              color: #d32f2f;
              margin-bottom: 16px;
            }
            
            .suggestions ul {
              text-align: left;
              margin: 12px 0;
            }
            
            .error-details {
              text-align: left;
              margin: 16px 0;
            }
            
            .timezone-info {
              background: #f5f5f5;
              padding: 12px;
              border-radius: 4px;
              margin: 8px 0;
            }
            
            .error-stack {
              background: #f5f5f5;
              padding: 12px;
              border-radius: 4px;
              overflow-x: auto;
              font-size: 12px;
              margin: 8px 0;
            }
            
            .error-actions {
              margin-top: 20px;
              display: flex;
              gap: 12px;
              justify-content: center;
            }
            
            .btn {
              padding: 8px 16px;
              border-radius: 4px;
              border: none;
              cursor: pointer;
              font-weight: 500;
            }
            
            .btn-primary {
              background: #1976d2;
              color: white;
            }
            
            .btn-outline {
              background: transparent;
              color: #1976d2;
              border: 1px solid #1976d2;
            }
            
            .btn:hover {
              opacity: 0.9;
            }
          `})]}):this.props.children}}export{A as S,Y as T,$ as U,O as a,C as g,M as i,B as v};

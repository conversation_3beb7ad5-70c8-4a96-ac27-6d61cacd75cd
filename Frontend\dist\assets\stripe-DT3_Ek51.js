var c="https://js.stripe.com/v3",m=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/;var S=function(){for(var r=document.querySelectorAll('script[src^="'.concat(c,'"]')),e=0;e<r.length;e++){var t=r[e];if(m.test(t.src))return t}return null},s=function(r){var e="",t=document.createElement("script");t.src="".concat(c).concat(e);var n=document.head||document.body;if(!n)throw new Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return n.appendChild(t),t},w=function(r,e){!r||!r._registerWrapper||r._registerWrapper({name:"stripe-js",version:"4.6.0",startTime:e})},a=null,l=null,d=null,h=function(r){return function(){r(new Error("Failed to load Stripe.js"))}},E=function(r,e){return function(){window.Stripe?r(window.Stripe):e(new Error("Stripe.js not available"))}},L=function(r){return a!==null?a:(a=new Promise(function(e,t){if(typeof window>"u"||typeof document>"u"){e(null);return}if(window.Stripe){e(window.Stripe);return}try{var n=S();if(!(n&&r)){if(!n)n=s(r);else if(n&&d!==null&&l!==null){var o;n.removeEventListener("load",d),n.removeEventListener("error",l),(o=n.parentNode)===null||o===void 0||o.removeChild(n),n=s(r)}}d=E(e,t),l=h(t),n.addEventListener("load",d),n.addEventListener("error",l)}catch(p){t(p);return}}),a.catch(function(e){return a=null,Promise.reject(e)}))},g=function(r,e,t){if(r===null)return null;var n=r.apply(void 0,e);return w(n,t),n},u,v=!1,f=function(){return u||(u=L(null).catch(function(r){return u=null,Promise.reject(r)}),u)};Promise.resolve().then(function(){return f()}).catch(function(i){v||console.warn(i)});var j=function(){for(var r=arguments.length,e=new Array(r),t=0;t<r;t++)e[t]=arguments[t];v=!0;var n=Date.now();return f().then(function(o){return g(o,e,n)})};const P=j("pk_test_51RbzYCH4ZNVw6RDIxjhOqvNVOTAsHRf4pJ3wPfTj0PwuLme0GhpFDE266eqRx0gDLnGItS3Yj1K9rDUyr8jBltvv00rZuxl0GN");export{P as s};
